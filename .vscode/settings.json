{"editor.codeActionsOnSave": {"source.fixAll.prettier": "explicit", "source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.insertSpaces": true, "editor.rulers": [80], "files.associations": {"**/pipelines/**/*.yml": "azure-pipelines"}, "[markdown]": {"editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 80}, "search.exclude": {"**/node_modules": true, "**/libs": true, "**/assets": true, "**/static": true, "**/bower_components": true, "**/build": true, "**/dist": true, "**/test-reports": true, "**/yarn.lock": true}, "svg.preview.background": "white", "workbench.colorCustomizations": {"list.activeSelectionForeground": "#4ec231", "list.inactiveSelectionForeground": "#4ec231", "list.highlightForeground": "#4ec231", "scrollbarSlider.activeBackground": "#4ec231", "editorSuggestWidget.highlightForeground": "#4ec231", "textLink.foreground": "#4ec231", "progressBar.background": "#4ec231", "pickerGroup.foreground": "#4ec231", "tab.unfocusedActiveBorder": "#5e0a44"}, "java.compile.nullAnalysis.mode": "automatic", "cSpell.words": ["bpUS", "USOCPI"]}