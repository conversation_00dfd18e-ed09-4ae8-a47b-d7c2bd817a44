export enum LoginAnalyticsEvent {
  LOGIN_CLICK = 'LoginAnalyticsEvent.LogInClick',
  CREATE_ACCOUNT_CLICK = 'LoginAnalyticsEvent.CreateAccountClick',
  FIND_CHARGER_MAP_BANNER = 'LoginAnalyticsEvent.FindChargerClick',
  CONFIRM_UBER_DRIVER_CLICK = 'LoginAnalyticsEvent.ConfirmUberDriverClick',
}

export enum TabsAnalyticsEvent {
  CHARGE_TAB_ACTIVE_CHARGE_CLICK = 'TabsAnalyticsEvent.ChargeTab_ActiveCharge_Click',
  CHARGE_TAB_NO_CHARGE_CLICK = 'TabsAnalyticsEvent.ChargeTab_NoCharge_Click',
  MAP_TAB_CLICK = 'TabsAnalyticsEvent.MapTab_Click',
  PROFILE_TAB_CLICK = 'TabsAnalyticsEvent.ProfileTab_Click',
  HELP_TAB_CLICK = 'TabsAnalyticsEvent.HelpTab_Click',
}

export enum OutageAnalyticsEvent {
  OUTAGE_FULL_OUTAGE_SCREEN_OPEN = 'OutageAnalyticsEvent.Outage_FullOutageScreen_Open',
  OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_OPEN = 'OutageAnalyticsEvent.Outage_PartialOutageBottomSheet_Open',
  OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_CLOSE = 'OutageAnalyticsEvent.Outage_PartialOutageBottomSheet_Close',
}

export enum HelpPageAnalyticsEvent {
  HELP_SCREEN_FAQS_CLICK = 'HelpPageAnalyticsEvent.Help_HelpScreen_FAQs_Click',
  HELP_SCREEN_PHONE_NUMBER_CLICK = 'HelpPageAnalyticsEvent.Help_HelpScreen_PhoneNumber_Click',
  HELP_SCREEN_EMAIL_CLICK = 'HelpPageAnalyticsEvent.Help_HelpScreen_Email_Click',
  HELP_WEB_FORM_CLICK = 'HelpPageAnalyticsEvent.Help_WebForm_Click',
  HELP_PARTNER_SITE_CLICK = 'HelpPageAnalyticsEvent.Help_PartnerSite_Click',
}

export enum UberAnalyticsEvent {
  WELCOME_TO_UBER_OPEN = 'UberAnalyticsEvent.WelcomeToUber_Open',
  WELCOME_TO_UBER_FIND_NEARBY_CHARGER_CLICK = 'UberAnalyticsEvent.FindNearbyCharger_Click',
  WELCOME_TO_UBER_VIEW_REWARDS_CLICK = 'UberAnalyticsEvent.WelcomeToUber_ViewRewards_Click',

  REAUTH_UBER_LINK_EXPIRED_OPEN = 'UberAnalyticsEvent.ReAuth_UberLinkExpired_Open',
  REAUTH_UBER_LINK_EXPIRED_VERIFY_CLICK = 'UberAnalyticsEvent.ReAuth_UberLinkExpired_Verify_Click',
  REAUTH_UBER_LINK_EXPIRED_X_CLICK = 'UberAnalyticsEvent.ReAuth_UberLinkExpired_X_Click',

  LOGIN_WITH_UBER_SUCCESS = 'UberAnalyticsEvent.LoginWithUber_Success',

  UBER_UNLINK_ACCOUNT_SUCCESS_SUBSCRIBE_CLICK = 'UberAnalyticsEvent.UnlinkAccountsSuccess_Subscribe_Click',
  UBER_UNLINK_ACCOUNT_SUCCESS_FIND_CHARGER_CLICK = 'UberAnalyticsEvent.UnlinkAccountsSuccess_FindCharger_Click',

  UNLINK_ACCOUNTS_MODAL_OPEN = 'UberAnalyticsEvent.UnlinkAccountsModal_Open',
  UNLINK_ACCOUNTS_KEEP_UBER_CLICK = 'UberAnalyticsEvent.UnlinkAccounts_KeepUber_Click',
  UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK = 'UberAnalyticsEvent.UnlinkAccounts_DisconnectAccounts_Click',

  UNLINKING_UNSUCCESSFUL_EVENT = 'UberAnalyticsEvent.UnlinkingUnsuccessful_Event',
  UNLINKING_UNSUCCESSFUL_OPEN = 'UberAnalyticsEvent.UnlinkingUnsuccessful_Open',
  UNLINKING_UNSUCCESSFUL_CONTACT_CLICK = 'UberAnalyticsEvent.UnlinkingUnsuccessful_Contact_Click',
  UNLINKING_UNSUCCESSFUL_TRY_AGAIN_CLICK = 'UberAnalyticsEvent.UnlinkingUnsuccessful_TryAgain_Click',
  UNLINKING_ACCOUNTS_OPEN = 'UberAnalyticsEvent.UnlinkingAccounts_Open',
  UNLINING_ACCOUNTS_SUCCESSFUL_OPEN = 'UberAnalyticsEvent.UnlinkingAccountsSuccessful_Open',

  CONNECTING_ACCOUNTS_SUCCESSFUL_OPEN = 'UberAnalyticsEvent.ConnectingAccountsSuccessful_Open',

  UBER_INELIGIBLE_ACCOUNT_OPEN = 'UberAnalyticsEvent.UberIneligibleAccount_Open',
  UBER_INELIGIBLE_ACCOUNT_RETRYLIMIT_OPEN = 'UberAnalyticsEvent.UberIneligibleAccount_RetryLimit_Open',
  UBER_INELIGIBLE_ACCOUNT_RETRY_SUCCESS = 'UberAnalyticsEvent.UberIneligibleAccount_Retry_Success',
  UBER_INELIGIBLE_ACCOUNT_TRYAGAIN_CLICK = 'UberAnalyticsEvent.UberIneligibleAccount_TryAgain_Click',
  UBER_INELIGIBLE_ACCOUNT_UNLINK_CLICK = 'UberAnalyticsEvent.UberIneligibleAccount_Unlink_Click',
}

export enum SiteBannerAnalyticsEvent {
  CHARGE_SELECT_CONNECTOR_UNENTITLED = 'SiteBannerAnalyticsEvent.Charge_SelectConnector_Unentitled',
  MAP_SITE_UNENTITLED = 'SiteBannerAnalyticsEvent.Map_Site_Unentitled',
}

export enum MigrationFlowAnalyticsEvent {
  SUBS_MIGRATION_SCREEN_OPEN = 'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_Open',
  SUBS_MIGRATIONSCREEN_DISMISS_CLICK = 'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_Dismiss_Click',
  SUBS_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK = 'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_UpdateAccount_Click',
  SUBS_MIGRATIONSCREEN_KEEPSUBS_CLICK = 'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_KeepSubs_Click',
  SUBS_MIGRATIONSCREEN_DECLINESUBS_CLICK = 'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_DeclineSubs_Click',
  SUBS_MIGRATIONSCREEN_FAQ_CLICK = 'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_FAQ_Click',
  SUBS_MIGRATION_ERRORSCREEN_OPEN = 'MigrationFlowAnalyticsEvent.SUBSMigration_ErrorScreen_Open',

  PAYG_MIGRATION_SCREEN_OPEN = 'MigrationFlowAnalyticsEvent.PAYGMigrationScreen_Open',
  PAYG_MIGRATIONSCREEN_DISMISS_CLICK = 'MigrationFlowAnalyticsEvent.PAYGMigrationScreen_Dismiss_Click',
  PAYG_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK = 'MigrationFlowAnalyticsEvent.PAYGMigrationScreen_UpdateAccount_Click',
  PAYG_MIGRATIONSCREEN_ORDER_RFID_CLICK = 'MigrationFlowAnalyticsEvent.PAYGMigrationScreen_OrderRFID_Click',
  PAYG_MIGRATIONSCREEN_MOVETOSUBS_CLICK = 'MigrationFlowAnalyticsEvent.PAYGMigrationScreen_MoveToSUBS_Click',
  PAYG_MIGRATIONSCREEN_FAQ_CLICK = 'MigrationFlowAnalyticsEvent.PAYGMigrationScreen_FAQ_Click',
  PAYG_MIGRATION_ERRORSCREEN_OPEN = 'MigrationFlowAnalyticsEvent.PAYGMigration_ErrorScreen_Open',
}

export enum UberMigrationAnalyticsEvent {
  UBER_MIGRATION_SCREEN_OPEN = 'UberMigrationEvent.UberMigrationScreen_Open',
  UBER_MIGRATION_SCREEN_DISMISS_CLICK = 'UberMigrationEvent.UberMigrationScreen_Dismiss_Click',
  UBER_MIGRATION_SCREEN_UPDATE_ACCOUNT_CLICK = 'UberMigrationEvent.UberMigrationScreen_UpdateAccount_Click',
  UBER_MIGRATION_SCREEN_MOVE_TO_SUBS_CLICK = 'UberMigrationEvent.UberMigrationScreen_MoveToSubs_Click',
}
