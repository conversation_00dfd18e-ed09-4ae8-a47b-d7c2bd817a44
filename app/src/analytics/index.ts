import { HelpPageAnalyticsEventPartnerSiteClickType } from './events/HelpScreen/PartnerSiteClick';
import { HelpPageAnalyticsEventSelectEmailType } from './events/HelpScreen/SelectEmail';
import { HelpPageAnalyticsEventSelectFAQType } from './events/HelpScreen/SelectFAQ';
import { HelpPageAnalyticsEventSelectPhoneNumberType } from './events/HelpScreen/SelectPhoneNumber';
import { HelpPageAnalyticsEventWebFormClickType } from './events/HelpScreen/WebFormClick';
import { LoginAnalyticsEventConfirmUberDriverType } from './events/Login/ConfirmUberDriver';
import { LoginAnalyticsEventCreateAccountType } from './events/Login/CreateAcount';
import { LoginAnalyticsEventFindChargerType } from './events/Login/FindCharger';
import { LoginAnalyticsEventLogInType } from './events/Login/Login';
import {
  PayGMigrationErrorScreenOpenType,
  PayGMigrationScreenDismissClickType,
  PayGMigrationScreenFAQClickType,
  PayGMigrationScreenMoveToSUBSClickType,
  PayGMigrationScreenOpenType,
  PayGMigrationScreenOrderRFIDClickType,
  PayGMigrationScreenUpdateAccountClickType,
} from './events/MigrationFlow/PaygMigrationFlow';
import {
  SUBSMigrationErrorScreenOpenType,
  SUBSMigrationScreenDeclineSubsClickType,
  SUBSMigrationScreenDismissClickType,
  SUBSMigrationScreenFAQClickType,
  SUBSMigrationScreenKeepSUBSClickType,
  SUBSMigrationScreenOpenType,
  SUBSMigrationScreenUpdateAccountClickType,
} from './events/MigrationFlow/SUBSMigrationFlow';
import { OutageAnalyticsEventClosePartialOutageBottomsheetType } from './events/Outage/ClosePartialOutageBottomsheet';
import { OutageAnalyticsEventOpenFullOutageScreenType } from './events/Outage/OpenFullOutageScreen';
import { OutageAnalyticsEventOpenPartialOutageBottomsheetType } from './events/Outage/OpenPartialOutageBottomsheet';
import { ChargeSelectConnectorUnentitledType } from './events/SiteBanner/ChargeSelectConnectorUnentitled';
import { MapSiteUnentitledType } from './events/SiteBanner/MapSiteUnentitled';
import { TabsAnalyticsEventActiveChargeType } from './events/Tabs/ChargeTabActiveCharge';
import { TabsAnalyticsEventNoActiveChargeType } from './events/Tabs/ChargeTabNoActiveCharge';
import { TabsAnalyticsEventOpenHelpTabType } from './events/Tabs/OpenHelpTab';
import { TabsAnalyticsEventOpenMapTabType } from './events/Tabs/OpenMapTab';
import { TabsAnalyticsEventOpenProfileTabType } from './events/Tabs/OpenProfileTab';
import {
  ConnectingAccountsSuccesfulOpenType,
  DisconnectingAccountsOpenType,
  DisconnectingAccountsSuccessfulOpenType,
} from './events/Uber/ConnectingDisconnectingAccounts';
import {
  IneligibleAccountOpenType,
  IneligibleAccountRetryLimitOpenType,
  IneligibleAccountRetryLimitSuccessType,
  IneligibleAccountTryAgainClickType,
  IneligibleAccountUnlinkClickType,
} from './events/Uber/IneligibleAccount';
import { LogInWithUberSuccessType } from './events/Uber/LogInWithUber';
import {
  UberLinkExpiredLogInWithUberClickType,
  UberLinkExpiredOpenType,
  UberLinkExpiredXClickType,
} from './events/Uber/UberLinkExpired';
import {
  UnlinkAccountsDisconnectAccountsClickType,
  UnlinkAccountsKeepUberPerksClickType,
  UnlinkAccountsOpenType,
  UnlinkAccountSuccessFindChargerClickType,
  UnlinkAccountSuccessSubscribeClickType,
} from './events/Uber/UnlinkAccounts';
import {
  UnlinkingUnsuccesfulEventType,
  UnlinkingUnsuccesfulGoBackClickType,
  UnlinkingUnsuccesfulOpenType,
  UnlinkingUnsuccesfulTryAgainClickType,
} from './events/Uber/UnlinkingUnsuccessful';
import {
  WelcomeToUberFindNearbyChargerClickType,
  WelcomeToUberOpenType,
  WelcomeToUberViewRewardsClickType,
} from './events/Uber/WelcomeToUber';
import {
  UberMigrationScreenDismissClickType,
  UberMigrationScreenMoveToSubsClickType,
  UberMigrationScreenOpenType,
  UberMigrationScreenUpdateAccountClickType,
} from './events/UberMigration/MigrationScreen';

export { analyticsEvent } from './analytics';
export { default as AnalyticsMiddleware } from './AnalyticsMiddleware';

export type HelpPageAnalyticsEventType =
  | HelpPageAnalyticsEventSelectFAQType
  | HelpPageAnalyticsEventSelectPhoneNumberType
  | HelpPageAnalyticsEventSelectEmailType
  | HelpPageAnalyticsEventWebFormClickType
  | HelpPageAnalyticsEventPartnerSiteClickType;

export type LoginAnalyticsEventType =
  | LoginAnalyticsEventFindChargerType
  | LoginAnalyticsEventCreateAccountType
  | LoginAnalyticsEventLogInType
  | LoginAnalyticsEventConfirmUberDriverType;

export type OutageAnalyticsEventType =
  | OutageAnalyticsEventOpenFullOutageScreenType
  | OutageAnalyticsEventOpenPartialOutageBottomsheetType
  | OutageAnalyticsEventClosePartialOutageBottomsheetType;

export type TabsAnalyticsEventType =
  | TabsAnalyticsEventActiveChargeType
  | TabsAnalyticsEventNoActiveChargeType
  | TabsAnalyticsEventOpenProfileTabType
  | TabsAnalyticsEventOpenHelpTabType
  | TabsAnalyticsEventOpenMapTabType;

export type UberAnalyticsEventType =
  | WelcomeToUberOpenType
  | WelcomeToUberFindNearbyChargerClickType
  | WelcomeToUberViewRewardsClickType
  | UberLinkExpiredOpenType
  | UberLinkExpiredLogInWithUberClickType
  | UberLinkExpiredXClickType
  | LogInWithUberSuccessType
  | UnlinkAccountSuccessSubscribeClickType
  | UnlinkAccountSuccessFindChargerClickType
  | UnlinkAccountsOpenType
  | UnlinkAccountsKeepUberPerksClickType
  | UnlinkAccountsDisconnectAccountsClickType
  | UnlinkingUnsuccesfulEventType
  | UnlinkingUnsuccesfulOpenType
  | UnlinkingUnsuccesfulGoBackClickType
  | UnlinkingUnsuccesfulTryAgainClickType
  | DisconnectingAccountsOpenType
  | DisconnectingAccountsSuccessfulOpenType
  | ConnectingAccountsSuccesfulOpenType
  | IneligibleAccountOpenType
  | IneligibleAccountRetryLimitOpenType
  | IneligibleAccountRetryLimitSuccessType
  | IneligibleAccountTryAgainClickType
  | IneligibleAccountUnlinkClickType;

export type SiteBannerAnalyticsEventType =
  | ChargeSelectConnectorUnentitledType
  | MapSiteUnentitledType;

export type MigrationFlowAnalyticsEventType =
  | SUBSMigrationScreenOpenType
  | SUBSMigrationScreenDismissClickType
  | SUBSMigrationScreenUpdateAccountClickType
  | SUBSMigrationScreenKeepSUBSClickType
  | SUBSMigrationScreenDeclineSubsClickType
  | SUBSMigrationScreenFAQClickType
  | SUBSMigrationErrorScreenOpenType
  | PayGMigrationScreenOpenType
  | PayGMigrationScreenDismissClickType
  | PayGMigrationScreenUpdateAccountClickType
  | PayGMigrationScreenOrderRFIDClickType
  | PayGMigrationScreenMoveToSUBSClickType
  | PayGMigrationScreenFAQClickType
  | PayGMigrationErrorScreenOpenType;
export type UberMigrationAnalyticsEventType =
  | UberMigrationScreenDismissClickType
  | UberMigrationScreenMoveToSubsClickType
  | UberMigrationScreenOpenType
  | UberMigrationScreenUpdateAccountClickType;
