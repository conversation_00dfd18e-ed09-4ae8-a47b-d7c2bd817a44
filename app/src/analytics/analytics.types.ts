import type {
  HelpPageAnalyticsEventType,
  LoginAnalyticsEventType,
  MigrationFlowAnalyticsEventType,
  OutageAnalyticsEventType,
  SiteBannerAnalyticsEventType,
  TabsAnalyticsEventType,
  UberAnalyticsEventType,
  UberMigrationAnalyticsEventType,
} from '@analytics/index';
import type { WalletAnalyticsEventType } from '@bp/bppay-wallet-feature';
import type { ChargeHistoryAnalyticsEventType } from '@bp/charge-history-mfe';
import type { ChargeAnalyticsEventType } from '@bp/charge-mfe';
import type { CreditAnalyticsEventType } from '@bp/credit-mfe';
import type { FavouritesAnalyticsEventType } from '@bp/favourites-mfe';
import type { GuestAnalyticsEventType } from '@bp/guest_feature-mfe';
import type { MapAnalyticsEventType } from '@bp/map-mfe';
import type { SubsAnalyticsEventType } from '@bp/mfe-subscription';
import type { OffersAnalyticsEventType } from '@bp/offers-mfe';
import type { OnboardingAnalyticsEventType } from '@bp/onboarding-mfe';
import type { PartnerDriverAnalyticsEventType } from '@bp/partnerdriver-mfe/dist/analytics';
import type { ProfileAnalyticsEventType } from '@bp/profile-mfe';
import type { LoginAnalyticsEventType as AuthAnalyticsEventsType } from '@bp/pulse-auth-sdk';
import type { RegAnalyticsEventType } from '@bp/registration-mfe';
import type { RFIDAnalyticsEventType } from '@bp/rfid-mfe';
import type { RTBFAnalyticsEventType } from '@bp/rtbf-mfe';

import type { MigrationFlowAnalyticsEventExtensionKeys } from './migration-flow-enums';
import type { RTBFAnalyticsEventExtensionKeys } from './rtbf-enums';

/**
 * This is a union of all the analytics event types which are exposed by
 * the MFEs
 */
export type AnalyticsEventType =
  | MapAnalyticsEventType
  | HelpPageAnalyticsEventType
  | LoginAnalyticsEventType
  | ChargeHistoryAnalyticsEventType
  | RFIDAnalyticsEventType
  | PartnerDriverAnalyticsEventType
  | ProfileAnalyticsEventType
  | ChargeAnalyticsEventType
  | WalletAnalyticsEventType
  | RTBFAnalyticsEventType
  | GuestAnalyticsEventType
  | CreditAnalyticsEventType
  | SubsAnalyticsEventType
  | OutageAnalyticsEventType
  | TabsAnalyticsEventType
  | RegAnalyticsEventType
  | FavouritesAnalyticsEventType
  | OnboardingAnalyticsEventType
  | AuthAnalyticsEventsType
  | UberAnalyticsEventType
  | SiteBannerAnalyticsEventType
  | OffersAnalyticsEventType
  | MigrationFlowAnalyticsEventType
  | UberMigrationAnalyticsEventType
  // Add extensions
  | { type: RTBFAnalyticsEventExtensionKeys; payload?: any }
  | { type: MigrationFlowAnalyticsEventExtensionKeys; payload?: any };

// Update the AnalyticsEventMap type to work with our new enums
// Use a more flexible type that will accept the enum keys as properties
export type AnalyticsEventMap = {
  [key: string]: undefined | ((payload?: any) => void | Promise<void>);
};
