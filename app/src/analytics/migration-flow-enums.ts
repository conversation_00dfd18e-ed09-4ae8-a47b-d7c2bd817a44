/**
 * Extension of MigrationFlowAnalyticsEvent
 * This file contains additional enum values for MigrationFlowAnalyticsEvent
 * that are missing from the imported package but referenced in the analytics handlers.
 */
export const MigrationFlowAnalyticsEventExtension = {
  SUBSMigrationScreen_Open:
    'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_Open',
  SUBSMigrationScreen_Dismiss_Click:
    'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_Dismiss_Click',
  SUBSMigrationScreen_UpdateAccount_Click:
    'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_UpdateAccount_Click',
  SUBSMigrationScreen_KeepSubs_Click:
    'MigrationFlowAnalyticsEvent.SUBSMigrationScreen_KeepSubs_Click',
} as const;

// Create a type that can be used as a union with MigrationFlowAnalyticsEvent
export type MigrationFlowAnalyticsEventExtensionType =
  typeof MigrationFlowAnalyticsEventExtension;
export type MigrationFlowAnalyticsEventExtensionKeys =
  keyof MigrationFlowAnalyticsEventExtensionType;
