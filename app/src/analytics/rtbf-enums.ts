/**
 * Extension of RTBFAnalyticsEvent
 * This file contains additional enum values for RTBFAnalyticsEvent
 * that are used in the airship.ts file but are missing from the rtbf-mfe package.
 */
export const RTBFAnalyticsEventExtension = {
  REASON_SCREEN_BACK_CLICK: 'RTBFAnalyticsEvent.REASON_SCREEN_BACK_CLICK',
  REASON_SCREEN_KEEP_CLICK: 'RTBFAnalyticsEvent.REASON_SCREEN_KEEP_CLICK',
  REASON_SCREEN_CONFIRM_CLICK: 'RTBFAnalyticsEvent.REASON_SCREEN_CONFIRM_CLICK',
  G<PERSON><PERSON><PERSON><PERSON>_SCREEN_OPEN: 'RTBFAnalyticsEvent.GUIDANCE_SCREEN_OPEN',
  G<PERSON><PERSON><PERSON><PERSON>_SCREEN_KEEP_ACCOUNT_CLICK:
    'RTBFAnalyticsEvent.GUIDANCE_SCREEN_KEEP_ACCOUNT_CLICK',
  <PERSON><PERSON><PERSON><PERSON><PERSON>_SCREEN_CANCEL_SUBS_CLICK:
    'RTBFAnalyticsEvent.GUIDANCE_SCREEN_CANCEL_SUBS_CLICK',
  GUIDANCE_SCREEN_NEXT_CLICK: 'RTBFAnalyticsEvent.GUIDANCE_SCREEN_NEXT_CLICK',
  SELECT_OPTION_SELECT_CLICK: 'RTBFAnalyticsEvent.SELECT_OPTION_SELECT_CLICK',
  SELECT_OPTION_PREVIOUS_CLICK:
    'RTBFAnalyticsEvent.SELECT_OPTION_PREVIOUS_CLICK',
  CONFIRMATION_CONFIRM_CLICK: 'RTBFAnalyticsEvent.CONFIRMATION_CONFIRM_CLICK',
  CONFIRMATION_PREVIOUS_CLICK: 'RTBFAnalyticsEvent.CONFIRMATION_PREVIOUS_CLICK',
  REASON_SCREEN_KEEP_ACCOUNT_CLICK:
    'RTBFAnalyticsEvent.REASON_SCREEN_KEEP_ACCOUNT_CLICK',
  REASON_SCREEN_CANCEL_SUBS_CLICK:
    'RTBFAnalyticsEvent.REASON_SCREEN_CANCEL_SUBS_CLICK',
  REASON_SCREEN_MANAGE_MARKETING_CLICK:
    'RTBFAnalyticsEvent.REASON_SCREEN_MANAGE_MARKETING_CLICK',
  REQUEST_SENT_OPEN: 'RTBFAnalyticsEvent.REQUEST_SENT_OPEN',
  REQUEST_FAILED_OPEN: 'RTBFAnalyticsEvent.REQUEST_FAILED_OPEN',
  GET_IN_TOUCH_CLICK: 'RTBFAnalyticsEvent.GET_IN_TOUCH_CLICK',
} as const;

// Create a type that can be used as a union with RTBFAnalyticsEvent
export type RTBFAnalyticsEventExtensionType =
  typeof RTBFAnalyticsEventExtension;
export type RTBFAnalyticsEventExtensionKeys =
  keyof RTBFAnalyticsEventExtensionType;
