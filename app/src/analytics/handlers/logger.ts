import { MapAnalyticsEvent } from '@bp/map-mfe';
import { getUserInfo } from '@common/asyncStorage';
import { logger } from '@utils/logger';
import clc from 'cli-color';

import type { AnalyticsEventMap, AnalyticsEventType } from '../analytics.types';

/**
 * Formats the platform's logEvent function to include userInfo
 * with every event
 *
 */

const customLogEvent = async (eventType: string, payload?: {}) => {
  try {
    const userInfo = await getUserInfo();
    logger.trace(eventType, { ...payload, ...userInfo });
  } catch (e) {
    logger.warn('There was an error in logger.customLogEvent(): ', e);
  }
};

const cliColorEventName = (eventName: string) =>
  clc.bgBlackBright.bold.whiteBright(` ${eventName} `);

/**
 * Some events may display too much debugging information, the messages
 * can be simplified by defining them below
 */
const eventMap: Partial<AnalyticsEventMap> = {
  [MapAnalyticsEvent.SITE_FAVOURITE_ADD]: ({ site }) => {
    return customLogEvent(
      cliColorEventName(MapAnalyticsEvent.SITE_FAVOURITE_ADD),
      {
        siteId: site.siteId,
      },
    );
  },
};

/**
 * Handles logging a custom analytics event to the console
 * @param {AnalyticsEventType} event an analytics event
 */
export const analyticsEvent = async (event: AnalyticsEventType) => {
  const customTrace: Function | undefined = eventMap[event.type];
  if (customTrace) {
    customTrace(event.payload);
    return;
  }

  logger.debug(cliColorEventName(event.type), event.payload);
};
