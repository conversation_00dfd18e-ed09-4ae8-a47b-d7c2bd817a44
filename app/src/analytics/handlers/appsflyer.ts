import {
  HelpPageAnalyticsEvent,
  LoginAnalyticsEvent,
  MigrationFlowAnalyticsEvent,
  OutageAnalyticsEvent,
  SiteBannerAnalyticsEvent,
  TabsAnalyticsEvent,
  UberAnalyticsEvent,
  UberMigrationAnalyticsEvent,
} from '@analytics/enums';
import { WalletAnalyticsEvent } from '@bp/bppay-wallet-feature';
import { ChargeHistoryAnalyticsEvent } from '@bp/charge-history-mfe';
import { ChargeAnalyticsEvent } from '@bp/charge-mfe';
import { CreditAnalyticsEvent } from '@bp/credit-mfe';
import { FavouritesAnalyticsEvent } from '@bp/favourites-mfe';
import { GuestAnalyticsEvent } from '@bp/guest_feature-mfe';
import { MapAnalyticsEvent } from '@bp/map-mfe';
import { SubsAnalyticsEvent } from '@bp/mfe-subscription';
import { OffersAnalyticsEvent } from '@bp/offers-mfe';
import { OnboardingAnalyticsEvent } from '@bp/onboarding-mfe';
import { PartnerDriverAnalyticsEvent } from '@bp/partnerdriver-mfe/dist/analytics';
import { ProfileAnalyticsEvent } from '@bp/profile-mfe';
import { ProfileAnalyticsEventType } from '@bp/profile-mfe/dist/analytics/events/types';
import { LoginAnalyticsEvent as AuthAnalyticsEventsType } from '@bp/pulse-auth-sdk';
import { RegAnalyticsEvent } from '@bp/registration-mfe';
import { RFIDAnalyticsEvent } from '@bp/rfid-mfe';
import { RTBFAnalyticsEvent } from '@bp/rtbf-mfe';
import { getUserInfo } from '@common/asyncStorage';
import env from '@env';
import { logger } from '@utils/logger';
import appsFlyer from 'react-native-appsflyer';

import type { AnalyticsEventMap, AnalyticsEventType } from '../analytics.types';

/**
 * Formats the platform's logEvent function to include userInfo
 * with every event
 *
 */

const logCustomEvent = async (eventType: string, payload?: {}) => {
  try {
    const userInfo = await getUserInfo();
    appsFlyer.logEvent(
      eventType.trim(),
      { ...payload, ...userInfo },
      () => {},
      e => {
        logger.warn('There was an error calling appsflyer logEvent: ', e);
      },
    );
  } catch (e) {
    logger.warn('There was an error calling logCustomEvent: ', e);
  }
};

const eventMap: AnalyticsEventMap = {
  [RTBFAnalyticsEvent.REASON_SCREEN_BACK_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_KEEP_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_CONFIRM_CLICK]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_OPEN]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_KEEP_ACCOUNT_CLICK]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_CANCEL_SUBS_CLICK]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_NEXT_CLICK]: undefined,
  [RTBFAnalyticsEvent.SELECT_OPTION_SELECT_CLICK]: undefined,
  [RTBFAnalyticsEvent.SELECT_OPTION_PREVIOUS_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRMATION_CONFIRM_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRMATION_PREVIOUS_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_KEEP_ACCOUNT_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_CANCEL_SUBS_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_MANAGE_MARKETING_CLICK]: undefined,
  [RTBFAnalyticsEvent.REQUEST_SENT_OPEN]: undefined,
  [RTBFAnalyticsEvent.REQUEST_FAILED_OPEN]: undefined,
  [RTBFAnalyticsEvent.GET_IN_TOUCH_CLICK]: undefined,
  [MapAnalyticsEvent.SEARCH_BAR_FOCUS]: undefined,
  [MapAnalyticsEvent.SEARCH_TEXT_CHANGE]: undefined,
  [MapAnalyticsEvent.SEARCH_LOCATION_SELECT]: undefined,
  [MapAnalyticsEvent.SEARCH_HISTORY_LOCATION_SELECT]: undefined,
  [MapAnalyticsEvent.SITE_DETAILS_CLOSE]: undefined,
  [MapAnalyticsEvent.SITE_DETAILS_VIEW]: undefined,
  [MapAnalyticsEvent.SITE_DIRECTIONS_VIEW]: undefined,
  [MapAnalyticsEvent.SITE_FAVOURITE_ADD]: undefined,
  [MapAnalyticsEvent.SITE_FAVOURITE_REMOVE]: undefined,
  [MapAnalyticsEvent.SITE_MARKER_SELECT]: undefined,
  [MapAnalyticsEvent.NEARBY_SITES_OPEN]: undefined,
  [MapAnalyticsEvent.NEARBY_SITE_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_CONNECTOR_TYPE_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_CONNECTOR_TYPE_SAVE]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_SPEED_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_SHOW_PULSE_ONLY_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_SPEED_SAVE]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_FAVOURITES_SELECT]: undefined,
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_SELECT]: undefined,
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_OPEN]: undefined,
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_CLOSE]: undefined,
  [MapAnalyticsEvent.CHARGEPOINT_CHARGE_START]: undefined,
  [MapAnalyticsEvent.CHARGEPOINT_CHARGE_AS_GUEST_SELECT]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_OPEN]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_SAVE]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_CLOSE]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_CLEAR]: undefined,
  [MapAnalyticsEvent.MAP_RECENTER_SELECT]: undefined,
  [MapAnalyticsEvent.MAP_SCREEN_OPEN]: undefined,
  [MapAnalyticsEvent.LOCATION_SERVICE_DISABLED_OPEN]: undefined,
  [MapAnalyticsEvent.LOCATION_SERVICE_DISABLED_CLOSE]: undefined,
  [MapAnalyticsEvent.OPERATOR_FILTERS_SAVE]: undefined,

  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_INVALID]: undefined,
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CHECK_VALID]: undefined,
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_CONFIRM_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_ADD_NEW_OFFER_SCREEN_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_COMING_SOON_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_ERROR_OFFER_NOT_ADDED_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_ERROR_OFFER_NOT_ADDED_GO_BACK_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_ERROR_SCREEN_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_LEGACY_PAYG_SUB_AND_SAVE_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_LEGACY_SUBS_MY_SUBSCRIPTION_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_LOGIN_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_LOGIN_CREATE_ACCOUNT_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_OFFER_ADDED_SUCCESS_OPEN]: ({
    offer_code,
    offer_type,
  }) => {
    return logCustomEvent('Offers_OfferAdded_Success_Open', {
      Entered_Value: offer_code,
      Offer_Type: offer_type,
    });
  },
  [OffersAnalyticsEvent.OFFERS_SCREEN_ACTIVE_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_NEW_OFFER_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_SUBSCRIBE_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_TCS_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SCREEN_USED_EXPIRED_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_OPEN]: undefined,
  [OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_REMOVE_CLICK]: undefined,
  [OffersAnalyticsEvent.OFFERS_SUBS_REQUIRED_SETUP_CLICK]: undefined,

  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_MODAL_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_KEEP_UBER_CLICK]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_EVENT]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_TRY_AGAIN_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_CONTACT_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_SUCCESSFULL_OPEN]:
    undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_OFFERS_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_BACK]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_CONTINUE]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LINK_RESULT_SCREEN_CTA_CLICK]: undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_ADAC_LINK_RESULT_SCREEN_OPEN]: ({
    rfid_status,
    response_code,
    error_message,
  }) => {
    return logCustomEvent('Partner_ADAC_LinkResultScreen_Open', {
      RFID_Status: rfid_status,
      Response_Code: response_code,
      ErrorMessage: error_message,
    });
  },
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_BACK]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_GET_RFID]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_LEARN_MORE]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_RETRY]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_BACK]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_ORDER_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_LOGOUT_MODAL_LOGOUT_CLICK]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_LOGOUT_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_EXPLORE_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_UNLINK_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UNLINK_ACCOUNTS_SUCCESS_SUBSCRIBE_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UNLINK_ACCOUNTS_SUCCESS_FIND_CHARGER_CLICK]:
    undefined,

  [ProfileAnalyticsEvent.PROFILE_SCREEN_LOGOUT_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_LOGIN_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_REGISTER_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_LANGUAGE_SELECTOR_UPDATE]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_FEEDBACK_CLICK]: undefined,
  [ProfileAnalyticsEvent.SETTINGS_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.ACCOUNT_DETAILS_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_OPEN]: undefined,
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_OPT_OUT_SAVE]: undefined,
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_UPDATE_SAVE]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.SETTINGS_SCREEN_TCS_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_SUCCESSFUL_FINISH]: undefined,

  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_EDIT_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_ADD_ADDRESS_CLICK]:
    undefined,
  [ProfileAnalyticsEvent.PROFILE_EXIT_MODAL_DELETE_CHANGES_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_FAILED_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_SEARCH_ERROR]: undefined,

  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_SUCCESSFUL_OPEN]: undefined,

  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_FAIL_EXIT_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SUCCESS_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SUCCESS_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_FAILURE_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_FAILURE_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_EXIT_MODAL_KEEP_EDITING_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_BACK]: undefined,

  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_SAVE_CLICK]: undefined,

  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_MANUAL_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_OPEN]: undefined,

  [ProfileAnalyticsEvent.PROFILE_UPDATE_DEATILS_FAIL_TRY_AGAIN_CLICK]:
    undefined,

  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_UPDATE_ADDRESS_CLICK]:
    undefined,
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_DONE_CLICK]: undefined,

  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_VERIFY_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SUCCESS_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SUCCESS_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_FAILURE_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_FAILURE_FINISH]: undefined,

  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_VERIFY_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_VERIFY_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SUCCESS_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SUCCESS_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_FAILURE_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_FAILURE_FINISH]: undefined,

  [HelpPageAnalyticsEvent.HELP_SCREEN_EMAIL_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_SCREEN_FAQS_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_SCREEN_PHONE_NUMBER_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_WEB_FORM_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_PARTNER_SITE_CLICK]: undefined,

  [AuthAnalyticsEventsType.LOGIN_ERROR]: undefined,
  [AuthAnalyticsEventsType.LOGIN_SCREEN_CLOSE]: undefined,
  [AuthAnalyticsEventsType.CIP_REGISTRATION_SUCCESS]: userId =>
    logCustomEvent('CIP_Registration_Success', { UserID: userId }),
  [AuthAnalyticsEventsType.CIP_REGISTRATION_ERROR]: undefined,
  [AuthAnalyticsEventsType.CIP_LOGIN_SUCCESS]: undefined,

  [LoginAnalyticsEvent.FIND_CHARGER_MAP_BANNER]: undefined,
  [LoginAnalyticsEvent.LOGIN_CLICK]: undefined,
  [LoginAnalyticsEvent.CREATE_ACCOUNT_CLICK]: undefined,
  [LoginAnalyticsEvent.CONFIRM_UBER_DRIVER_CLICK]: undefined,

  [OutageAnalyticsEvent.OUTAGE_FULL_OUTAGE_SCREEN_OPEN]: undefined,
  [OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_OPEN]: undefined,
  [OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_CLOSE]: undefined,

  [TabsAnalyticsEvent.CHARGE_TAB_ACTIVE_CHARGE_CLICK]: undefined,
  [TabsAnalyticsEvent.CHARGE_TAB_NO_CHARGE_CLICK]: undefined,
  [TabsAnalyticsEvent.HELP_TAB_CLICK]: undefined,
  [TabsAnalyticsEvent.MAP_TAB_CLICK]: undefined,
  [TabsAnalyticsEvent.PROFILE_TAB_CLICK]: undefined,

  // Charge History Analytics
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_OPEN]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_CLICK]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADVAT_CLICK]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADVAT_FAILED]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_ADDADDRESS]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_UNPAIDVIEW_MAKEPAYMENT]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADCREDITNOTE]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADORIGINAL]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_CREDITNOTE_FAILED]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_REFUND_ADDADDRESS]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_CLICK]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_CHARGINGFEES_CLICK]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_DOWNLOADVAT_FAILED]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_DOWNLOADVAT_CLICK]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSCRIPTIONINVOICESTAB_CLICK]:
    undefined,
  [GuestAnalyticsEvent.PRE_AUTHORISATION_FAILED]: undefined,
  [GuestAnalyticsEvent.PRE_AUTHORISATION_SUCCESSFUL]: undefined,

  [RFIDAnalyticsEvent.PULSE_CHARGE_CARD_OPEN]: undefined,
  [RFIDAnalyticsEvent.CONFIRM_AND_ORDER_ORDER_CTA_CLICK]: undefined,
  [RFIDAnalyticsEvent.CARD_ORDER_SUCCESS]: undefined,
  [RFIDAnalyticsEvent.CARD_ORDER_FAILED]: undefined,
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_REPLACE_CLICK]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_POP_UP_ORDER_NEW_SELECT]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_POP_UP_KEEP_CARD_SELECT]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_SUCCESS]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_FAILED]: undefined,
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_CANCEL_CLICK]: undefined,
  [RFIDAnalyticsEvent.CANCEL_CARD_FAILED]: undefined,
  [RFIDAnalyticsEvent.CANCEL_CARD_SUCCESS]: undefined,
  [RFIDAnalyticsEvent.POPULATED_SHIPPING_ADDRESS_OPEN]: undefined,
  [RFIDAnalyticsEvent.POPULATED_SHIPPING_ADDRESS_SAVE_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_BACK_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_USE_ADDRESS_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_ERROR]: undefined,
  [RFIDAnalyticsEvent.PULSE_CHARGE_CARD_ORDER_CTA_CLICK]: undefined,
  [RFIDAnalyticsEvent.LINK_PAYMENT_CARD_SCREEN_OPEN]: undefined,
  [RFIDAnalyticsEvent.LINK_PAYMENT_CARD_SCREEN_CTA_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_OPEN]: undefined,
  [RFIDAnalyticsEvent.CONFIRM_AND_ORDER_OPEN]: undefined,
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_OPEN]: undefined,
  [RFIDAnalyticsEvent.UBER_MIGRATION_SCREEN_SUCCESS_OPEN]: undefined,

  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CLOSE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CONNECTOR_CHANGE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CONNECTOR_CONFIRM]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_ID_MATCH]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_NO_ID_MATCH]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_NO_ACCESS]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_CHARGER_ID_HELP_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_NEAR_BY_SITE_SELECT_LOCATION_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_NEAR_BY_SITE_CHARGE_NOW_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_START_CHARGE_CLICK]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StartScreen_StartCharge_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_CONNECTING_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_REQUEST_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_REQUEST_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ACCEPTED_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ACCEPTED_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_SUCCESSFUL]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StartChargeSuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_RESPONSE_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
    ErrorMessage,
  }) => {
    return logCustomEvent('Charge_StartChargeUnsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      ErrorMessage,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_TRY_AGAIN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_DISCONNECT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_CALL_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_NO_DATA_RETRY_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_NO_DATA_END_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_LONG_WAIT_CALL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_GO_BACK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CONNECTING_RENEWABLE_EXPANDER_OPEN]: undefined,
  [ChargeAnalyticsEvent.NO_ACTIVE_SESSION_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.NO_ACTIVE_SESSION_SCREEN_FIND_A_CHARGER_SELECT]:
    undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_STOP_CHARGE_STEPS_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT_WAIT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT_DISC]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT_DISC]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT_CALL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGING_POP_UP_YES_CLICK]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StopChargingPopUp_Yes_Click', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGING_POP_UP_NO_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_DISCONNECTING_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_REQUEST_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_REQUEST_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_ACCEPTED_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_ACCEPTED_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_SUCCESSFUL]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StopChargeSuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_RESPONSE_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_DISCONNECTING_TIMEOUT_OPEN]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_DisconnectingTimeout_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_UNSUCCESSFUL]: ({
    chargepoint,
    connector,
  }) => {
    return logCustomEvent('Charge_StopChargeUnsuccessful', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_OPEN]: ({
    chargepoint,
    connector,
    af_currency,
    af_revenue,
  }) => {
    return logCustomEvent('Charge_SummaryScreen_Open', {
      CP_ID: chargepoint?.apolloInternalId,
      Site_Provider: chargepoint?.provider,
      Site_Country: chargepoint?.site?.siteDetails?.country,
      ConnectorType: connector?.type,
      af_currency,
      af_revenue,
      af_content_id: 'Charge_SummaryScreen_Open',
      CP_Scheme: chargepoint?.schemes[0]?.schemeName,
      CP_Operator: chargepoint?.site?.cpo,
    });
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_VAT_INVOICE_DOWNLOAD]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_VAT_IN_HISTORY_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_ERROR_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_GUEST_START_CHARGE_SELECT_PAYMENT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_SCREEN_CLOSE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_HISTORY_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CHARGER_NO_LONGER_AVAILABLE_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_BPCM_TIMEOUT_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_PREAUTH_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_EXTERNAL_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CONNECT_INSTRUCTIONS_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CONNECT_START_CHARGE_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_MAP_ERROR]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_NO_ACCESS]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_TOP_UP_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_TOP_UP_SCREEN_TOP_UP_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_GET_DIRECTIONS_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_CHARGER_INFORMATION_CLICK]:
    undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_NAVIGATE_TO_WEBSHOP]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_OUTSIDE_REGION]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_CHARGE_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_ADD_PAYMENT_METHOD]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_GET_CHARGE_NO_DATA]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SERVICE_UNAVAILABLE_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_BLOCK_START_CHARGE_USER_DISTANCE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_BLOCK_START_CHARGE_NO_LOCATION_SERVICES]:
    undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_OPEN]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_BACK]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_KEEP_CLICK]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_DELETE_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_BACK_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_KEEP_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRM_DELETION_CANCEL_SUBS_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRM_DELETION_SCREEN_CONFIRM_CLICK]: undefined,
  [RTBFAnalyticsEvent.REQUEST_SENT_DONE]: undefined,
  [RTBFAnalyticsEvent.REQUEST_FAILED_DONE]: undefined,
  [RTBFAnalyticsEvent.REFUND_EMAIL_SENT]: undefined,
  [RTBFAnalyticsEvent.REQUEST_RECEIVED_OPEN]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_SETTINGS_CLICK]: undefined,
  [WalletAnalyticsEvent.CHANGE_DEFAULT_CARD_SAVE]: undefined,
  [WalletAnalyticsEvent.REMOVE_CARD_CANT_REMOVE_POP_UP_OPEN]: undefined,
  [WalletAnalyticsEvent.REMOVE_CARD_ERROR_POP_UP_OPEN]: undefined,
  [WalletAnalyticsEvent.REMOVE_FINAL_CARD_POP_UP_REMOVE_CLICK]: undefined,
  [WalletAnalyticsEvent.REMOVE_CARD_POP_UP_REMOVE_CLICK]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_FAILED_SCREEN_CLOSE]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_FAILED_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_SUCCESS_SCREEN_FINISH]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_SUCCESS_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_SUM_SCREEN_AGREE_CLICK]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_BANNER_VIEW_CLICK]: undefined,
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_CLOSE_CLICK]: undefined,
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_RETRY_CLICK]: undefined,
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.CARD_ADDED_SCREEN_FINISH_CLICK]: undefined,
  [WalletAnalyticsEvent.CARD_ADDED_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.PAYMENT_DETAILS_ADD_NEW_CARD_CLICK]: undefined,
  [WalletAnalyticsEvent.PAYMENT_DETAILS_MY_SUBS_CLICK]: undefined,
  [WalletAnalyticsEvent.CHARGE_DEFAULT_CARD_ADD_NEW]: undefined,
  [WalletAnalyticsEvent.CHARGE_DEFAULT_CARD_UPDATE]: undefined,
  [WalletAnalyticsEvent.PAYMENT_DETAILS_OPEN]: undefined,
  [WalletAnalyticsEvent.PAYMENT_SCREEN_RECORD_DETECTED_OPEN]: undefined,
  [WalletAnalyticsEvent.ADD_CARD_SCREEN_RECORD_DETECTED_OPEN]: undefined,
  [WalletAnalyticsEvent.EXPOSED_COMPONENT_ERROR_OPEN]: undefined,
  [WalletAnalyticsEvent.EXPOSED_COMPONENT_ERROR_RETRY_CLICK]: undefined,
  [WalletAnalyticsEvent.SUBS_CVV_RECACHE_FLOW_OPEN]: undefined,
  [WalletAnalyticsEvent.SUBS_CVV_RECACHE_FLOW_COMPLETE]: undefined,
  [WalletAnalyticsEvent.CVV_RECACHE_FLOW_COMPLETE_3DS_CHECK_FAILURE]: undefined,

  [CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_OPEN]: undefined,
  [CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_ERROR_VIEW]: undefined,
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_BUTTON_CLICK]: undefined,
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_SUCCESS]: ({ creditBalance }) =>
    logCustomEvent('Credit_TopUpCredit_Success', { creditBalance }),
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_FAILED]: undefined,
  [CreditAnalyticsEvent.CREDIT_STRIPE_WEBVIEW_CLOSED]: undefined,
  [CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_SUCCESS]: undefined,
  [CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_ERROR]: undefined,

  [SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) =>
    logCustomEvent('SUBS_IntroOfferScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    }),
  [SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
  }) =>
    logCustomEvent('SUBS_ReactivateSubsScreen_Open', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
    }),
  [SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS]: undefined,
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR]: undefined,
  [SubsAnalyticsEvent.SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_FAILURE]: undefined,
  [SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_SUCCESS]: undefined,
  [SubsAnalyticsEvent.SUBS_SETUP_COMPLETE]: ({
    account_balance,
    gocardless_mandate_status,
    tag_ids,
    first_time_subscribing,
    offer_code,
    currency,
    value,
    discount,
    subscription_id,
  }) =>
    logCustomEvent('SUBS_SetupComplete', {
      account_balance,
      gocardless_mandate_status,
      tag_ids,
      first_time_subscribing,
      offer_code,
      currency,
      value,
      discount,
      subscription_id,
    }),
  [SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE]: undefined,
  [SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_FAILED]: undefined,

  [SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_OPEN]: ({
    tag_ids,
    first_time_subscribing,
  }) =>
    logCustomEvent('SUBS_SubscribeAndSaveScreen_Open', {
      tag_ids,
      first_time_subscribing,
    }),
  [SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_CTA_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_MY_SUBSCRIPTION_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_CONFIRM_SUBSCRIPTION_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_SUCCESS]: undefined,
  [SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_ERROR]: undefined,
  [SubsAnalyticsEvent.SUBS_CONFIRM_AND_AUTHORISE_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_START_CHARGING_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_ORDER_RFID_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_SETUP_FAILURE_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_SETUP_FAILURE_RETRY_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_EXIT_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_RETRY_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_FAILURE]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_SUCCESS]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_FAILURE]: undefined,
  [SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_SUCCESS]: undefined,

  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_OPEN]: undefined,
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_SEND_VERIF_CLICK]: undefined,
  [RegAnalyticsEvent.CUSTOMISE_BP_PULSE_APP_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.CUSTOMISE_BP_PULSE_APP_OPEN]: undefined,
  [RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_CONTINUE_CLICK]: undefined,
  [RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_OPEN]: undefined,
  [RegAnalyticsEvent.EMAIL_VER_SCREEN_CHANGE_EMAIL_CLICK]: undefined,
  [RegAnalyticsEvent.EMAIL_VER_SCREEN_VERIFIED_EMAIL_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_BACK_TOBPPULSE_APP_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_CONTINUE_SETUP_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_LOG_OUT_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_OPEN]: undefined,
  [RegAnalyticsEvent.INCORRECT_ACCESS_TOKEN_OPEN]: undefined,
  [RegAnalyticsEvent.JUMP_RIGHT_IN_CLICK]: undefined,
  [RegAnalyticsEvent.MARKETING_OPT_IN_SAVE]: () =>
    logCustomEvent('Reg_MarketingOptIn_Save'),
  [RegAnalyticsEvent.MARKETING_OPT_OUT_SAVE]: () =>
    logCustomEvent('Reg_MarketingOptOut_Save'),
  [RegAnalyticsEvent.SETTING_UP_EV_ACCOUNT_LOGOUT_CLICK]: undefined,
  [RegAnalyticsEvent.SETTING_UP_EV_ACCOUNT_TRY_AGAIN_CLICK]: undefined,
  [RegAnalyticsEvent.SETTING_UP_EVACCOUNT_OPEN]: undefined,
  [RegAnalyticsEvent.TRY_AGAIN_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_GO_BACK_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_LOGIN_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_OPEN]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_CLOSE_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_OPEN]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_CLOSE_CLICK]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_OPEN]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_SEND_VERIF_CLICK]: undefined,
  [RegAnalyticsEvent.VERIFICATION_LINK_SENT_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.VERIFICATION_LINK_SENT_OPEN]: undefined,
  [RegAnalyticsEvent.EV_ACCOUNT_SETUP_SUCCESSFUL]: undefined,
  [RegAnalyticsEvent.EV_ACCOUNT_SETUP_UNSUCCESSFUL]: undefined,

  [FavouritesAnalyticsEvent.GET_FAVOURITES_SUCCESS]: undefined,
  [FavouritesAnalyticsEvent.GET_FAVOURITES_FAILED]: undefined,
  [FavouritesAnalyticsEvent.ADD_FAVOURITE_SUCCESS]: undefined,
  [FavouritesAnalyticsEvent.ADD_FAVOURITE_FAILED]: undefined,
  [FavouritesAnalyticsEvent.REMOVE_FAVOURITE_SUCCESS]: undefined,
  [FavouritesAnalyticsEvent.REMOVE_FAVOURITE_FAILED]: undefined,

  [OnboardingAnalyticsEvent.REQUEST_SUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.REQUEST_UNSUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.ONBOARDING_ACCOUNT_SUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.ONBOARDING_ACCOUNT_UNSUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.ONBOARDING_CHARGING_SUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.ONBOARDING_CHARGING_UNSUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.ONBOARDING_ROAMING_SUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.ONBOARDING_ROAMING_UNSUCCESSFUL]: undefined,

  [UberAnalyticsEvent.UNLINK_ACCOUNTS_MODAL_OPEN]: undefined,
  [UberAnalyticsEvent.WELCOME_TO_UBER_OPEN]: undefined,
  [UberAnalyticsEvent.WELCOME_TO_UBER_FIND_NEARBY_CHARGER_CLICK]: undefined,
  [UberAnalyticsEvent.WELCOME_TO_UBER_VIEW_REWARDS_CLICK]: undefined,
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_OPEN]: undefined,
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_VERIFY_CLICK]: undefined,
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_X_CLICK]: undefined,
  [UberAnalyticsEvent.LOGIN_WITH_UBER_SUCCESS]: undefined,
  [UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_SUBSCRIBE_CLICK]: undefined,
  [UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_FIND_CHARGER_CLICK]:
    undefined,
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_KEEP_UBER_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_EVENT]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_OPEN]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_CONTACT_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_TRY_AGAIN_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINKING_ACCOUNTS_OPEN]: undefined,
  [UberAnalyticsEvent.UNLINING_ACCOUNTS_SUCCESSFUL_OPEN]: undefined,
  [UberAnalyticsEvent.CONNECTING_ACCOUNTS_SUCCESSFUL_OPEN]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_OPEN]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_TRYAGAIN_CLICK]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_UNLINK_CLICK]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRYLIMIT_OPEN]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRY_SUCCESS]: undefined,

  [SiteBannerAnalyticsEvent.CHARGE_SELECT_CONNECTOR_UNENTITLED]: undefined,
  [SiteBannerAnalyticsEvent.MAP_SITE_UNENTITLED]: undefined,
  [ProfileAnalyticsEventType.OFFERS_WIDGET_CLICK]: undefined,

  [MigrationFlowAnalyticsEvent.PAYG_MIGRATION_SCREEN_OPEN]: undefined,
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_DISMISS_CLICK]: undefined,
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK]: ({
    entered_value,
  }) =>
    logCustomEvent('PAYGMigrationScreen_UpdateAccount_Click', {
      entered_value,
    }),

  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_ORDER_RFID_CLICK]:
    undefined,
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_MOVETOSUBS_CLICK]:
    undefined,
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_FAQ_CLICK]: undefined,
  [MigrationFlowAnalyticsEvent.PAYG_MIGRATION_ERRORSCREEN_OPEN]: ({ error }) =>
    logCustomEvent('PAYGMigration_ErrorScreen_Open', {
      error,
    }),

  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK]: ({
    entered_value,
  }) =>
    logCustomEvent('SUBSMigrationScreen_UpdateAccount_Click', {
      entered_value,
    }),
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_KEEPSUBS_CLICK]: undefined,
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DECLINESUBS_CLICK]:
    undefined,
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_FAQ_CLICK]: undefined,
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATION_ERRORSCREEN_OPEN]: ({ error }) =>
    logCustomEvent('SUBSMigration_ErrorScreen_Open', {
      error,
    }),

  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_OPEN]: undefined,
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_DISMISS_CLICK]: undefined,
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_UPDATE_ACCOUNT_CLICK]:
    undefined,
  [UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_MOVE_TO_SUBS_CLICK]:
    undefined,
};

/**
 * Handles logging a custom analytics event to Appsflyer
 * @param {AnalyticsEventType} event an analytics event
 */
export const analyticsEvent = async (event: AnalyticsEventType) => {
  const command: Function | undefined = eventMap[event.type];
  if (command) {
    return command(event.payload);
  }
  return null;
};

/**
 * Apply various appsFlyer event listeners
 */
export const applyListeners = () => {
  logger.info('Applying Appsflyer listeners');
  appsFlyer.onInstallConversionData(() => {});
  appsFlyer.onDeepLink(() => {});
};

/**
 * Initialize AppsFlyer
 */
export const initSdk = async () => {
  appsFlyer.initSdk(
    {
      devKey: env.APPSFLYER_DEV_KEY,
      isDebug: !!env.APPSFLYER_DEBUG_MODE,
      appId: env.APPSFLYER_APP_ID,
      onInstallConversionDataListener: true,
      onDeepLinkListener: true,
      timeToWaitForATTUserAuthorization: 10,
    },
    res => {
      logger.info('Appsflyer initSdk callback: ', res);
      // On success, apply appsflyer listeners
      applyListeners();
    },
    e => {
      logger.error('AppsFlyer error: ', e);
    },
  );
};
