import Airship from '@ua/react-native-airship';

import { AirshipAnalyticsService, logCustomEvent } from './airship';

// Mock Airship and dependencies
jest.mock('@ua/react-native-airship', () => ({
  takeOff: jest.fn().mockResolvedValue(true),
  isFlying: jest.fn().mockResolvedValue(true),
  addListener: jest.fn().mockReturnValue({ remove: jest.fn() }),
  analytics: {
    addCustomEvent: jest.fn().mockResolvedValue(undefined),
  },
  push: {
    setUserNotificationsEnabled: jest.fn().mockResolvedValue(undefined),
  },
  channel: {
    editAttributes: jest.fn().mockReturnValue({
      setAttribute: jest.fn().mockReturnThis(),
      apply: jest.fn().mockResolvedValue(undefined),
    }),
  },
  contact: {
    identify: jest.fn().mockResolvedValue(undefined),
    getNamedUserId: jest.fn().mockResolvedValue('test-user-id'),
    editAttributes: jest.fn().mockReturnValue({
      setAttribute: jest.fn().mockReturnThis(),
      apply: jest.fn().mockResolvedValue(undefined),
    }),
  },
  EventType: {
    DeepLink: 'deeplink_received',
    NotificationResponse: 'notification_response',
    PushReceived: 'push_received',
  },
}));

jest.mock('@common/asyncStorage', () => ({
  getUserInfo: jest.fn().mockResolvedValue({ userId: 'test-user' }),
}));

jest.mock('@utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@utils/navigation', () => ({
  navigate: jest.fn(),
}));

jest.mock('@env', () => ({
  AIRSHIP_APP_SECRET: 'test-secret',
  AIRSHIP_APP_KEY: 'test-key',
  AIRSHIP_SITE: 'eu',
  AIRSHIP_IN_PRODUCTION: false,
}));

describe('AirshipAnalyticsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the static properties
    // @ts-ignore
    AirshipAnalyticsService.isInitialized = false;
    // @ts-ignore
    AirshipAnalyticsService.isReady = false;
    // @ts-ignore
    AirshipAnalyticsService.eventListenersAdded = false;
    // @ts-ignore
    AirshipAnalyticsService.initPromise = null;
  });

  describe('initAirShip', () => {
    it('should initialize Airship with correct configuration', async () => {
      // Call the initialization method
      await AirshipAnalyticsService.initAirShip();

      // Verify takeOff was called with correct config
      expect(Airship.takeOff).toHaveBeenCalledWith(
        expect.objectContaining({
          default: {
            appSecret: 'test-secret',
            appKey: 'test-key',
          },
          site: 'eu',
        }),
      );

      // Verify event listeners were added
      expect(Airship.addListener).toHaveBeenCalledWith(
        'deeplink_received',
        expect.any(Function),
      );
    });
  });

  describe('logCustomEvent', () => {
    it('should log a custom event to Airship directly', async () => {
      // Call logCustomEvent
      await logCustomEvent('test_event', { key: 'value' });

      // Verify that the Airship SDK method was called directly
      expect(Airship.analytics.addCustomEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventName: 'test_event',
          properties: expect.objectContaining({
            key: 'value',
            userInfo: { userId: 'test-user' },
          }),
        }),
      );
    });

    it('should handle errors gracefully', async () => {
      // Mock the Airship method to throw an error
      const mockError = new Error('Test error');
      (Airship.analytics.addCustomEvent as jest.Mock).mockRejectedValueOnce(
        mockError,
      );

      // Call logCustomEvent
      await logCustomEvent('test_event', { key: 'value' });

      // Verify error was logged
      expect(require('@utils/logger').logger.error).toHaveBeenCalledWith(
        'There was an error calling Airship logCustomEvent: ',
        mockError,
      );
    });
  });
  describe('user identity methods', () => {
    it('should set customer type attribute directly', async () => {
      await AirshipAnalyticsService.setCustomerTypeAttribute('test-type');

      expect(
        Airship.channel.editAttributes().setAttribute,
      ).toHaveBeenCalledWith('customer_type', 'test-type');
    });

    it('should associate user ID with Airship directly', async () => {
      await AirshipAnalyticsService.associateUserIdToAirshipNamedUserId(
        'test-user-id',
      );

      expect(Airship.contact.identify).toHaveBeenCalledWith('test-user-id');
      expect(Airship.contact.getNamedUserId).toHaveBeenCalled();
    });

    it('should set Salesforce push consent attribute directly', async () => {
      await AirshipAnalyticsService.setSalesforcePushConsentAttribute(true);

      const setAttribute = Airship.contact.editAttributes().setAttribute;
      const apply = Airship.contact.editAttributes().apply;

      expect(setAttribute).toHaveBeenCalledWith(
        'salesforce_push_consent',
        true,
      );
      expect(apply).toHaveBeenCalled();
    });
  });
});
