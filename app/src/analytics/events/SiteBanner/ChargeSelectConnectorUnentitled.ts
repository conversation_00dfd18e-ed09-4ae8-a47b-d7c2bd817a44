import { SiteBannerAnalyticsEvent } from '@analytics/enums';

type SiteDetails = {
  siteProvider: string;
  siteCountry: string;
  cpScheme: string;
  cpOperator: string | null | undefined;
};

type ChargeSelectConnectorUnentitledPayload = {
  siteDetails?: SiteDetails;
  bannerType: string;
  connectorType?: string | unknown;
  cpId?: string | unknown;
};

export type ChargeSelectConnectorUnentitledType = {
  type: SiteBannerAnalyticsEvent.CHARGE_SELECT_CONNECTOR_UNENTITLED;
  payload: ChargeSelectConnectorUnentitledPayload;
};

export const ChargeSelectConnectorUnentitled = (
  payload: ChargeSelectConnectorUnentitledPayload,
): ChargeSelectConnectorUnentitledType => ({
  type: SiteBannerAnalyticsEvent.CHARGE_SELECT_CONNECTOR_UNENTITLED,
  payload,
});
