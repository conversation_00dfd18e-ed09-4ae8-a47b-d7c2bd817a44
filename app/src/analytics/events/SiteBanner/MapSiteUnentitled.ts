import { SiteBannerAnalyticsEvent } from '@analytics/enums';

type SiteDetails = {
  siteId: string;
  siteProvider: string;
  siteCountry: string;
  cpScheme: string;
  cpOperator: string | null | undefined;
};

type MapSiteUnentitledPayload = {
  siteDetails?: SiteDetails;
  bannerType: string;
  connectorType?: string | unknown;
  isLocationServiceEnabled?: boolean | unknown;
};

export type MapSiteUnentitledType = {
  type: SiteBannerAnalyticsEvent.MAP_SITE_UNENTITLED;
  payload: MapSiteUnentitledPayload;
};

export const MapSiteUnentitled = (
  payload: MapSiteUnentitledPayload,
): MapSiteUnentitledType => ({
  type: SiteBannerAnalyticsEvent.MAP_SITE_UNENTITLED,
  payload,
});
