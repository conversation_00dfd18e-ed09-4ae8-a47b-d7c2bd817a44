import { LoginAnalyticsEvent } from '@analytics/enums';

type SiteDetails = {
  siteProvider: string;
  siteCountry: string;
  cpScheme: string;
};

type LoginAnalyticsEventFindChargerPayload = {
  siteDetails: SiteDetails;
  bannerType: string;
  MFEName: string | unknown;
};

export type LoginAnalyticsEventFindChargerType = {
  type: LoginAnalyticsEvent.FIND_CHARGER_MAP_BANNER;
  payload: LoginAnalyticsEventFindChargerPayload;
};

export const LoginAnalyticsEventFindCharger = (
  payload: LoginAnalyticsEventFindChargerPayload,
): LoginAnalyticsEventFindChargerType => ({
  type: LoginAnalyticsEvent.FIND_CHARGER_MAP_BANNER,
  payload,
});
