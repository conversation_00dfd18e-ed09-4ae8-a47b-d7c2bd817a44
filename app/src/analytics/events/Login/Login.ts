import { LoginAnalyticsEvent } from '@analytics/enums';

type LoginAnalyticsEventLogInPayload = {
  bannerType: string;
  MFEName: string | unknown;
};

export type LoginAnalyticsEventLogInType = {
  type: LoginAnalyticsEvent.LOGIN_CLICK;
  payload: LoginAnalyticsEventLogInPayload;
};

export const LoginAnalyticsEventLogIn = (
  payload: LoginAnalyticsEventLogInPayload,
): LoginAnalyticsEventLogInType => ({
  type: LoginAnalyticsEvent.LOGIN_CLICK,
  payload,
});
