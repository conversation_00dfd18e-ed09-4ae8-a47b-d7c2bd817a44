import { LoginAnalyticsEvent } from '@analytics/enums';

type SiteDetails = {
  siteProvider: string;
  siteCountry: string;
  cpScheme: string;
};

type LoginAnalyticsEventConfirmUberDriverPayload = {
  siteDetails: SiteDetails;
  bannerType: string;
  MFEName: string | unknown;
};

export type LoginAnalyticsEventConfirmUberDriverType = {
  type: LoginAnalyticsEvent.CONFIRM_UBER_DRIVER_CLICK;
  payload: LoginAnalyticsEventConfirmUberDriverPayload;
};

export const LoginAnalyticsEventConfirmUberDriver = (
  payload: LoginAnalyticsEventConfirmUberDriverPayload,
): LoginAnalyticsEventConfirmUberDriverType => ({
  type: LoginAnalyticsEvent.CONFIRM_UBER_DRIVER_CLICK,
  payload,
});
