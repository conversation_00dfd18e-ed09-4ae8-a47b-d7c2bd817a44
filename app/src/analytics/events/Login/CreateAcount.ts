import { LoginAnalyticsEvent } from '@analytics/enums';

type LoginAnalyticsEventCreateAccountPayload = {
  bannerType: string;
  MFEName: string | unknown;
};

export type LoginAnalyticsEventCreateAccountType = {
  type: LoginAnalyticsEvent.CREATE_ACCOUNT_CLICK;
  payload: LoginAnalyticsEventCreateAccountPayload;
};

export const LoginAnalyticsEventCreateAccount = (
  payload: LoginAnalyticsEventCreateAccountPayload,
): LoginAnalyticsEventCreateAccountType => ({
  type: LoginAnalyticsEvent.CREATE_ACCOUNT_CLICK,
  payload,
});
