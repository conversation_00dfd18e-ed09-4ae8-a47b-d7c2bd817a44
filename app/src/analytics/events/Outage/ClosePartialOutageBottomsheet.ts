import { OutageAnalyticsEvent } from '@analytics/enums';

type OutageAnalyticsEventClosePartialOutageBottomsheetPayload = {};

export type OutageAnalyticsEventClosePartialOutageBottomsheetType = {
  type: OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_CLOSE;
  payload: OutageAnalyticsEventClosePartialOutageBottomsheetPayload;
};

export const OutageAnalyticsEventClosePartialOutageBottomsheet = (
  payload: OutageAnalyticsEventClosePartialOutageBottomsheetPayload = {},
): OutageAnalyticsEventClosePartialOutageBottomsheetType => ({
  type: OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_CLOSE,
  payload,
});
