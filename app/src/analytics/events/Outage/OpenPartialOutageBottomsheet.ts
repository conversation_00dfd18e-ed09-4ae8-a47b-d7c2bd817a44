import { OutageAnalyticsEvent } from '@analytics/enums';

type OutageAnalyticsEventOpenPartialOutageBottomsheetPayload = {};

export type OutageAnalyticsEventOpenPartialOutageBottomsheetType = {
  type: OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_OPEN;
  payload: OutageAnalyticsEventOpenPartialOutageBottomsheetPayload;
};

export const OutageAnalyticsEventOpenPartialOutageBottomsheet = (
  payload: OutageAnalyticsEventOpenPartialOutageBottomsheetPayload = {},
): OutageAnalyticsEventOpenPartialOutageBottomsheetType => ({
  type: OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_OPEN,
  payload,
});
