import { OutageAnalyticsEvent } from '@analytics/enums';

type OutageAnalyticsEventOpenFullOutageScreenPayload = {};

export type OutageAnalyticsEventOpenFullOutageScreenType = {
  type: OutageAnalyticsEvent.OUTAGE_FULL_OUTAGE_SCREEN_OPEN;
  payload: OutageAnalyticsEventOpenFullOutageScreenPayload;
};

export const OutageAnalyticsEventOpenFullOutageScreen = (
  payload: OutageAnalyticsEventOpenFullOutageScreenPayload = {},
): OutageAnalyticsEventOpenFullOutageScreenType => ({
  type: OutageAnalyticsEvent.OUTAGE_FULL_OUTAGE_SCREEN_OPEN,
  payload,
});
