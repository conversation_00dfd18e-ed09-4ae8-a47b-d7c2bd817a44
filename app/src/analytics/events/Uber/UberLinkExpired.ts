import { UberAnalyticsEvent } from '@analytics/enums';

export type UberLinkExpiredPayload = {};

export type UberLinkExpiredOpenType = {
  type: UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_OPEN;
  payload: UberLinkExpiredPayload;
};

export const ReAuthUberLinkExpiredOpen = (
  payload: UberLinkExpiredPayload = {},
): UberLinkExpiredOpenType => ({
  type: UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_OPEN,
  payload,
});

export type UberLinkExpiredLogInWithUberClickType = {
  type: UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_VERIFY_CLICK;
  payload: UberLinkExpiredPayload;
};

export const ReAuthUberLinkExpiredVerifyClick = (
  payload: UberLinkExpiredPayload = {},
): UberLinkExpiredLogInWithUberClickType => ({
  type: UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_VERIFY_CLICK,
  payload,
});

export type UberLinkExpiredXClickType = {
  type: UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_X_CLICK;
  payload: UberLinkExpiredPayload;
};

export const ReAuthUberLinkExpiredXClick = (
  payload: UberLinkExpiredPayload = {},
): UberLinkExpiredXClickType => ({
  type: UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_X_CLICK,
  payload,
});
