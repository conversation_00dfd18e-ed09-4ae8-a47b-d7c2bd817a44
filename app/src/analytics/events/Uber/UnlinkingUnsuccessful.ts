import { UberAnalyticsEvent } from '@analytics/enums';

export enum USER_JOURNEY {
  UBER_REAUTH = 'uber_reauth',
  UBER_INELIGIBILITY = 'uber_ineligibility',
  PARTNER_UNLINK = 'partner_unlink',
}

export type UnlinkingUnsuccesfulPayload = {
  user_journey?: USER_JOURNEY;
};

export type UnlinkingUnsuccesfulEventType = {
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_EVENT;
  payload: UnlinkingUnsuccesfulPayload;
};

export const UnlinkingUnsuccesfulEvent = (
  payload: UnlinkingUnsuccesfulPayload = {},
): UnlinkingUnsuccesfulEventType => ({
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_EVENT,
  payload,
});

export type UnlinkingUnsuccesfulOpenType = {
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_OPEN;
  payload: UnlinkingUnsuccesfulPayload;
};

export const UnlinkingUnsuccesfulOpen = (
  payload: UnlinkingUnsuccesfulPayload = {},
): UnlinkingUnsuccesfulOpenType => ({
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_OPEN,
  payload,
});

export type UnlinkingUnsuccesfulGoBackClickType = {
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_CONTACT_CLICK;
  payload: UnlinkingUnsuccesfulPayload;
};

export const UnlinkingUnsuccesfulContactClick = (
  payload: UnlinkingUnsuccesfulPayload = {},
): UnlinkingUnsuccesfulGoBackClickType => ({
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_CONTACT_CLICK,
  payload,
});

export type UnlinkingUnsuccesfulTryAgainClickType = {
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_TRY_AGAIN_CLICK;
  payload: UnlinkingUnsuccesfulPayload;
};

export const UnlinkingUnsuccesfulTryAgainClick = (
  payload: UnlinkingUnsuccesfulPayload = {},
): UnlinkingUnsuccesfulTryAgainClickType => ({
  type: UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_TRY_AGAIN_CLICK,
  payload,
});
