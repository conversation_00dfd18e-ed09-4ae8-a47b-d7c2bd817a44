import { UberAnalyticsEvent } from '@analytics/enums';

export enum USER_JOURNEY {
  UBER_REAUTH = 'uber_reauth',
  UBER_INELIGIBILITY = 'uber_ineligibility',
  PARTNER_UNLINK = 'partner_unlink',
}

export type UnlinkAccountsPayload = {
  user_journey?: USER_JOURNEY;
};

export type UnlinkAccountsOpenType = {
  type: UberAnalyticsEvent.UNLINK_ACCOUNTS_MODAL_OPEN;
  payload: UnlinkAccountsPayload;
};

export const UnlinkAccountsModalOpen = (
  payload: UnlinkAccountsPayload = {},
): UnlinkAccountsOpenType => ({
  type: UberAnalyticsEvent.UNLINK_ACCOUNTS_MODAL_OPEN,
  payload,
});

export type UnlinkAccountsKeepUberPerksClickType = {
  type: UberAnalyticsEvent.UNLINK_ACCOUNTS_KEEP_UBER_CLICK;
  payload: UnlinkAccountsPayload;
};

export const UnlinkAccountsKeepUberClick = (
  payload: UnlinkAccountsPayload = {},
): UnlinkAccountsKeepUberPerksClickType => ({
  type: UberAnalyticsEvent.UNLINK_ACCOUNTS_KEEP_UBER_CLICK,
  payload,
});

export type UnlinkAccountsDisconnectAccountsClickType = {
  type: UberAnalyticsEvent.UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK;
  payload: UnlinkAccountsPayload;
};

export const UnlinkAccountsDisconnectAccountsClick = (
  payload: UnlinkAccountsPayload = {},
): UnlinkAccountsDisconnectAccountsClickType => ({
  type: UberAnalyticsEvent.UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK,
  payload,
});

export type UnlinkAccountSuccessSubscribeClickType = {
  type: UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_SUBSCRIBE_CLICK;
  payload: UnlinkAccountsPayload;
};

export const UnlinkAccountSuccessSubscribeClick = (
  payload: UnlinkAccountsPayload = {},
): UnlinkAccountSuccessSubscribeClickType => ({
  type: UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_SUBSCRIBE_CLICK,
  payload,
});

export type UnlinkAccountSuccessFindChargerClickType = {
  type: UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_FIND_CHARGER_CLICK;
  payload: UnlinkAccountsPayload;
};

export const UnlinkAccountSuccessFindChargerClick = (
  payload: UnlinkAccountsPayload = {},
): UnlinkAccountSuccessFindChargerClickType => ({
  type: UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_FIND_CHARGER_CLICK,
  payload,
});
