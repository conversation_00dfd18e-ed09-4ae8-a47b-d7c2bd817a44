import { UberAnalyticsEvent } from '@analytics/enums';

type WelcomeToUberPayload = {};

export type WelcomeToUberOpenType = {
  type: UberAnalyticsEvent.WELCOME_TO_UBER_OPEN;
  payload: WelcomeToUberPayload;
};

export const WelcomeToUberOpen = (
  payload: WelcomeToUberPayload = {},
): WelcomeToUberOpenType => ({
  type: UberAnalyticsEvent.WELCOME_TO_UBER_OPEN,
  payload,
});

export type WelcomeToUberFindNearbyChargerClickType = {
  type: UberAnalyticsEvent.WELCOME_TO_UBER_FIND_NEARBY_CHARGER_CLICK;
  payload: WelcomeToUberPayload;
};

export const WelcomeToUberFindNearbyChargerClick = (
  payload: WelcomeToUberPayload = {},
): WelcomeToUberFindNearbyChargerClickType => ({
  type: UberAnalyticsEvent.WELCOME_TO_UBER_FIND_NEARBY_CHARGER_CLICK,
  payload,
});

export type WelcomeToUberViewRewardsClickType = {
  type: UberAnalyticsEvent.WELCOME_TO_UBER_VIEW_REWARDS_CLICK;
  payload: WelcomeToUberPayload;
};

export const WelcomeToUberViewRewardsClick = (
  payload: WelcomeToUberPayload = {},
): WelcomeToUberViewRewardsClickType => ({
  type: UberAnalyticsEvent.WELCOME_TO_UBER_VIEW_REWARDS_CLICK,
  payload,
});
