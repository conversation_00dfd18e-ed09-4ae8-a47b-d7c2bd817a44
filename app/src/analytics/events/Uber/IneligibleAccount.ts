import { UberAnalyticsEvent } from '@analytics/enums';

export type IneligibleAccountOpenPayload = {};

export type IneligibleAccountOpenType = {
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_OPEN;
  payload: IneligibleAccountOpenPayload;
};

export const IneligibleAccountOpen = (
  payload: IneligibleAccountOpenPayload = {},
): IneligibleAccountOpenType => ({
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_OPEN,
  payload,
});

export type IneligibleAccountTryAgainClickType = {
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_TRYAGAIN_CLICK;
  payload: IneligibleAccountOpenPayload;
};

export const IneligibleAccountTryAgainClick = (
  payload: IneligibleAccountOpenPayload = {},
): IneligibleAccountTryAgainClickType => ({
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_TRYAGAIN_CLICK,
  payload,
});

export type IneligibleAccountUnlinkClickType = {
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_UNLINK_CLICK;
  payload: IneligibleAccountOpenPayload;
};

export const IneligibleAccountUnlinkClick = (
  payload: IneligibleAccountOpenPayload = {},
): IneligibleAccountUnlinkClickType => ({
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_UNLINK_CLICK,
  payload,
});

export type IneligibleAccountRetryLimitOpenType = {
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRYLIMIT_OPEN;
  payload: IneligibleAccountOpenPayload;
};

export const IneligibleAccountRetryLimitOpen = (
  payload: IneligibleAccountOpenPayload = {},
): IneligibleAccountRetryLimitOpenType => ({
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRYLIMIT_OPEN,
  payload,
});

export type IneligibleAccountRetryLimitSuccessType = {
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRY_SUCCESS;
  payload: IneligibleAccountOpenPayload;
};

export const IneligibleAccountRetrySuccess = (
  payload: IneligibleAccountOpenPayload = {},
): IneligibleAccountRetryLimitSuccessType => ({
  type: UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRY_SUCCESS,
  payload,
});
