import { UberAnalyticsEvent } from '@analytics/enums';

export enum USER_JOURNEY {
  UBER_REAUTH = 'uber_reauth',
  UBER_INELIGIBILITY = 'uber_ineligibility',
  PARTNER_UNLINK = 'partner_unlink',
}

export type ConnectingDisconnectingAccountsPayload = {
  user_journey?: USER_JOURNEY;
};

export type DisconnectingAccountsOpenType = {
  type: UberAnalyticsEvent.UNLINKING_ACCOUNTS_OPEN;
  payload: ConnectingDisconnectingAccountsPayload;
};

export const DisconnectingAccountsOpen = (
  payload: ConnectingDisconnectingAccountsPayload = {},
): DisconnectingAccountsOpenType => ({
  type: UberAnalyticsEvent.UNLINKING_ACCOUNTS_OPEN,
  payload,
});

export type DisconnectingAccountsSuccessfulOpenType = {
  type: UberAnalyticsEvent.UNLINING_ACCOUNTS_SUCCESSFUL_OPEN;
  payload: ConnectingDisconnectingAccountsPayload;
};

export const UnlinkingAccountsSuccessfulOpen = (
  payload: ConnectingDisconnectingAccountsPayload = {},
): DisconnectingAccountsSuccessfulOpenType => ({
  type: UberAnalyticsEvent.UNLINING_ACCOUNTS_SUCCESSFUL_OPEN,
  payload,
});

export type ConnectingAccountsSuccesfulOpenType = {
  type: UberAnalyticsEvent.CONNECTING_ACCOUNTS_SUCCESSFUL_OPEN;
  payload: ConnectingDisconnectingAccountsPayload;
};

export const ConnectingAccountsSuccesfulOpen = (
  payload: ConnectingDisconnectingAccountsPayload = {},
): ConnectingAccountsSuccesfulOpenType => ({
  type: UberAnalyticsEvent.CONNECTING_ACCOUNTS_SUCCESSFUL_OPEN,
  payload,
});
