import { TabsAnalyticsEvent } from '@analytics/enums';

type TabsAnalyticsEventActiveChargePayload = {};

export type TabsAnalyticsEventActiveChargeType = {
  type: TabsAnalyticsEvent.CHARGE_TAB_ACTIVE_CHARGE_CLICK;
  payload: TabsAnalyticsEventActiveChargePayload;
};

export const TabsAnalyticsEventActiveCharge = (
  payload: TabsAnalyticsEventActiveChargePayload = {},
): TabsAnalyticsEventActiveChargeType => ({
  type: TabsAnalyticsEvent.CHARGE_TAB_ACTIVE_CHARGE_CLICK,
  payload,
});
