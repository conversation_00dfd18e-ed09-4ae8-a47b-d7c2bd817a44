import { TabsAnalyticsEvent } from '@analytics/enums';

type TabsAnalyticsEventNoActiveChargePayload = {};

export type TabsAnalyticsEventNoActiveChargeType = {
  type: TabsAnalyticsEvent.CHARGE_TAB_NO_CHARGE_CLICK;
  payload: TabsAnalyticsEventNoActiveChargePayload;
};

export const TabsAnalyticsEventNoActiveCharge = (
  payload: TabsAnalyticsEventNoActiveChargePayload = {},
): TabsAnalyticsEventNoActiveChargeType => ({
  type: TabsAnalyticsEvent.CHARGE_TAB_NO_CHARGE_CLICK,
  payload,
});
