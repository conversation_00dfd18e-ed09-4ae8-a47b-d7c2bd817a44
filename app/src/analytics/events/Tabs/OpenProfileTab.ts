import { TabsAnalyticsEvent } from '@analytics/enums';

type TabsAnalyticsEventOpenProfileTabPayload = {};

export type TabsAnalyticsEventOpenProfileTabType = {
  type: TabsAnalyticsEvent.PROFILE_TAB_CLICK;
  payload: TabsAnalyticsEventOpenProfileTabPayload;
};

export const TabsAnalyticsEventOpenProfileTab = (
  payload: TabsAnalyticsEventOpenProfileTabPayload = {},
): TabsAnalyticsEventOpenProfileTabType => ({
  type: TabsAnalyticsEvent.PROFILE_TAB_CLICK,
  payload,
});
