import { TabsAnalyticsEvent } from '@analytics/enums';

type TabsAnalyticsEventOpenMapTabPayload = {};

export type TabsAnalyticsEventOpenMapTabType = {
  type: TabsAnalyticsEvent.MAP_TAB_CLICK;
  payload: TabsAnalyticsEventOpenMapTabPayload;
};

export const TabsAnalyticsEventOpenMapTab = (
  payload: TabsAnalyticsEventOpenMapTabPayload = {},
): TabsAnalyticsEventOpenMapTabType => ({
  type: TabsAnalyticsEvent.MAP_TAB_CLICK,
  payload,
});
