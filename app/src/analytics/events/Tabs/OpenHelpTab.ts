import { TabsAnalyticsEvent } from '@analytics/enums';

type TabsAnalyticsEventOpenHelpTabPayload = {};

export type TabsAnalyticsEventOpenHelpTabType = {
  type: TabsAnalyticsEvent.HELP_TAB_CLICK;
  payload: TabsAnalyticsEventOpenHelpTabPayload;
};

export const TabsAnalyticsEventOpenHelpTab = (
  payload: TabsAnalyticsEventOpenHelpTabPayload = {},
): TabsAnalyticsEventOpenHelpTabType => ({
  type: TabsAnalyticsEvent.HELP_TAB_CLICK,
  payload,
});
