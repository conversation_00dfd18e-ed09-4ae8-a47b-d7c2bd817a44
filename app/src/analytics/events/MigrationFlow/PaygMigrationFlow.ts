import { MigrationFlowAnalyticsEvent } from '@analytics/enums';

/**PayGMigrationScreen_Open */
type PayGMigrationScreenOpenPayload = { migration_status: string };
export type PayGMigrationScreenOpenType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATION_SCREEN_OPEN;
  payload: PayGMigrationScreenOpenPayload;
};

export const PayGMigrationScreenOpen = (
  payload: PayGMigrationScreenOpenPayload,
): PayGMigrationScreenOpenType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATION_SCREEN_OPEN,
  payload,
});

/**PayGMigrationScreen_Dismiss_Click */
type PayGMigrationScreenDismissClickPayload = { migration_status: string };

export type PayGMigrationScreenDismissClickType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_DISMISS_CLICK;
  payload: PayGMigrationScreenDismissClickPayload;
};

export const PayGMigrationScreenDismissClick = (
  payload: PayGMigrationScreenDismissClickPayload,
): PayGMigrationScreenDismissClickType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_DISMISS_CLICK,
  payload,
});

/**PayGMigrationScreen_UpdateAccount_Click*/
export type PayGMigrationScreenUpdateAccountClickPayload = {
  entered_value: string;
  migration_status: string;
};
export type PayGMigrationScreenUpdateAccountClickType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK;
  payload: PayGMigrationScreenUpdateAccountClickPayload;
};

export const PayGMigrationScreenUpdateAccountClick = (
  payload: PayGMigrationScreenUpdateAccountClickPayload,
): PayGMigrationScreenUpdateAccountClickType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK,
  payload,
});

/**PAYGMigrationScreen_OrderRFID_Click */
type PayGMigrationScreenOrderRFIDClickPayload = {};
export type PayGMigrationScreenOrderRFIDClickType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_ORDER_RFID_CLICK;
  payload: PayGMigrationScreenOrderRFIDClickPayload;
};

export const PayGMigrationScreenOrderRFIDClick = (
  payload: PayGMigrationScreenOrderRFIDClickPayload = {},
): PayGMigrationScreenOrderRFIDClickType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_ORDER_RFID_CLICK,
  payload,
});

/**PayGMigrationScreen_MoveToSUBS_Click*/
type PayGMigrationScreenMoveToSUBSClickPayload = {};
export type PayGMigrationScreenMoveToSUBSClickType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_MOVETOSUBS_CLICK;
  payload: PayGMigrationScreenMoveToSUBSClickPayload;
};

export const PayGMigrationScreenMoveToSUBSClick = (
  payload: PayGMigrationScreenMoveToSUBSClickPayload = {},
): PayGMigrationScreenMoveToSUBSClickType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_MOVETOSUBS_CLICK,
  payload,
});

/**PayGMigrationScreen_FAQ_Click */
type PayGMigrationScreenFAQClickPayload = { migration_status: string };
export type PayGMigrationScreenFAQClickType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_FAQ_CLICK;
  payload: PayGMigrationScreenFAQClickPayload;
};

export const PayGMigrationScreenFAQClick = (
  payload: PayGMigrationScreenFAQClickPayload,
): PayGMigrationScreenFAQClickType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATIONSCREEN_FAQ_CLICK,
  payload,
});

/**PayGMigration_ErrorScreen_Open*/
export type PayGMigrationErrorScreenOpenPayload = {
  error: string;
  migration_status: string;
};
export type PayGMigrationErrorScreenOpenType = {
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATION_ERRORSCREEN_OPEN;
  payload: PayGMigrationErrorScreenOpenPayload;
};

export const PayGMigrationErrorScreenOpen = (
  payload: PayGMigrationErrorScreenOpenPayload,
): PayGMigrationErrorScreenOpenType => ({
  type: MigrationFlowAnalyticsEvent.PAYG_MIGRATION_ERRORSCREEN_OPEN,
  payload,
});
