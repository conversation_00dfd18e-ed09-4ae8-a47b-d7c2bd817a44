import { MigrationFlowAnalyticsEvent } from '@analytics/enums';

/**SUBSMigrationScreen_Open */
type SUBSMigrationScreenOpenPayload = { migration_status: string };
export type SUBSMigrationScreenOpenType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATION_SCREEN_OPEN;
  payload: SUBSMigrationScreenOpenPayload;
};

export const SUBSMigrationScreenOpen = (
  payload: SUBSMigrationScreenOpenPayload,
): SUBSMigrationScreenOpenType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATION_SCREEN_OPEN,
  payload,
});

/**SUBSMigrationScreen_Dismiss_Click */
type SUBSMigrationScreenDismissPayload = { migration_status: string };

export type SUBSMigrationScreenDismissClickType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DISMISS_CLICK;
  payload: SUBSMigrationScreenDismissPayload;
};

export const SUBSMigrationScreenDismissClick = (
  payload: SUBSMigrationScreenDismissPayload,
): SUBSMigrationScreenDismissClickType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DISMISS_CLICK,
  payload,
});

/**SUBSMigrationScreen_UpdateAccount_Click*/
type SUBSMigrationScreenUpdateAccountClickPayload = {
  entered_value: string;
  migration_status: string;
};
export type SUBSMigrationScreenUpdateAccountClickType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK;
  payload: SUBSMigrationScreenUpdateAccountClickPayload;
};

export const SUBSMigrationScreenUpdateAccountClick = (
  payload: SUBSMigrationScreenUpdateAccountClickPayload,
): SUBSMigrationScreenUpdateAccountClickType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK,
  payload,
});

/**SUBSMIGRATIONSCREEN_KEEPSUBS_CLICK */
type SUBSMigrationScreenKeepSUBSClickPayload = {};
export type SUBSMigrationScreenKeepSUBSClickType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_KEEPSUBS_CLICK;
  payload: SUBSMigrationScreenKeepSUBSClickPayload;
};

export const SUBSMigrationScreenKeepSUBSClick = (
  payload: SUBSMigrationScreenKeepSUBSClickPayload = {},
): SUBSMigrationScreenKeepSUBSClickType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_KEEPSUBS_CLICK,
  payload,
});

/**SUBSMigrationScreen_DeclineSubs_Click*/
type SUBSMigrationScreenDeclineSubsClickPayload = {};
export type SUBSMigrationScreenDeclineSubsClickType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DECLINESUBS_CLICK;
  payload: SUBSMigrationScreenDeclineSubsClickPayload;
};

export const SUBSMigrationScreenDeclineSubsClick = (
  payload: SUBSMigrationScreenDeclineSubsClickPayload = {},
): SUBSMigrationScreenDeclineSubsClickType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DECLINESUBS_CLICK,
  payload,
});

/**SUBSMigrationScreen_FAQ_Click */
type SUBSMigrationScreenFAQClickPayload = { migration_status: string };
export type SUBSMigrationScreenFAQClickType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_FAQ_CLICK;
  payload: SUBSMigrationScreenFAQClickPayload;
};

export const SUBSMigrationScreenFAQClick = (
  payload: SUBSMigrationScreenFAQClickPayload,
): SUBSMigrationScreenFAQClickType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_FAQ_CLICK,
  payload,
});

/**SUBSMigration_ErrorScreen_Open*/
type SUBSMigrationErrorScreenOpenPayload = {
  error: string;
  migration_status: string;
};
export type SUBSMigrationErrorScreenOpenType = {
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATION_ERRORSCREEN_OPEN;
  payload: SUBSMigrationErrorScreenOpenPayload;
};

export const SUBSMigrationErrorScreenOpen = (
  payload: SUBSMigrationErrorScreenOpenPayload,
): SUBSMigrationErrorScreenOpenType => ({
  type: MigrationFlowAnalyticsEvent.SUBS_MIGRATION_ERRORSCREEN_OPEN,
  payload,
});
