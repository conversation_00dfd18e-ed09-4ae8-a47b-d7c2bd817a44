import { UberMigrationAnalyticsEvent } from '@analytics/enums';

export type UberMigrationScreenPayload = { migration_status: string };

export type UberMigrationScreenOpenType = {
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_OPEN;
  payload: UberMigrationScreenPayload;
};

export type UberMigrationScreenDismissClickType = {
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_DISMISS_CLICK;
  payload: UberMigrationScreenPayload;
};

export type UberMigrationScreenUpdateAccountClickType = {
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_UPDATE_ACCOUNT_CLICK;
  payload: UberMigrationScreenPayload;
};

export type UberMigrationScreenMoveToSubsClickType = {
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_MOVE_TO_SUBS_CLICK;
  payload: UberMigrationScreenPayload;
};

export const UberMigrationScreenOpen = (
  payload: UberMigrationScreenPayload,
): UberMigrationScreenOpenType => ({
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_OPEN,
  payload,
});

export const UberMigrationScreenDismissClick = (
  payload: UberMigrationScreenPayload,
): UberMigrationScreenDismissClickType => ({
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_DISMISS_CLICK,
  payload,
});

export const UberMigrationScreenUpdateAccountClick = (
  payload: UberMigrationScreenPayload,
): UberMigrationScreenUpdateAccountClickType => ({
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_UPDATE_ACCOUNT_CLICK,
  payload,
});

export const UberMigrationScreenMoveToSubsClick = (
  payload: UberMigrationScreenPayload,
): UberMigrationScreenMoveToSubsClickType => ({
  type: UberMigrationAnalyticsEvent.UBER_MIGRATION_SCREEN_MOVE_TO_SUBS_CLICK,
  payload,
});
