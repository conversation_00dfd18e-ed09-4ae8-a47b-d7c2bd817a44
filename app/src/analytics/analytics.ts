import { logger } from '@utils/logger';

import type { AnalyticsEventType } from './analytics.types';
import { AirshipAnalyticsService } from './handlers/airship';
import * as appsflyer from './handlers/appsflyer';
import * as firebase from './handlers/firebase';
import * as debugLogger from './handlers/logger';

/**
 * <PERSON><PERSON> logging a custom analytics event to all analytics platforms
 *
 * To ensure that the event tracking is not blocking of the main thread,
 * it would be better to implement the `EventEmitter` provided by
 * `react-native` or `node:events`; however, the `EventEmitter` is not
 * supported in the version of react native
 *
 * @param {AnalyticsEvent} event the event in the prescribed format
 */

export const analyticsEvent = async (event: AnalyticsEventType) => {
  Promise.all([
    debugLogger.analyticsEvent(event),
    appsflyer.analyticsEvent(event),
    firebase.analyticsEvent(event),
    AirshipAnalyticsService.analyticsEvent(event),
  ]).catch(e => {
    logger.log('Analytics error caught: ', e);
  });
};
