/**
 * This file operates under the assumption that imported values are rendered once and maintained in scope throughout the application's lifecycle.
 *
 * When an object, array, function, or any non-primitive value is imported, it is subject to mutation in place. This means that any modifications to the imported value will persist and become the subsequent value in any subsequent function or timer executions.
 *
 * It's important to be mindful of this behavior when working with imported values, as directly mutating them may lead to unintended side effects or unexpected behavior in the application.
 */

import { SupportedBrands } from '@bp/pulse-shared-types/lib/types/SupportedBrands';
import { SupportedCountries } from '@common/enums';
import remoteConfig from '@react-native-firebase/remote-config';
import { logger } from '@utils/logger';
import defaultsDeep from 'lodash/defaultsDeep';

import {
  BrandCountryConfig,
  PulseAppConfig,
  RemoteConfig,
} from './config.types';
import {
  DEFAULT_BRAND_CONFIG,
  DEFAULT_BRAND_VALUES,
  DEFAULT_CONFIG,
  DEFAULT_VALUES,
} from './defaults/config.default';

// Set the default remote config configuration
const remoteConfigSetDefaults = () =>
  remoteConfig().setDefaults({ ...DEFAULT_CONFIG, ...DEFAULT_BRAND_CONFIG });

export const fetchRemoteConfig = (forceUpdate: boolean) =>
  remoteConfig()
    // Set minimum fetch interval to 5 minutes, or 0 if forced update
    .setConfigSettings({
      minimumFetchIntervalMillis: forceUpdate ? 0 : 1000 * 60 * 5,
    })
    .then(remoteConfigSetDefaults)
    .then(() => remoteConfig().fetchAndActivate())
    .then(fetchedRemotely => {
      if (fetchedRemotely) {
        logger.log(
          'New configs were retrieved from the backend and activated.',
        );
      } else {
        logger.log(
          'No new configs were fetched from the backend, and the local configs were already activated',
        );
      }
      return fetchedRemotely;
    })
    .catch(e => {
      logger.log('Failed to fetch remote config with error: ', e);
      throw e;
    });

/**
 * Returns a parsed configuration object for the specified country
 * @param {SupportedCountries} country the country code to return config for
 * @returns {RemoteConfig} an object thats shaped like IRemoteConfig
 */
export const getConfig = async ({
  country,
  forceUpdate,
  authenticated,
  brand,
}: {
  country: SupportedCountries | null;
  forceUpdate: boolean;
  authenticated: boolean;
  brand: SupportedBrands;
}): Promise<PulseAppConfig> => {
  // Use GUEST config if not logged in
  const countryConfig = authenticated && country ? country : 'GUEST';
  const brandKey = brand.toUpperCase();
  try {
    // Fetch and activate remote config
    await fetchRemoteConfig(forceUpdate);

    // Assign config to variable
    const config = remoteConfig().getValue(countryConfig);
    const brandConfig = remoteConfig().getValue(brandKey);

    // Parse the config response
    const parsedCountryConfig = JSON.parse(config.asString());
    const parsedBrandConfig = JSON.parse(brandConfig.asString());

    // Parse the default config values
    const parsedDefaultConfig = DEFAULT_CONFIG[countryConfig]
      ? JSON.parse(DEFAULT_CONFIG[countryConfig])
      : {};
    const parsedDefaultBrandConfig = DEFAULT_BRAND_CONFIG[
      brandKey as keyof typeof DEFAULT_BRAND_CONFIG
    ]
      ? JSON.parse(
          DEFAULT_BRAND_CONFIG[brandKey as keyof typeof DEFAULT_BRAND_CONFIG],
        )
      : {};

    // Merge the configuration for the country
    const mergedCountryConfig = defaultsDeep(
      parsedCountryConfig,
      parsedDefaultConfig,
      DEFAULT_VALUES[countryConfig],
    );
    // Merge the configuration for the brand
    const mergedBrandConfig = defaultsDeep(
      parsedBrandConfig,
      parsedDefaultBrandConfig,
      DEFAULT_BRAND_VALUES[brandKey as keyof typeof DEFAULT_BRAND_VALUES],
    );

    // Return the configuration for the country and brand
    return {
      ...mergedCountryConfig,
      ...mergedBrandConfig,
    };
  } catch (e) {
    logger.log(
      'Error encountered with remote config, returning default config: ',
      e,
    );

    const defaultBrandConfig: BrandCountryConfig =
      DEFAULT_BRAND_VALUES[brandKey as keyof typeof DEFAULT_BRAND_VALUES];
    const defaultCountryConfig: RemoteConfig = DEFAULT_VALUES[countryConfig];
    return {
      ...defaultCountryConfig,
      ...defaultBrandConfig,
    };
  }
};
