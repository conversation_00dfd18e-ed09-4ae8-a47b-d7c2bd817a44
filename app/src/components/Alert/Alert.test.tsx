import { MockedProvider } from '@apollo/client/testing';
import { render } from '@testing-library/react-native';
import React from 'react';

import ThemeProvider from '../../providers/ThemeProvider';
import Alert, { IAlertType } from './Alert';

describe('Modal component', () => {
  it('renders default', () => {
    const types: (IAlertType | undefined)[] = ['Primary', 'Success', undefined];
    types.forEach(type => {
      const handlePress = jest.fn();
      const ModalTest = render(
        <ThemeProvider>
          <MockedProvider>
            <Alert
              type={type}
              title="Connection established"
              message="We’ve re-esbalished a connection, you can now start a charge from the app."
              closeModal={() => handlePress}
            />
          </MockedProvider>
        </ThemeProvider>,
      );
      expect(ModalTest.toJSON()).toMatchSnapshot();
    });
  });
});
