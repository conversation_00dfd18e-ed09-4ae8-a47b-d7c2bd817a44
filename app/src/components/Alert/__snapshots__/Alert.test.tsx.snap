// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Modal component renders default 1`] = `
<View
  collapsable={false}
  style={
    {
      "transform": [
        {
          "translateY": 3000,
        },
      ],
    }
  }
>
  <View
    importantForAccessibility="yes"
    style={
      {
        "bottom": 92,
        "paddingBottom": 12,
        "paddingLeft": 12,
        "paddingRight": 12,
        "paddingTop": 12,
        "position": "absolute",
        "width": "100%",
      }
    }
  >
    <View
      style={
        {
          "backgroundColor": "rgb(0,0,0)",
          "borderBottomLeftRadius": 3,
          "borderBottomRightRadius": 3,
          "borderColor": "rgb(0,0,0)",
          "borderStyle": "solid",
          "borderTopLeftRadius": 3,
          "borderTopRightRadius": 3,
          "borderWidth": 1,
          "elevation": 10,
          "overflow": "hidden",
          "paddingBottom": 12,
          "paddingLeft": 12,
          "paddingRight": 12,
          "paddingTop": 12,
          "shadowColor": "'rgb(0,0,0)'",
          "shadowOffset": {
            "height": 0,
            "width": 0,
          },
          "shadowOpacity": 0.11,
          "shadowRadius": 6,
          "width": "100%",
        }
      }
      type="Primary"
    >
      <View
        style={
          {
            "alignItems": "flex-end",
            "display": "flex",
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingRight": 12,
          }
        }
      >
        <Text
          style={
            {
              "color": "rgb(255,255,255)",
              "fontSize": 18,
              "lineHeight": 32,
            }
          }
          testID="Notifications.Title"
          type="Primary"
        >
          Connection established
        </Text>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "color": "white",
              "opacity": 1,
            }
          }
          testID="Notifications.CloseButton"
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="28px"
            bbWidth="28px"
            focusable={false}
            height="28px"
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 28,
                  "width": 28,
                },
              ]
            }
            vbHeight={24}
            vbWidth={24}
            width="28px"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                fillRule={0}
                propList={
                  [
                    "fill",
                    "fillRule",
                  ]
                }
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                  >
                    <RNSVGPath
                      d="M0 0H32V32H0z"
                      fill={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      fillOpacity={0}
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -4,
                          -4,
                        ]
                      }
                      propList={
                        [
                          "fill",
                          "fillOpacity",
                        ]
                      }
                    />
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "stroke",
                          "strokeLinecap",
                        ]
                      }
                      stroke={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      strokeLinecap={2}
                    >
                      <RNSVGPath
                        d="M12.6.6l-12 12m12 0L.6.6"
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            5.425000000000011,
                            5.424999999999997,
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
      <Text
        style={
          {
            "color": "rgb(255,255,255)",
            "fontFamily": "Roboto-Regular",
            "fontSize": 13,
            "lineHeight": 23,
            "paddingBottom": 0,
            "paddingLeft": 0,
            "paddingRight": 0,
            "paddingTop": 0,
          }
        }
        testID="Notifications.Message"
        type="Primary"
      >
        We’ve re-esbalished a connection, you can now start a charge from the app.
      </Text>
    </View>
  </View>
</View>
`;

exports[`Modal component renders default 2`] = `
<View
  collapsable={false}
  style={
    {
      "transform": [
        {
          "translateY": 3000,
        },
      ],
    }
  }
>
  <View
    importantForAccessibility="yes"
    style={
      {
        "bottom": 92,
        "paddingBottom": 12,
        "paddingLeft": 12,
        "paddingRight": 12,
        "paddingTop": 12,
        "position": "absolute",
        "width": "100%",
      }
    }
  >
    <View
      style={
        {
          "backgroundColor": "rgb(230, 255, 242)",
          "borderBottomLeftRadius": 3,
          "borderBottomRightRadius": 3,
          "borderColor": "rgb(46, 230, 140)",
          "borderStyle": "solid",
          "borderTopLeftRadius": 3,
          "borderTopRightRadius": 3,
          "borderWidth": 1,
          "elevation": 10,
          "overflow": "hidden",
          "paddingBottom": 12,
          "paddingLeft": 12,
          "paddingRight": 12,
          "paddingTop": 12,
          "shadowColor": "'rgb(0,0,0)'",
          "shadowOffset": {
            "height": 0,
            "width": 0,
          },
          "shadowOpacity": 0.11,
          "shadowRadius": 6,
          "width": "100%",
        }
      }
      type="Success"
    >
      <View
        style={
          {
            "alignItems": "flex-end",
            "display": "flex",
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingRight": 12,
          }
        }
      >
        <Text
          style={
            {
              "color": "rgb(17, 17, 17)",
              "fontSize": 18,
              "lineHeight": 32,
            }
          }
          testID="Notifications.Title"
          type="Success"
        >
          Connection established
        </Text>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "color": "white",
              "opacity": 1,
            }
          }
          testID="Notifications.CloseButton"
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="28px"
            bbWidth="28px"
            focusable={false}
            height="28px"
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 28,
                  "width": 28,
                },
              ]
            }
            vbHeight={24}
            vbWidth={24}
            width="28px"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                fillRule={0}
                propList={
                  [
                    "fill",
                    "fillRule",
                  ]
                }
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                  >
                    <RNSVGPath
                      d="M0 0H32V32H0z"
                      fill={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      fillOpacity={0}
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -4,
                          -4,
                        ]
                      }
                      propList={
                        [
                          "fill",
                          "fillOpacity",
                        ]
                      }
                    />
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "stroke",
                          "strokeLinecap",
                        ]
                      }
                      stroke={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      strokeLinecap={2}
                    >
                      <RNSVGPath
                        d="M12.6.6l-12 12m12 0L.6.6"
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            5.425000000000011,
                            5.424999999999997,
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
      <Text
        style={
          {
            "color": "rgb(17, 17, 17)",
            "fontFamily": "Roboto-Regular",
            "fontSize": 13,
            "lineHeight": 23,
            "paddingBottom": 0,
            "paddingLeft": 0,
            "paddingRight": 0,
            "paddingTop": 0,
          }
        }
        testID="Notifications.Message"
        type="Success"
      >
        We’ve re-esbalished a connection, you can now start a charge from the app.
      </Text>
    </View>
  </View>
</View>
`;

exports[`Modal component renders default 3`] = `
<View
  collapsable={false}
  style={
    {
      "transform": [
        {
          "translateY": 3000,
        },
      ],
    }
  }
>
  <View
    importantForAccessibility="yes"
    style={
      {
        "bottom": 92,
        "paddingBottom": 12,
        "paddingLeft": 12,
        "paddingRight": 12,
        "paddingTop": 12,
        "position": "absolute",
        "width": "100%",
      }
    }
  >
    <View
      style={
        {
          "backgroundColor": "rgb(0,0,0)",
          "borderBottomLeftRadius": 3,
          "borderBottomRightRadius": 3,
          "borderColor": "rgb(0,0,0)",
          "borderStyle": "solid",
          "borderTopLeftRadius": 3,
          "borderTopRightRadius": 3,
          "borderWidth": 1,
          "elevation": 10,
          "overflow": "hidden",
          "paddingBottom": 12,
          "paddingLeft": 12,
          "paddingRight": 12,
          "paddingTop": 12,
          "shadowColor": "'rgb(0,0,0)'",
          "shadowOffset": {
            "height": 0,
            "width": 0,
          },
          "shadowOpacity": 0.11,
          "shadowRadius": 6,
          "width": "100%",
        }
      }
      type="Primary"
    >
      <View
        style={
          {
            "alignItems": "flex-end",
            "display": "flex",
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingRight": 12,
          }
        }
      >
        <Text
          style={
            {
              "color": "rgb(255,255,255)",
              "fontSize": 18,
              "lineHeight": 32,
            }
          }
          testID="Notifications.Title"
          type="Primary"
        >
          Connection established
        </Text>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "color": "white",
              "opacity": 1,
            }
          }
          testID="Notifications.CloseButton"
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="28px"
            bbWidth="28px"
            focusable={false}
            height="28px"
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 28,
                  "width": 28,
                },
              ]
            }
            vbHeight={24}
            vbWidth={24}
            width="28px"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                fillRule={0}
                propList={
                  [
                    "fill",
                    "fillRule",
                  ]
                }
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                  >
                    <RNSVGPath
                      d="M0 0H32V32H0z"
                      fill={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      fillOpacity={0}
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -4,
                          -4,
                        ]
                      }
                      propList={
                        [
                          "fill",
                          "fillOpacity",
                        ]
                      }
                    />
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "stroke",
                          "strokeLinecap",
                        ]
                      }
                      stroke={
                        {
                          "payload": 4294967295,
                          "type": 0,
                        }
                      }
                      strokeLinecap={2}
                    >
                      <RNSVGPath
                        d="M12.6.6l-12 12m12 0L.6.6"
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            5.425000000000011,
                            5.424999999999997,
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
      <Text
        style={
          {
            "color": "rgb(255,255,255)",
            "fontFamily": "Roboto-Regular",
            "fontSize": 13,
            "lineHeight": 23,
            "paddingBottom": 0,
            "paddingLeft": 0,
            "paddingRight": 0,
            "paddingTop": 0,
          }
        }
        testID="Notifications.Message"
        type="Primary"
      >
        We’ve re-esbalished a connection, you can now start a charge from the app.
      </Text>
    </View>
  </View>
</View>
`;
