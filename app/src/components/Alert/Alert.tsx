import { Notifications } from '@bp/ui-components/mobile/core';
import React, { useEffect, useRef } from 'react';
import { Animated } from 'react-native';

export type IAlertType = 'Success' | 'Primary';

export type AlertModalProps = {
  type?: IAlertType;
  duration?: number;
  title: string;
  message: string;
  closeModal: () => void;
  accessibilityLabelText?: string;
  accessibilityHintText?: string;
};

const OFF_SCREEN = 3000;

const AlertModal = ({
  type = 'Primary',
  title,
  message,
  duration = 2000,
  closeModal,
  accessibilityLabelText,
  accessibilityHintText,
}: AlertModalProps) => {
  const translateY = useRef(new Animated.Value(OFF_SCREEN)).current;

  useEffect(() => {
    Animated.timing(translateY, {
      toValue: 0,
      duration,
      useNativeDriver: true,
    }).start();
  }, [duration, translateY]);

  return (
    <Animated.View style={{ transform: [{ translateY }] }}>
      <Notifications
        type={type}
        title={title}
        message={message}
        closeModal={closeModal}
        accessibilityLabelText={accessibilityLabelText}
        accessibilityHintText={accessibilityHintText}
      />
    </Animated.View>
  );
};

export default AlertModal;
