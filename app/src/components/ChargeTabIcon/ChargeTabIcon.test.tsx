import { MockedProvider } from '@apollo/client/testing';
import { render } from '@testing-library/react-native';
import React from 'react';

import ThemeProvider from '../../providers/ThemeProvider';
import ChargeTabIcon from './ChargeTabIcon';

describe('Charge Tab Icon component', () => {
  it('renders the charge tab icon without crashing', () => {
    const ChargeTabIconComponent = render(
      <ThemeProvider>
        <MockedProvider>
          <ChargeTabIcon isTabSelected={true} isCharging={false} />
        </MockedProvider>
      </ThemeProvider>,
    );

    expect(ChargeTabIconComponent.toJSON()).toMatchSnapshot();
  });

  it('Active charge animation should be visible', () => {
    const { getByTestId } = render(
      <ThemeProvider>
        <MockedProvider>
          <ChargeTabIcon isTabSelected={true} isCharging={true} />
        </MockedProvider>
      </ThemeProvider>,
    );

    expect(getByTestId('activeChargeAnimation')).toBeTruthy();
  });

  it('Active charge animation should be invisible', () => {
    const { queryByTestId } = render(
      <ThemeProvider>
        <MockedProvider>
          <ChargeTabIcon isTabSelected={true} isCharging={false} />
        </MockedProvider>
      </ThemeProvider>,
    );

    expect(queryByTestId('activeChargeAnimation')).toBeNull();
  });
});
