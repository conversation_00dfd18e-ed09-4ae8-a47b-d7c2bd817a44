import { ChargeIcon, ChargeIconSelected } from '@assets/images';
import React from 'react';

import ActiveChargeAnimation from '../ActiveChargeAnimation/ActiveChargeAnimation';
import * as S from './ChargeTabIcon.styles';

type ChargeTabIconProps = {
  isTabSelected: boolean;
  isCharging: boolean;
};

const ChargeTabIcon = ({ isTabSelected, isCharging }: ChargeTabIconProps) => {
  return (
    <S.ChargeTabStyle>
      {isTabSelected ? (
        <ChargeIconSelected width={24} height={24} />
      ) : (
        <ChargeIcon width={24} height={24} />
      )}

      {isCharging && (
        <S.AnimationContainer testID="activeChargeAnimation">
          <ActiveChargeAnimation width={20} progress={1} />
        </S.AnimationContainer>
      )}
    </S.ChargeTabStyle>
  );
};

export default ChargeTabIcon;
