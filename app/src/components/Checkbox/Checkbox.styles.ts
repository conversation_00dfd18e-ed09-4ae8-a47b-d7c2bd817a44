import styled from 'styled-components/native';

export const CheckBoxContainer = styled.View`
  flex-direction: row;
  margin-bottom: 16px;
  align-items: center;
`;

export const CheckBoxLabels = styled.Text`
  font-size: 12px;
  letter-spacing: 0.6px;
  font-family: 'Roboto-Regular';
  color: #111111b3;
  margin-left: 16px;
  line-height: 18px;
`;

export const HyperLink = styled.Text`
  font-size: 12px;
  letter-spacing: 0.6px;
  font-family: 'Roboto-Regular';
  color: #00009680;
  margin-left: 16px;
`;

export const CheckBoxLabelsFont14 = styled.Text`
  font-size: 14px;
  letter-spacing: 0.6px;
  font-family: 'Roboto-Regular';
  color: #111111d7;
  margin-left: 16px;
  line-height: 21px;
`;

export const MigrationHyperLinkStyle = styled.Text`
  font-size: 14px;
  letter-spacing: 0.6px;
  font-family: 'Roboto-Regular';
  color: #0064cc;
  text-decoration: underline;
  text-decoration-color: '#0064cc';
  margin-left: 21px;
`;
