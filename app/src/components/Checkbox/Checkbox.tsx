import { Checkbox as CheckboxUI } from '@bp/ui-components/mobile/core';
import React from 'react';
import { Linking } from 'react-native';

import * as S from './Checkbox.styles';

export type CheckboxContainerProps = {
  handleCheckboxPress: () => void;
  accessibilityLabelText: string;
  checkboxLabel: string;
  hyperLink?: string;
  hyperText?: string;
  disabled?: boolean;
  isMigrationScreen?: boolean;
  testID?: string;
};

export const Checkbox = ({
  handleCheckboxPress,
  accessibilityLabelText,
  checkboxLabel,
  hyperLink,
  hyperText,
  disabled = false,
  isMigrationScreen,
  testID,
}: CheckboxContainerProps) => {
  return (
    <S.CheckBoxContainer>
      <CheckboxUI
        disabled={disabled}
        onPress={handleCheckboxPress}
        accessibilityLabel={accessibilityLabelText}
        testID={testID}
      />
      {!isMigrationScreen ? (
        <S.CheckBoxLabels>
          {checkboxLabel}
          {hyperLink && (
            <S.HyperLink onPress={() => Linking.openURL(hyperLink)}>
              {hyperText}
            </S.HyperLink>
          )}
        </S.CheckBoxLabels>
      ) : (
        <S.CheckBoxLabelsFont14>
          {checkboxLabel}
          {hyperLink && (
            <S.MigrationHyperLinkStyle
              onPress={() => Linking.openURL(hyperLink)}>
              {hyperText}
            </S.MigrationHyperLinkStyle>
          )}
        </S.CheckBoxLabelsFont14>
      )}
    </S.CheckBoxContainer>
  );
};
