import LottieView from 'lottie-react-native';
import React, { memo } from 'react';

import activeChargeAnimation from './json/activeCharge.json';

interface IProps {
  width?: number;
  height?: number;
  autoplay?: boolean;
  loop?: boolean;
  progress?: number;
}

const ActiveChargeAnimation = (props: IProps) => {
  const {
    height = 20,
    width = 20,
    progress = 1,
    loop = true,
    autoplay = true,
  } = props;

  return (
    <LottieView
      style={{ width, height }}
      source={activeChargeAnimation}
      autoPlay={autoplay}
      loop={loop}
      progress={progress}
    />
  );
};

export default memo(ActiveChargeAnimation);
