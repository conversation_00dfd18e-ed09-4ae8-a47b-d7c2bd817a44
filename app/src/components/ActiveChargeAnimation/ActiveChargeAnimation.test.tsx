import { render } from '@testing-library/react-native';
import React from 'react';

import ThemeProvider from '../../providers/ThemeProvider';
import ActiveChargeAnimation from './ActiveChargeAnimation';

describe('Active Charge Animation component', () => {
  it(`renders the animation without crashing`, () => {
    const AnimationTest = render(
      <ThemeProvider>
        <ActiveChargeAnimation />
      </ThemeProvider>,
    );
    expect(AnimationTest.toJSON()).toMatchSnapshot();
  });
});
