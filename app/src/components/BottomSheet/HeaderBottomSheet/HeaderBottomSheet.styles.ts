import { PText } from '@bp/ui-components/mobile/core';
import styled from 'styled-components/native';

export const Header = styled.View`
  align-items: center;
  flex-direction: row;
  display: flex;
  padding: 28px 16px 16px;
`;

export const InputContainer = styled.View`
  width: 100%;
  padding: 0px 24px 16px 24px;
`;

export const TitleText = styled(PText)`
  width: 100%;
  font-size: 18px;
  letter-spacing: 1px;
  line-height: 24px;
  text-align: center;
  flex-wrap: wrap;
`;

export const CloseIcon = styled.TouchableOpacity`
  right: 24px;
`;
