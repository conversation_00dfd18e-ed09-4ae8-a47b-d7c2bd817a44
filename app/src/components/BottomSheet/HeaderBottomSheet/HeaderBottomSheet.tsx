import { CloseSign } from '@assets/images';
import { PTextType } from '@bp/ui-components/mobile/core';
import React from 'react';
import { useTranslation } from 'react-i18next';

import * as S from './HeaderBottomSheet.styles';

interface IProps {
  handleDismiss: VoidFunction;
  title: string;
}

const HeaderBottomSheet = ({ handleDismiss, title }: IProps) => {
  const { t } = useTranslation();

  return (
    <S.Header>
      <S.TitleText
        type={PTextType.LEAD}
        accessibilityLabel={title}
        testID="MinorOutageHeader">
        {title}
      </S.TitleText>
      <S.CloseIcon
        onPress={handleDismiss}
        testID="errorModalContentCloseButton"
        accessibilityLabel={t('misc.closeButton')}>
        <CloseSign color="#000" />
      </S.CloseIcon>
    </S.Header>
  );
};

export default HeaderBottomSheet;
