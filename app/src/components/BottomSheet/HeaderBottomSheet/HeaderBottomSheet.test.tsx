import React from 'react';

import { render } from '../../../utils/test-utils';
import HeaderBottomSheet from './HeaderBottomSheet';

describe('<HeaderBottomSheet />', () => {
  it('Should render properly', () => {
    const handleDismiss = jest.fn();
    const { getByTestId } = render(
      <HeaderBottomSheet title="Header" handleDismiss={handleDismiss} />,
    );

    const header = getByTestId('MinorOutageHeader');
    expect(header.children[0]).toEqual('Header');
  });
});
