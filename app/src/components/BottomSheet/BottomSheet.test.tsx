import {
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react-native';
import React, { ReactNode, Ref } from 'react';

import BottomSheet from './BottomSheet';

jest.mock('@bp/ui-components/mobile/core', () => {
  const { Text } = jest.requireActual('react-native');
  return {
    ...jest.requireActual('@bp/ui-components/mobile/core'),
    PText: Text,
    PTextType: {
      VALUE_V2: '',
    },
  };
});

jest.mock(
  '@bp/ui-components/mobile/core/components/molecules/ReanimatedBottomSheet/ReanimatedBottomSheet.js',
  () => {
    const ReactOriginal = jest.requireActual('react');
    const { View, Pressable } = jest.requireActual('react-native');

    return {
      __esModule: true,
      ...jest.requireActual(
        '@bp/ui-components/mobile/core/components/molecules/ReanimatedBottomSheet/ReanimatedBottomSheet.js',
      ),
      ReanimatedBottomSheetProvider: ({
        children,
      }: {
        children: ReactNode;
      }) => <>{children}</>,
      ReanimatedBottomSheetModal: ReactOriginal.forwardRef(
        (
          {
            children,
            onDismiss,
            headerComponent,
          }: {
            children: ReactNode;
            onDismiss: any;
            headerComponent: any;
          },
          ref: Ref<any>,
        ) => (
          <View innerRef={ref}>
            {children}
            {headerComponent()}
            <Pressable testID="handleBottomSheetDismiss" onPress={onDismiss} />
          </View>
        ),
      ),
    };
  },
);

const renderComponent = (
  content = {
    id: '123',
    enabled: true,
    heading: 'Heading',
    body: 'Body',
  },
  showBottomSheet = true,
  setDismissed = jest.fn(),
  snapPoints = '92%',
) => {
  render(
    <BottomSheet
      content={content}
      showBottomSheet={showBottomSheet}
      setDismissed={setDismissed}
      snapPoints={snapPoints}
    />,
  );
};

describe('<BottomSheet />', () => {
  describe('@content', () => {
    it('should display correct body content', async () => {
      renderComponent();

      await waitFor(() => {
        screen.getByText('Heading');
        screen.getByText('Body');
      });
    });
  });

  describe('@handleBottomSheetDismiss', () => {
    it('should call handleBottomSheetDismiss', async () => {
      const mockedDismissed = jest.fn();
      renderComponent(undefined, undefined, mockedDismissed);

      fireEvent.press(screen.getByTestId('handleBottomSheetDismiss'));

      await waitFor(() => {
        expect(mockedDismissed).toBeCalled();
      });
    });
  });
});
