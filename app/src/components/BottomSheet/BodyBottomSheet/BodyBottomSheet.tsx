import { NotificationIcon } from '@assets/images';
import { PTextType, Spacer } from '@bp/ui-components/mobile/core';
import React from 'react';

import * as S from './BodyBottomSheet.styles';

interface IModal {
  body?: string;
}

const BodyBottomSheet = ({ body }: IModal) => {
  return (
    <S.InputContainer>
      <S.IconContainer>
        <NotificationIcon width="96" height="96" />
      </S.IconContainer>
      <Spacer vSpace={30} />
      <S.Content
        type={PTextType.VALUE_V2}
        accessibilityLabel={body}
        testID="MinorOutageBody">
        {body}
      </S.Content>
      <Spacer vSpace={30} />
    </S.InputContainer>
  );
};

export default BodyBottomSheet;
