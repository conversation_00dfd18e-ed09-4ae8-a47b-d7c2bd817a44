import {
  ReanimatedBottomSheetBackdrop,
  ReanimatedBottomSheetFooter,
  ReanimatedBottomSheetModal,
  ReanimatedBottomSheetModalRef,
  ReanimatedBottomSheetProvider,
} from '@bp/ui-components/mobile/core';
import { ReanimatedBottomSheetFooterProps } from '@bp/ui-components/mobile/core/components/molecules/ReanimatedBottomSheet/types';
import React, { useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { BodyBottomSheet, FooterBottomSheet, HeaderBottomSheet } from './index';

interface Content {
  id: string;
  enabled: boolean;
  heading: string;
  body: string;
  buttonText?: string;
}

interface IProps {
  content: Content;
  showBottomSheet: boolean;
  setDismissed: VoidFunction;
  snapPoints: string;
}

const BottomSheet = ({
  content,
  showBottomSheet,
  setDismissed,
  snapPoints,
}: IProps) => {
  const { t } = useTranslation();
  const bottomSheetRef = useRef<ReanimatedBottomSheetModalRef>(null);

  const handleBottomSheetDismiss = useCallback(() => {
    setDismissed();
    bottomSheetRef.current?.close();
  }, [setDismissed]);

  const renderHeaderComponent = useCallback(
    () => (
      <HeaderBottomSheet
        title={content?.heading}
        handleDismiss={handleBottomSheetDismiss}
      />
    ),
    [content?.heading, handleBottomSheetDismiss],
  );

  const renderFooterComponent = useCallback(
    (props: JSX.IntrinsicAttributes & ReanimatedBottomSheetFooterProps) => (
      <ReanimatedBottomSheetFooter {...props}>
        <FooterBottomSheet
          buttonText={content?.buttonText || t('misc.closeButton')}
          handleDismiss={handleBottomSheetDismiss}
        />
      </ReanimatedBottomSheetFooter>
    ),
    [content?.buttonText, t, handleBottomSheetDismiss],
  );

  useEffect(() => {
    if (showBottomSheet === true) {
      bottomSheetRef.current?.present();
    }
  }, [showBottomSheet]);

  return (
    <ReanimatedBottomSheetProvider>
      <ReanimatedBottomSheetModal
        snapPoints={[snapPoints]}
        index={0}
        name="BottomSheetComponent"
        onDismiss={handleBottomSheetDismiss}
        headerComponent={renderHeaderComponent}
        footerComponent={renderFooterComponent}
        backdropComponent={ReanimatedBottomSheetBackdrop}
        ref={bottomSheetRef}>
        <BodyBottomSheet body={content?.body} />
      </ReanimatedBottomSheetModal>
    </ReanimatedBottomSheetProvider>
  );
};

export default BottomSheet;
