import React from 'react';

import { render, screen } from '../../../utils/test-utils';
import FooterBottomSheet from './FooterBottomSheet';

describe('<FooterBottomSheet />', () => {
  it('Should render properly', () => {
    const handleDismiss = jest.fn();
    const renderWithTheme = () =>
      render(
        <FooterBottomSheet buttonText="Close" handleDismiss={handleDismiss} />,
      );
    renderWithTheme();
    const button = screen.getByRole('button');
    expect(button).toBeDefined();
  });
});
