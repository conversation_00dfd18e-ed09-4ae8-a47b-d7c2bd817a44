import { Button } from '@bp/ui-components/mobile/core';
import React from 'react';

import * as S from './FooterBottomSheet.styles';

interface IProps {
  handleDismiss: VoidFunction;
  buttonText?: string;
}

const FooterBottomSheet = ({ handleDismiss, buttonText }: IProps) => (
  <S.ButtonContainer>
    <Button
      onPress={handleDismiss}
      accessibilityLabel={buttonText}
      testID="MinorOutageFooter">
      {buttonText}
    </Button>
  </S.ButtonContainer>
);

export default FooterBottomSheet;
