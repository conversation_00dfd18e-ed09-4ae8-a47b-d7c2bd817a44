import { SupportedCountries } from '@common/enums';
import { SupportedHelpConfigCountries } from '@screens/Help/countryConfig';

interface CountryArrayIprops {
  countryIcon: string;
  countryName: string;
  countryCode: SupportedHelpConfigCountries;
}

export const CountryArray: CountryArrayIprops[] = [
  {
    countryIcon: '🇳🇱',
    countryName: 'Netherlands',
    countryCode: SupportedCountries.NL,
  },
  {
    countryIcon: '🇬🇧',
    countryName: 'United Kingdom',
    countryCode: SupportedCountries.UK,
  },
  {
    countryIcon: '🇪🇸',
    countryName: 'Spain',
    countryCode: SupportedCountries.ES,
  },
  {
    countryIcon: '🇩🇪',
    countryName: 'Germany',
    countryCode: SupportedCountries.DE,
  },
  {
    countryIcon: '🇺🇸',
    countryName: 'United States',
    countryCode: SupportedCountries.US,
  },
];

export type CountrySelectionCardViewProps = {
  selectedCountry: SupportedHelpConfigCountries | null;
  setSelectedCountry: (
    choosenCountry: SupportedHelpConfigCountries | null,
  ) => void;
  accessibilityLabel: string;
  accessibilityHint?: string;
};
