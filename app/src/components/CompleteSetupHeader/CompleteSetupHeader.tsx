import { CloseSign } from '@assets/images';
import { t } from 'i18next';
import React from 'react';
import { TouchableOpacity } from 'react-native';

import * as S from './CompleteSetupHeader.styles';

type CompleteSetupHeaderProps = {
  onClosePress: () => void;
};

const CompleteSetupHeader = ({ onClosePress }: CompleteSetupHeaderProps) => {
  return (
    <S.TopContainer>
      <S.Header>
        <S.HeaderTitle>{t('completeSetUp.title')}</S.HeaderTitle>
        <TouchableOpacity
          testID="CompleteSetupHeader.CloseButton"
          onPress={onClosePress}>
          <CloseSign />
        </TouchableOpacity>
      </S.Header>
      <S.SubTitle>{t('completeSetUp.subtitle')}</S.SubTitle>

      <S.Description>{t('completeSetUp.description')}</S.Description>
    </S.TopContainer>
  );
};

export default CompleteSetupHeader;
