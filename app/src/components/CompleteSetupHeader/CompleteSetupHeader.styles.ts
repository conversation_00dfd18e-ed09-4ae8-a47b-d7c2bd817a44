import styled from 'styled-components/native';

export const TopContainer = styled.View`
  flex: 1;
`;

export const Header = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 72px;
  align-items: center;
`;

export const HeaderTitle = styled.Text`
  font-size: 20px;
  text-align: center;
  color: #111111;
  letter-spacing: 0.15px;
  font-family: 'Roboto-Regular';
  margin-left: auto;
  margin-right: auto;
  line-height: 30px;
`;

export const SubTitle = styled.Text`
  letter-spacing: 0.1px;
  line-height: 28px;
  text-align: center;
  font-size: 23px;
  margin-bottom: 8px;
`;

export const Description = styled.Text`
  text-align: center;
  font-size: 16px;
  letter-spacing: 0.1px;
  color: #111111;
  margin-bottom: 30px;
  font-family: 'Roboto-Regular';
  line-height: 28px;
`;
