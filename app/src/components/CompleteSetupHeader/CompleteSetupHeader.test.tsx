import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';

import ThemeProvider from '../../providers/ThemeProvider';
import CompleteSetupHeader from './CompleteSetupHeader';

describe('<CompleteSetupHeader />', () => {
  describe('@onClosePress', () => {
    it('should trigger close function', () => {
      const mocked = jest.fn();
      render(
        <ThemeProvider>
          <CompleteSetupHeader onClosePress={mocked} />
        </ThemeProvider>,
      );
      fireEvent.press(screen.getByTestId('CompleteSetupHeader.CloseButton'));
      expect(mocked).toBeCalled();
    });
  });
});
