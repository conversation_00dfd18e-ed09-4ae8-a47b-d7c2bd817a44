import { AlertIcon, ChevronLeft } from '@assets/images';
import {
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react-native';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';

import Banner from './Banner';

const mockuseAuth = jest.fn();
jest.mock('@bp/pulse-auth-sdk', () => ({
  useAuth: jest.fn(),
}));

mockuseAuth.mockImplementation(() => ({
  authenticated: true,
  user: {
    address: {},
  },
}));

jest.mock('react-native/Libraries/Utilities/Platform', () => {
  let platform = {
    OS: 'ios',
    select: () => {},
  };

  const select = jest.fn().mockImplementation(obj => {
    const value = obj[platform.OS];
    return !value ? obj.default : value;
  });

  platform.select = select;

  return platform;
});

describe('<Banner />', () => {
  const { t } = useTranslation();
  it('renders Banner Component on iOS', async () => {
    Platform.OS = 'ios';
    render(
      <Banner
        leftIcon={<AlertIcon />}
        title={t('map.SoftOnboardingBanner.header')}
        rightIcon={<ChevronLeft />}
      />,
    );

    waitFor(() => {
      expect(screen.getByTestId('banner')).toBeDefined();
    });
  });

  it('renders Banner Component on Android', async () => {
    Platform.OS = 'android';
    render(
      <Banner
        leftIcon={<AlertIcon />}
        title={t('map.SoftOnboardingBanner.header')}
        rightIcon={<ChevronLeft />}
      />,
    );

    waitFor(() => {
      expect(screen.getByTestId('banner')).toBeDefined();
    });
  });

  it('renders Banner Component on Profile screen on Android device', async () => {
    Platform.OS = 'android';
    const mockNavigateOnPress = jest.fn();
    const { getByTestId } = render(
      <Banner
        leftIcon={<AlertIcon />}
        title={t('map.SoftOnboardingBanner.header')}
        rightIcon={<ChevronLeft />}
        navigateOnPress={mockNavigateOnPress}
      />,
    );

    const banner = getByTestId('banner');
    fireEvent(banner, 'touchEnd');

    waitFor(() => {
      expect(banner).toBeDefined();
      expect(mockNavigateOnPress).toHaveBeenCalled();
    });
  });

  it('renders Banner Component on Profile screen on ios device', async () => {
    Platform.OS = 'ios';
    render(
      <Banner
        leftIcon={<AlertIcon />}
        title={t('map.SoftOnboardingBanner.header')}
        rightIcon={<ChevronLeft />}
      />,
    );

    waitFor(() => {
      expect(screen.getByTestId('banner')).toBeDefined();
    });
  });
});
