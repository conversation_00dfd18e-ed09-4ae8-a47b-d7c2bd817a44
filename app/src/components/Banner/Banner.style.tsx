import styled from 'styled-components/native';

export const Banner = styled.View`
  width: 100%;
`;

export const Inner = styled.View`
  background: #ffee5c;
  padding: 12px;
  align-items: center;
  flex-direction: row;
`;

export const Title = styled.Text`
  font-size: 16px;
  color: #111111;
  letter-spacing: 0.15px;
  font-family: 'Roboto-Regular';
  margin-left: auto;
  margin-right: auto;
  line-height: 28px;
`;

export const Icon = styled.View`
  font-size: 24px;
  line-height: 24px;
`;

export const RightArrow = styled.View`
  font-size: 24px;
  line-height: 24px;
  transform: 'rotate(90deg)';
`;
