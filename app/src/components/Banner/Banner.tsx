import React from 'react';

import * as S from './Banner.style';

type BannerProps = {
  leftIcon?: React.ReactNode;
  title: string;
  rightIcon?: React.ReactNode;
  navigateOnPress?: () => void;
};

const Banner = ({
  leftIcon,
  title,
  rightIcon,
  navigateOnPress,
}: BannerProps) => {
  return (
    <S.Banner testID="banner" onTouchEnd={navigateOnPress}>
      <S.Inner>
        {leftIcon && <S.Icon>{leftIcon}</S.Icon>}
        <S.Title>{title}</S.Title>
        {rightIcon && <S.Icon>{rightIcon}</S.Icon>}
      </S.Inner>
    </S.Banner>
  );
};

export default Banner;
