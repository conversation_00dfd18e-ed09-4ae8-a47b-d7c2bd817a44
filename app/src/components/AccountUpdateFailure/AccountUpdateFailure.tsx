import { useAuth } from '@bp/pulse-auth-sdk';
import { ActionConfirmationModal } from '@bp/ui-components/mobile/core/components/molecules';
import { navigate } from '@utils/navigation';
import React from 'react';
import { useTranslation } from 'react-i18next';

export type AccountUpdateFailureProps = {
  isModalVisible: boolean;
  setAccountUpdateStatus: (status: boolean) => void;
};

export const AccountUpdateFailure = ({
  isModalVisible,
  setAccountUpdateStatus,
}: AccountUpdateFailureProps) => {
  const { t } = useTranslation();

  const { logout } = useAuth();

  const handleCloseModalOnPress = () => {
    logout();
    setAccountUpdateStatus(false);
    navigate('Profile', {}, false);
  };

  const handleTryAgainModalOnPress = () => {
    setAccountUpdateStatus(false);
  };

  return (
    <ActionConfirmationModal
      isVisible={isModalVisible}
      titleText={t('accountUpdate.title')}
      primaryButtonText={t('accountUpdate.tryAgain')}
      primaryButtonOnPress={handleTryAgainModalOnPress}
      titleMessage={t('accountUpdate.description')}
      secondaryButtonText={t('accountUpdate.logout')}
      secondaryButtonOnPress={handleCloseModalOnPress}
    />
  );
};
