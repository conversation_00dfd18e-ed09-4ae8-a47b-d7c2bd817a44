import { useAuth } from '@bp/pulse-auth-sdk';
import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';
import { useTranslation } from 'react-i18next';

import ThemeProvider from '../../providers/ThemeProvider';
import { AccountUpdateFailure } from './AccountUpdateFailure';

const mockLogout = jest.fn();

// Mock auth
jest.mock('@bp/pulse-auth-sdk', () => ({
  useAuth: jest.fn(),
}));

const mockedUseAuth = jest.mocked(useAuth);

describe('<AccountUpdateFailure />', () => {
  const mockAccountUpdateCallback = jest.fn();
  const { t } = useTranslation();
  const { getByText } = screen;

  it('@LogoutButton Visibility', async () => {
    mockedUseAuth.mockImplementation(
      () =>
        ({
          logout: mockLogout,
        } as any),
    );

    render(
      <ThemeProvider>
        <AccountUpdateFailure
          isModalVisible={true}
          setAccountUpdateStatus={mockAccountUpdateCallback}
        />
      </ThemeProvider>,
    );

    const logoutButton = await screen.getByText(t('accountUpdate.logout'));

    expect(logoutButton).toBeDefined();

    fireEvent.press(logoutButton);
    expect(mockLogout).toHaveBeenCalled();
  });

  it('@TryagainButton Visibility', async () => {
    render(
      <ThemeProvider>
        <AccountUpdateFailure
          isModalVisible={true}
          setAccountUpdateStatus={mockAccountUpdateCallback}
        />
      </ThemeProvider>,
    );

    const tryAgainButton = await screen.getByText(t('accountUpdate.tryAgain'));

    expect(tryAgainButton).toBeDefined();

    fireEvent.press(tryAgainButton);
    expect(mockAccountUpdateCallback).toHaveBeenCalled();
  });

  it('@ModalInvisibility', () => {
    render(
      <ThemeProvider>
        <AccountUpdateFailure
          isModalVisible={false}
          setAccountUpdateStatus={mockAccountUpdateCallback}
        />
      </ThemeProvider>,
    );

    expect(() => getByText(t('accountUpdate.tryAgain'))).toThrow();
    expect(() => getByText(t('accountUpdate.logout'))).toThrow();
  });
});
