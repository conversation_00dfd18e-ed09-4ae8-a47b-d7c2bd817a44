// @ts-nocheck
import { useCharge } from '@bp/charge-mfe';
import { render, screen } from '@testing-library/react-native';
import React from 'react';

import { useConnectivity } from '../../providers/ConnectivityProvider';
import Notifications from './Notifications';

jest.mock('@bp/charge-mfe', () => ({
  useCharge: jest.fn(() => ({ isCharging: false })),
}));

jest.mock('../../providers/ConnectivityProvider', () => ({
  useConnectivity: jest.fn(() => ({ isInternetReachable: true })),
}));

describe('<Notifications />', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders nothing if there is no network interruption', () => {
    render(<Notifications />);

    const toast = screen.queryByTestId('toast');
    expect(toast).toBeNull(); // it doesn't exist
  });

  it('renders a simple notification if there is no internet connection', () => {
    useConnectivity.mockImplementation(() => ({ isInternetReachable: false }));

    render(<Notifications />);
    const toast = screen.queryByTestId('toast');

    expect(toast).toBeDefined();
  });

  it('renders a special notification if there is no internet connection, and a charge in progress', () => {
    useConnectivity.mockImplementation(() => ({ isInternetReachable: false }));
    useCharge.mockImplementation(() => ({ isCharging: true }));
    render(<Notifications />);

    expect(screen.queryByTestId('toast')).toBeDefined();
    expect(screen.queryByText('notification.text')).toBeDefined();
  });
});
