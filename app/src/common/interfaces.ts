import { SupportedBrands } from '@bp/pulse-shared-types/lib/types/SupportedBrands';
import { SupportedCountries } from '@common/enums';

export type GuestCountry = 'GUEST';

export type HomeCountries = SupportedCountries | GuestCountry;

export type PaymentMethod =
  | 'NEW'
  | 'SUBS'
  | 'PAYG'
  | 'PAYG-WALLET'
  | 'SUBS-WALLET';

export type SupportedBrandKeys = Uppercase<SupportedBrands>;
export interface IRowWithData {
  title: string;
  icon: any;
  subTitle: string;
  linkText?: string;
  linkUrl?: string;
}
