import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@utils/logger';

import { getUserInfo } from './asyncStorage';

describe('common.asyncStorage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('getUserInfo should invoke AsyncStorage and return a correctly formatted object', async () => {
    // Cast fn to get around typescript error
    (AsyncStorage.multiGet as jest.Mock).mockResolvedValue([
      ['@country', 'UK'],
      ['@user_id', '00BCWacQAH'],
      ['@locale', 'en_GB'],
      ['@user_scheme', null],
      ['@user_type', 'CSP_LITE_PORTAL'],
    ]);

    const result = await getUserInfo();

    expect(AsyncStorage.multiGet).toBeCalledWith([
      '@country',
      '@locale',
      '@user_id',
      '@user_scheme',
      '@user_type',
      '@partner_type',
      '@user_migration_date',
    ]);
    expect(result).toMatchObject({
      country: 'UK',
      userId: '00BCWacQAH',
      locale: 'en_GB',
      userScheme: null,
      userType: 'CSP_LITE_PORTAL',
    });
  });

  test('getUserInfo should log an error, if fails', async () => {
    const logSpy = jest.spyOn(logger, 'warn').mockImplementation(() => null);
    (AsyncStorage.multiGet as jest.Mock) = jest
      .fn()
      .mockRejectedValue(new Error());

    await getUserInfo();

    expect(logSpy).toHaveBeenCalled();
  });
});
