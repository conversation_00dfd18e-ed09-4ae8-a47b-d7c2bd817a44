import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@utils/logger';

/**
 * Return a formatted userInfo object from
 * data stored in AsyncStorage
 *
 */

export const getUserInfo = async () => {
  try {
    const values = await AsyncStorage.multiGet([
      '@country',
      '@locale',
      '@user_id',
      '@user_scheme',
      '@user_type',
      '@partner_type',
      '@user_migration_date',
    ]);
    const userInfo = values.map(([key, value]) => {
      const propName = key
        .replace('@', '')
        .replace(/_(\w)/g, (match, p1) => p1.toUpperCase());
      return [propName, value];
    });
    return Object.fromEntries(userInfo);
  } catch (e) {
    logger.warn('Error in getUserInfo: ', e);
    return null;
  }
};

export const setAsyncStorageItem = (key: string, value: string | undefined) => {
  AsyncStorage.setItem(key, value || '');
};
