import { ChargeScreenNames } from '@bp/charge-mfe/dist/common/enums';
import { ProfileScreenNames } from '@bp/profile-mfe/dist/common/enums';

export enum MenuOptions {
  Landing = 'ProfileLanding',
  ChargeActivity = 'ChargeActivity',
  Details = 'YourDetails',
  Settings = 'Settings',
  Feedback = 'Feedback',
  RFID = 'RFID',
  Wallet = 'Wallet',
}

export enum SupportedCountries {
  UK = 'UK',
  NL = 'NL',
  DE = 'DE',
  ES = 'ES',
  US = 'US',
}

export enum ImageType {
  PNG,
  SVG,
}

export enum SupportedUserTypes {
  NEW = 'NEW',
  GUEST = 'GUEST',
  SUBS = 'SUBS',
  'PAYG-WALLET' = 'PAYG-Wallet',
  'SUBS-WALLET' = 'SUBS-WALLET',
  'PAYG' = 'PAYG',
}

export enum ChargepointSchemeId {
  POLAR = 16,
  BMW = 92,
  UBER = 1567,
  ROYAL_MAIL = 2271,
}

export enum SupportedPartner {
  UBER = 'Uber',
  ADAC = 'ADAC',
}

export enum ChargepointSchemeName {
  UBER = 'bp Hub – Uber	',
  ROYAL_MAIL = 'View_WP_Royal Mail',
  BMW = 'BMW',
  POLAR = 'Polar',
}

export enum ConnectorType {
  CCS1 = 'CCS1',
  CCS2 = 'CCS2',
  CHADEMO = 'CHADEMO',
  COMMANDO = 'COMMANDO',
  DOMESTIC_E = 'DOMESTIC_E',
  DOMESTIC_G = 'DOMESTIC_G',
  DOMESTIC_J = 'DOMESTIC_J',
  SCHUKO = 'SCHUKO',
  TESLA = 'TESLA',
  TYPE_1 = 'TYPE_1',
  TYPE_2 = 'TYPE_2',
  TYPE_3A = 'TYPE_3A',
  TYPE_3C = 'TYPE_3C',
  UNKNOWN = 'UNKNOWN',
}

export enum ScreenNamesHavingBanner {
  Profile = ProfileScreenNames.Profile,
  SelectConnector = ChargeScreenNames.SelectConnector,
  SerialSearch = ChargeScreenNames.SerialSearch,
}

export enum TokenStatus {
  INVALID = 'INVALID',
  INACTIVE = 'INACTIVE',
  REAUTH = 'REAUTH',
}

export enum ChargeScreenNamesToNavigate {
  SelectConnector = 'ChargeMFE.SelectConnector',
  ConnectVehicle = 'ChargeMFE.ConnectVehicle',
}
