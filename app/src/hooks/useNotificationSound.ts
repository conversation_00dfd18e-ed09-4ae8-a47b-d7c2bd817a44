import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import PushNotification from 'react-native-push-notification';

import {
  getSoundFileName,
  NOTIFICATION_SOUND_CONFIG,
} from '../config/notificationSound';
import { logger } from '../utils/logger';

interface UseNotificationSoundOptions {
  soundFileName?: string;
  enabled?: boolean;
}

interface UseNotificationSoundReturn {
  playSound: () => void;
  isLoaded: boolean;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Custom hook for playing notification sounds
 * @param options Configuration options for the sound
 * @returns Object with playSound function and loading state
 */
export const useNotificationSound = (
  options: UseNotificationSoundOptions = {},
): UseNotificationSoundReturn => {
  const {
    soundFileName = getSoundFileName(),
    enabled = NOTIFICATION_SOUND_CONFIG.DEFAULTS.ENABLED,
  } = options;

  const [isLoaded, setIsLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);

  // Helper function to play sound on iOS
  const playIOSSound = useCallback(() => {
    PushNotificationIOS.addNotificationRequest({
      id: `sound_${Date.now()}`,
      title: '', // Silent notification
      body: '',
      sound: soundFileName,
      badge: 0,
      userInfo: {},
    });
  }, [soundFileName]);

  // Helper function to play sound on Android
  const playAndroidSound = useCallback(() => {
    PushNotification.localNotification({
      title: '', // Silent notification
      message: '',
      playSound: true,
      soundName: soundFileName,
      autoCancel: true,
      smallIcon: 'ic_notification',
      vibrate: false,
      ongoing: false,
      priority: 'min', // Minimal priority to avoid showing notification
      visibility: 'secret', // Don't show on lock screen
      importance: 'min', // Minimal importance
      channelId: NOTIFICATION_SOUND_CONFIG.ANDROID.CHANNEL_ID, // Use existing channel
    });
  }, [soundFileName]);

  // Helper function to log successful sound playback
  const logSoundPlayback = useCallback(() => {
    if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
      logger.info(
        `[NotificationSound] Notification sound played: ${soundFileName} (${Platform.OS})`,
      );
    }
  }, [soundFileName]);

  // Helper function to log sound playback errors
  const logSoundError = useCallback((error: unknown) => {
    if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
      logger.warn(
        '[NotificationSound] Failed to play notification sound:',
        error,
      );
    }
  }, []);

  // Helper function to log skipped sound playback
  const logSoundSkipped = useCallback(() => {
    const shouldLogVerbose =
      NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED &&
      NOTIFICATION_SOUND_CONFIG.LOGGING.VERBOSE;

    if (shouldLogVerbose) {
      logger.info(
        `[NotificationSound:Verbose] Sound playback skipped - loaded: ${isLoaded}, enabled: ${isEnabled}`,
      );
    }
  }, [isLoaded, isEnabled]);

  // Play notification sound with error handling
  // Note: This creates silent local notifications that only play sound
  // without interfering with Airship's push notification handling
  const playSound = useCallback(() => {
    if (!isLoaded || !isEnabled) {
      logSoundSkipped();
      return;
    }

    try {
      if (Platform.OS === 'ios') {
        playIOSSound();
      } else {
        playAndroidSound();
      }
      logSoundPlayback();
    } catch (error) {
      logSoundError(error);
    }
  }, [
    isLoaded,
    isEnabled,
    playIOSSound,
    playAndroidSound,
    logSoundPlayback,
    logSoundError,
    logSoundSkipped,
  ]);

  // Initialize sound configuration without interfering with Airship
  useEffect(() => {
    const initializeSoundConfiguration = () => {
      try {
        // Only set up local sound configuration, don't configure push notifications
        // as that would interfere with Airship's push notification handling
        setIsLoaded(true);
        if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
          logger.info(
            `[NotificationSound] Notification sound ready for playback: ${soundFileName} (${Platform.OS})`,
          );
        }
      } catch (error) {
        if (NOTIFICATION_SOUND_CONFIG.LOGGING.ENABLED) {
          logger.warn(
            '[NotificationSound] Failed to initialize sound configuration:',
            error,
          );
        }
        setIsLoaded(false);
        setIsEnabled(false);
      }
    };

    initializeSoundConfiguration();

    return () => {
      // Cleanup if needed
      setIsLoaded(false);
    };
  }, [soundFileName]);

  const setEnabled = useCallback((enableSound: boolean) => {
    setIsEnabled(enableSound);
  }, []);

  return {
    playSound,
    isLoaded,
    isEnabled,
    setEnabled,
  };
};
