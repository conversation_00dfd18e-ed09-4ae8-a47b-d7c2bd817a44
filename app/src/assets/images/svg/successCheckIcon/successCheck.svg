<svg width="248" height="266" viewBox="0 0 248 266" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="xf6tzn73cc">
            <stop stop-color="#111" stop-opacity="0" offset="0%"/>
            <stop stop-color="#FFF" offset="100%"/>
        </linearGradient>
        <linearGradient x1=".258%" y1="50%" x2="100%" y2="50%" id="ddy5xsc8ae">
            <stop stop-color="#3023AE" offset="0%"/>
            <stop stop-color="#53A0FD" offset="47.525%"/>
            <stop stop-color="#B4EC51" offset="100%"/>
        </linearGradient>
        <linearGradient x1="-.111%" y1="50.055%" x2="100%" y2="50.055%" id="cs915pmvrf">
            <stop stop-color="#8585CE" offset="0%"/>
            <stop stop-color="#3333AD" offset="100%"/>
        </linearGradient>
        <linearGradient x1="37.711%" y1="-1.99%" x2="62.253%" y2="101.995%" id="u66jgu66vg">
            <stop stop-color="#8585CE" offset="0%"/>
            <stop stop-color="#3333AD" offset="100%"/>
        </linearGradient>
        <filter x="-15%" y="-15%" width="130%" height="130%" filterUnits="objectBoundingBox" id="f8orwl6wzb">
            <feMorphology radius="12" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/>
            <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
            <feGaussianBlur stdDeviation="6" in="shadowOffsetOuter1" result="shadowBlurOuter1"/>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" in="shadowBlurOuter1"/>
        </filter>
        <circle id="rsb9vzx1da" cx="100" cy="100" r="100"/>
    </defs>
    <g transform="translate(24)" fill="none" fill-rule="evenodd">
        <g transform="translate(0 42)">
            <mask id="zqnk8m8cfd" fill="#fff">
                <use xlink:href="#rsb9vzx1da"/>
            </mask>
            <g opacity=".5">
                <use fill="#000" filter="url(#f8orwl6wzb)" xlink:href="#rsb9vzx1da"/>
                <circle stroke="#FFF" stroke-width="12" fill="#AFDFFC" cx="100" cy="100" r="106"/>
            </g>
            <path fill="url(#xf6tzn73cc)" opacity=".4" style="mix-blend-mode:color-burn" mask="url(#zqnk8m8cfd)" d="M200 0v200H0z"/>
        </g>
        <rect fill="#000096" y="186" width="200" height="5" rx="2.5"/>
        <rect fill="url(#ddy5xsc8ae)" y="186" width="200" height="5" rx="2.5"/>
        <g transform="translate(58.553 100.526)">
            <circle stroke="#000096" stroke-width="3" fill="#FFF" cx="41.447" cy="41.447" r="39.947"/>
            <path d="M26.668 38.855a2.938 2.938 0 0 0-4.183.191 3.016 3.016 0 0 0 .19 4.228L36.607 56.14a4.113 4.113 0 0 0 5.855-.267c.065-.073.065-.073.127-.147L62.474 31.55a3.015 3.015 0 0 0-.384-4.215 2.939 2.939 0 0 0-4.17.388L39.228 50.452l-12.56-11.597z" fill="#000096" fill-rule="nonzero"/>
        </g>
        <rect fill="url(#cs915pmvrf)" fill-rule="nonzero" transform="rotate(30 63.928 10.928)" x="55.928" y="2.928" width="16" height="16" rx="2"/>
        <path d="m161.599 10.829.621 4.117c.043.325.26.62.55.767l3.67 1.8c.737.37.751 1.447.014 1.83l-3.641 1.875a1.02 1.02 0 0 0-.535.767l-.55 4.132c-.115.841-1.112 1.18-1.69.576l-2.876-2.952a.985.985 0 0 0-.882-.28l-4.017.753c-.81.147-1.445-.709-1.07-1.461l1.865-3.705c.144-.295.144-.649 0-.944l-1.937-3.66c-.39-.738.217-1.623 1.04-1.49l4.033.664a.995.995 0 0 0 .881-.295l2.818-3.011c.564-.605 1.576-.28 1.706.546v-.03z" fill="#9C0" fill-rule="nonzero"/>
        <rect fill="#FBDB65" fill-rule="nonzero" x="13" y="42" width="20" height="20" rx="10"/>
        <path d="M179.03 89.03c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10z" stroke="url(#u66jgu66vg)" stroke-width="4" stroke-dasharray="0" transform="rotate(-76.72 179.03 79.03)"/>
    </g>
</svg>
