import { useAuth } from '@bp/pulse-auth-sdk';
import { Header } from '@bp/ui-components/mobile';
import {
  Button,
  ButtonAction,
  ButtonSize,
  PTextType,
  Spacer,
} from '@bp/ui-components/mobile/core';
import { navigate } from '@utils/navigation';
import Lottie from 'lottie-react-native';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Dimensions, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import * as S from './Error.styles';

const Error = () => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();
  const { authenticated } = useAuth();
  const handleContinueButtonPress = () => {
    navigate('Tabs', { screen: 'Map' }, true);
  };

  const { width, height } = Dimensions.get('window');
  const lottieHeight = height * 0.4;

  const containerStyle = useMemo(
    () => ({
      height: lottieHeight,
      overflow: 'hidden' as const,
    }),
    [lottieHeight],
  );

  const lottieStyle = useMemo(
    () => ({
      width: width * 2,
      marginLeft: -width,
      height: lottieHeight,
    }),
    [width, lottieHeight],
  );

  return (
    <>
      <S.Container topInset={insets.top}>
        <Header title={t('errorScreen.title')} />
        <S.contentWrapper>
          <View style={containerStyle}>
            <Lottie
              source={require('../../assets/lottie/404_error.json')}
              autoPlay
              loop
              resizeMode="cover"
              style={lottieStyle}
            />
          </View>
          <Spacer vSpace={29} />
          <S.ContentText
            type={PTextType.SMALL}
            accessibilityLabel={'errorScreen.contentText1'}>
            {t('errorScreen.contentText1')}
          </S.ContentText>
          <S.ContentText
            type={PTextType.SMALL}
            accessibilityLabel={'errorScreen.contentText2'}>
            {t('errorScreen.contentText2')}
          </S.ContentText>
          <S.ContentText
            type={PTextType.SMALL}
            accessibilityLabel={'errorScreen.contentText3'}>
            {t('errorScreen.contentText3')}
          </S.ContentText>
          <S.BottomContainer bottomInset={insets.bottom}>
            <Button
              accessibilityLabel={'ReturnHomeButton'}
              testID={'ReturnHomeButton'}
              type={ButtonAction.PRIMARY}
              size={ButtonSize.DEFAULT}
              onPress={handleContinueButtonPress}>
              {authenticated ? 'Logout' : 'Return home'}
            </Button>
          </S.BottomContainer>
        </S.contentWrapper>
      </S.Container>
    </>
  );
};

export default Error;
