import { PText, PTextType } from '@bp/ui-components/mobile/core';
import { mapTheme } from '@providers/ThemeProvider';
import styled from 'styled-components/native';

export type ContainerProps = {
  topInset: number;
};

export type BottomContainerProps = {
  bottomInset: number;
};

export const Container = styled.View<ContainerProps>`
  padding-top: ${({ topInset }: ContainerProps) => topInset + 10}px;
  flex: 1;
  background-color: ${() => mapTheme.color.white};
  align-items: center;
  justify-content: center;
`;

export const contentWrapper = styled.ScrollView`
  flex: 1;
  width: 100%;
`;

export const TitleBarContent = styled.View`
  padding: 18px 15px 15px 15px;
  flex-direction: row;
`;

export const TitleText = styled(PText).attrs({ type: PTextType.LEAD })`
  text-align: center;
  padding-right: 30px;
  padding-left: 30px;
  flex-grow: 1;
  font-size: 20px;
  line-height: 27px;
`;

export const BottomContainer = styled.View<BottomContainerProps>`
  flex: 1;
  width: 100%;
  padding-bottom: ${({ bottomInset }: BottomContainerProps) =>
    bottomInset + 24}px;
  padding-right: 24px;
  padding-left: 24px;
  font-size: 16px;
  line-height: 28px;
  justify-content: flex-end;
`;

export const ContentText = styled(PText)`
  text-align: center;
  padding-right: 24px;
  padding-left: 24px;
  padding-bottom: 24px;
  font-size: 16px;
  line-height: 28px;
`;
