import { fireEvent, render } from '@testing-library/react-native';
import { navigate } from '@utils/navigation';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import ThemeProvider from '../../providers/ThemeProvider';
import Error from './Error';

jest.mock('@utils/navigation', () => {
  return {
    navigate: jest.fn(),
  };
});

const renderPage = () =>
  render(
    <ThemeProvider>
      <SafeAreaProvider>
        <Error />
      </SafeAreaProvider>
    </ThemeProvider>,
  );

describe('Terms And Conditions', () => {
  it('Pressing the close button opens the pop up modal', () => {
    const { getByTestId } = renderPage();

    const returnHome = getByTestId('ReturnHomeButton');
    fireEvent.press(returnHome);
    expect(navigate).toHaveBeenCalled();
  });
});
