const RN = require('react-native');

module.exports = {
  __esModule: true,
  default: RN.View,
  BottomSheetScrollView: RN.ScrollView,
  BottomSheetBackdrop: RN.View,
  BottomSheetHandle: RN.View,
  BottomSheetModal: <PERSON><PERSON><PERSON>,
  BottomSheetFooter: RN.View,
  BottomSheetFlatList: RN.FlatList,
  useBottomSheet: jest.fn(),
  useBottomSheetModal: jest.fn(),
  useBottomSheetSpringConfigs: jest.fn(),
  useBottomSheetTimingConfigs: jest.fn(),
  useBottomSheetInternal: jest.fn(),
  useBottomSheetDynamicSnapPoints: jest.fn(),
  BottomSheetModalProvider: jest.fn(),
};
