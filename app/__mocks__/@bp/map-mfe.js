module.exports = {
  __esModule: true,
  default: (props) => props?.children ?? null,
  MapScreen: (props) => props?.children ?? null,
  MapFiltersScreen: (props) => props?.children ?? null,
  MapFilterByNetworkScreen: (props) => props?.children ?? null,
  MapProvider: (props) => props?.children ?? null,
  MapScreenName: {
    MAP_SCREEN: 'MapMFE.MapScreen',
    MAP_FILTERS_SCREEN: 'MapMFE.MapFiltersScreen',
    MAP_FILTER_BY_NETWORK_SCREEN: "MapMFE.MapFilterByNetworkScreen"
  },
  MapScreenParams: {
    VIEW_USER_POSITION: 'MapMFE.VIEW_USER_POSITION',
  },
  MapScreenParamsViewSite: jest.fn(),
  MapAnalyticsEvent: {
    'SITE_MARKER_SELECT': 'SITE_MARKER_SELECT'
  },
  MapLocales: {
    EN_GB: "en-GB",
    EN_US: "en-US",
    DE_DE: "de-DE",
    NL_NL: "nl-NL",
    ES_ES: "es-ES",
    FR_FR: "fr-FR",
    ZZ: "zz",
    DEV: "dev"
  },
  SiteType: {
    EV: 'test',
  },
  Provider: {
    BPCM: 'BPCM',
    DCS: 'DCS',
    EVC: 'EVC',
    aral: 'aral',
    fleet: 'fleet',
    hasToBe: 'hasToBe',
    semarchy: 'semarchy',
  },
  FuelType: { AD_BLUE: 'AD_BLUE' },
  ConnectorType: { CCS1: ' CCS1' },
  ConnectorFormat: {
    CABLE: "CABLE",
    SOCKET: "SOCKET",
    UNKNOWN: "UNKNOWN"
  },
  ChargepointSchemeId: { ROYAL_MAIL: 'ROYAL_MAIL' },
  Service: { AD_BLUE: 'AD_BLUE' },
  AvailabilityState: {
    Available: "Available",
    Charging: "Charging",
    Occupied: "Occupied",
    Reserved: "Reserved",
    Unavailable: "Unavailable",
    Unknown: "Unknown"
  },
  Currency: {
    EUR: 'EUR',
  },
  TariffType: {
    GUEST: "GUEST",
    PAYG: "PAYG",
    SUBSCRIBER: 'SUBSCRIBER',
  },
  Country: {
    AT: 'AT',
    BE: 'BE',
    BG: 'BG',
    CH: 'CH',
    CZ: 'CZ',
    DE: 'DE',
    DK: 'DK',
    EE: 'EE',
    ES: 'ES',
    FR: 'FR',
    HR: 'HR',
    HU: 'HU',
    IE: 'IE',
    IT: 'IT',
    LT: 'LT',
    LU: 'LU',
    LV: 'LV',
    NL: 'NL',
    NO: 'NO',
    PL: 'PL',
    RO: 'RO',
    SE: 'SE',
    SI: 'SI',
    SK: 'SK',
    UK: 'UK',
    US: 'US',
  },
  EntitlementOrigin: {
    GUEST: 0,
    UK: 1,
    DE: 2,
    NL: 3,
    US: 4,
  },
};
