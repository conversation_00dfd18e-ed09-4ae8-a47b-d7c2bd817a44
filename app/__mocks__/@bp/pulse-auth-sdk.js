module.exports = {
  useAuth: jest.fn().mockReturnValue({
    authenticated: false,
    consents: [],
    user: {
      consentsValid: false,
      clientId: '',
      loading: false,
    },
    loginOrRegister: jest.fn(),
    logout: jest.fn(),
    getIdToken: jest.fn(),
    getAccessToken: jest.fn(),
    getUser: jest.fn(),
    getConsents: jest.fn(),
    updateConsent: jest.fn(),
    updateEmail: jest.fn(),
    updatePhone: jest.fn(),
    updateTrackingConsent: jest.fn(),
    verifyPhone: jest.fn(),
  }),
  SFConsent: {
    GENERAL_MARKETING: 'General Marketing',
    PERSONAL_MARKETING: 'Personal Marketing',
    EV_MARKETING: 'EV Marketing',
    EV_MARKETING_SMS: 'EV Marketing - SMS',
    EV_MARKETING_PUSH: 'EV Marketing - Push',
    TERMS_AND_CONDITIONS: 'Terms and Conditions',
    EV_TERMS_AND_CONDITIONS: 'EV Terms and Conditions',
    EV_TERMS_AND_CONDITIONS_NL: 'EV Terms and Conditions NL',
    EV_TERMS_AND_CONDITIONS_ES: 'EV Terms and Conditions ES',
    EV_TERMS_AND_CONDITIONS_DE: 'EV Terms and Conditions DE',
    TRACK_AND_TARGET: 'Track and Target',
    PRIVACY_POLICY: 'Privacy Policy',
    EV_PRIVACY_POLICY: 'EV Privacy Policy',
    EV_PRIVACY_POLICY_NL: 'EV Privacy Policy NL',
    EV_PRIVACY_POLICY_ES: 'EV Privacy Policy ES',
    EV_PRIVACY_POLICY_DE: 'EV Privacy Policy DE',
    THIRD_PARTY_SHARING: '3rd Party Sharing',
    EV_RIDEHAILING: 'EV Ridehailing'
  },
  LoginAnalyticsEvent:{},
}
