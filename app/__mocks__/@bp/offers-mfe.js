module.exports = {
  __esModule: true,
  OffersMfe: props => props?.children ?? null,
  OffersProvider: props => props?.children ?? null,
  OffersLocales: {
    DE_DE: 'de-DE',
    EN_GB: 'en-GB',
    EN_US: 'en-US',
    ES_ES: 'es-ES',
    FR_FR: 'fr-FR',
    NL_NL: 'nl-NL',
  },
  UserCountry: {
    DE: 'DE',
    NL: 'NL',
    UK: 'UK',
    ES: 'ES',
  },
  UserType: {
    NEW: 'NEW',
    SUBS: 'SUBS',
    PAYG: 'PAYG',
    PAYG_WALLET: 'PAYG-Wallet',
    SUBS_WALLET: 'SUBS-WALLET',
  },
  OffersScreenName: {
    ADD_NEW_OFFER_SCREEN: 'OffersMfe.AddNewOfferScreen',
    ADD_OFFER_ERROR_SCREEN: 'OffersMfe.AddOfferErrorScreen',
    ADD_OFFER_SUCCESS_SCREEN: 'OffersMfe.AddOfferSuccessScreen',
    ARCHIVED_OFFERS_SCREEN: 'OffersMfe.ArchivedOffersScreen',
    COMING_SOON_SCREEN: 'OffersMfe.ComingSoonScreen',
    LOGIN_SCREEN: 'OffersMfe.LoginScreen',
    NO_CONNECTION_SCREEN: 'OffersMfe.NoConnectionScreen',
    SUBSCRIPTION_REQUIRED_SCREEN: 'OffersMfe.SubscriptionRequiredScreen',
    USER_OFFERS_SCREEN: 'OffersMfe.UserOffersScreen',
  },
};
