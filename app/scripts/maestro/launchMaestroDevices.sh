
#! /bin/bash

# Arguments
platform=$1

# echo "$platform $device $osVersion $appId"

if [ $platform = 'ios' ]; then
    devices=$(xcrun simctl list | grep -E "Maestro_iPhone[0-9]*_[0-9]+.*\(Shutdown\)" | awk -F "[()]" '{print $2}' | xargs | sed 's/ /,/g')
else 
    # TODO: Obtain Android devices
    echo "Error: Android is not yet supported"
    exit 1
fi
echo "$devices"
count=$(echo "$devices" | awk -F',' '{print NF}')

if [ $count = 0 ]; then
    echo "No Maestro devices found"
else
    echo "Launching $count devices"
    # Convert comma-separated list to array for iteration
    IFS=',' read -r -a device_array <<< "$devices"

    # Loop through each device UUID and boot it
    for uuid in "${device_array[@]}"; do
        if [[ -n "$uuid" ]]; then
            echo "Booting device: $uuid"
            xcrun simctl boot "$uuid"
        fi
    done
fi