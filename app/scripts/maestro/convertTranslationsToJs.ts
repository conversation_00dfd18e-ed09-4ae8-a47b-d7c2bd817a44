/* This script takes the original JSON translation files and converts them into
 * a JS format used in Maestro tests
 */

import fs from 'fs';

import de_DE from '../../src/translations/locales/de-DE.json';
import en_GB from '../../src/translations/locales/en-GB.json';
import en_US from '../../src/translations/locales/en-US.json';
import es_ES from '../../src/translations/locales/es-ES.json';
import nl_NL from '../../src/translations/locales/nl-NL.json';

const locales = [
  {
    name: 'de-DE',
    value: de_DE,
  },
  {
    name: 'en-GB',
    value: en_GB,
  },
  {
    name: 'en-US',
    value: en_US,
  },
  {
    name: 'es-ES',
    value: es_ES,
  },
  {
    name: 'nl-NL',
    value: nl_NL,
  },
];

locales.forEach(({ name, value }) => {
  console.log(`Writing ${name} locale to file`);
  fs.writeFileSync(
    `./maestro/pom/elements/translations/${name}.js`,
    `output.t = ${JSON.stringify(value, null, 2)}`,
  );
});
