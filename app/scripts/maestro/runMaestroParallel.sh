
#! /bin/bash
export MAESTRO_DRIVER_STARTUP_TIMEOUT=300000

# Arguments
platform=$1
device=$2
osVersion=$3
appId=$4

# echo "$platform $device $osVersion $appId"

if [ $platform = 'ios' ]; then
    devices=$(xcrun simctl list | grep -E "Maestro_"$device"_"$osVersion"_[0-9]+.*\(Booted\)" | awk -F "[()]" '{print $2}' | xargs | sed 's/ /,/g')
else 
    devices=$(adb devices | grep -E "emulator-[0-9]+" | awk -F " " '{print $1}' | xargs | sed 's/ /,/g')
fi

count=$(echo "$devices" | awk -F',' '{print NF}')

if [ $count = 0 ]; then
    echo "No specified devices found"
else
    echo "Running Maestro on $count devices"
    $HOME/.maestro/bin/maestro --device="$devices" test --shard-split "$count" --format junit --debug-output logs maestro @maestro/.env -e APP_ID=$4
fi