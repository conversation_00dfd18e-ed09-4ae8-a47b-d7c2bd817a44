const oldPackageJson = require('../../package.json');
const newPackageJson = require('../../package-new.json');
const semver = require('semver');

type Dependencies = {
  [key: string]: string;
};

const findChanges = (
  oldDependencies: Dependencies,
  newDependencies: Dependencies,
) => {
  const changed: Array<string> = [];
  Object.keys(oldDependencies).map((key, value) => {
    if (oldDependencies[key] !== newDependencies[key]) {
      changed.push(key);
    }
  });
  return changed;
};

const changed = findChanges(
  oldPackageJson.dependencies,
  newPackageJson.dependencies,
);

const filtered: Array<string> = changed.filter(value =>
  value.startsWith('@bp/'),
);

const requiredVersion: Dependencies = {
  '@bp/ui-components': newPackageJson.dependencies['@bp/ui-components'],
  '@bp/pulse-shared-types':
    newPackageJson.dependencies['@bp/pulse-shared-types'],
};
const errors = () => {
  const errors: Array<string> = [];
  filtered.forEach(packageName => {
    const packageDependencies = require(`../../../node_modules/${packageName}/package.json`);
    Object.keys(requiredVersion).forEach(key => {
      const dependency =
        packageDependencies.dependencies?.[key] ||
        packageDependencies.devDependencies?.[key];
      const simplified = dependency?.replace('^', '');
      if (
        dependency &&
        !semver.satisfies(simplified, requiredVersion[key].replace('^', ''))
      ) {
        const error = `Please update ${key} version required: ${requiredVersion[key]}, found: ${simplified} inside: ${packageName}`;
        console.log(error);
        errors.push(error);
      }
    });
  });
  return errors;
};

if (errors().length > 0) {
  process.exit(1);
}

export {};
