#!/usr/bin/env bash

cat  << EOF
G_MAPS_URL=${G_MAPS_URL}
G_MAPS_KEY=${G_MAPS_KEY}
G_MAPS_ANDROID=${G_MAPS_ANDROID}
G_MAPS_IOS=${G_MAPS_IOS}
APPSFLYER_APP_ID=${APPSFLYER_APP_ID}
APPSFLYER_DEV_KEY=${APPSFLYER_DEV_KEY}
API_GATEWAY_KEY=${API_GATEWAY_KEY}
GATEWAY_URL_PUBLIC=${GATEWAY_URL_PUBLIC}
GATEWAY_URL_PRIVATE=${GATEWAY_URL_PRIVATE}
GUEST_AUTH_URL=${GUEST_AUTH_URL}
GUEST_AUTH_KEY=${GUEST_AUTH_KEY}
BPPAY_GUEST_MICROSITE=${BPPAY_GUEST_MICROSITE}
STRIPE_MICROSITE=${STRIPE_MICROSITE}
CIP_BASE_URL=${CIP_BASE_URL}
CIP_CLIENT_ID=${CIP_CLIENT_ID}
CIP_REDIRECT_URL=${CIP_REDIRECT_URL}
CIP_EMAIL_SUCCESS_REDIRECT_URL=${CIP_EMAIL_SUCCESS_REDIRECT_URL}
CIP_IHUB_BASE_URL=${CIP_IHUB_BASE_URL}
CARDINAL_ENVIRONMENT=${CARDINAL_ENVIRONMENT}
STAGE=${STAGE}
BRAND=${BRAND}
ANDROID_BUNDLE_ID=${ANDROID_BUNDLE_ID}
IOS_APP_ID=${IOS_APP_ID}
COUNTRY_CODE=${COUNTRY_CODE}
AIRSHIP_APP_SECRET=${AIRSHIP_APP_SECRET}
AIRSHIP_APP_KEY=${AIRSHIP_APP_KEY}
AIRSHIP_SITE=${AIRSHIP_SITE}
AIRSHIP_IN_PRODUCTION=${AIRSHIP_IN_PRODUCTION}
EOF
