<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AFAppID</key>
	<string>$(AF_APP_ID)</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>bp pulse</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>de</string>
		<string>Dutch</string>
		<string>Spanish</string>
	</array>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>bppulse</string>
				<string>sfdc</string>
				<string>bppay</string>
				<string>bppaywallet</string>
				<string>chargemaster</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
		<string>message</string>
		<string>readdle-spark</string>
		<string>airmail</string>
		<string>ms-outlook</string>
		<string>googlegmail</string>
		<string>inbox-gmail</string>
		<string>ymail</string>
		<string>superhuman</string>
		<string>yandexmail</string>
		<string>fastmail</string>
		<string>protonmail</string>
		<string>szn-email</string>
		<string>comgooglemaps</string>
		<string>citymapper</string>
		<string>waze</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAdvertisingAttributionReportEndpoint</key>
	<string>https://appsflyer-skadnetwork.com/</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location is required for us to be able to understand when you are near your chargepoint and to show you a map of all chargepoints</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Your location is required for us to be able to understand when you are near your chargepoint and to show you a map of all chargepoints</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location is required for us to be able to understand when you are near your chargepoint and to show you a map of all chargepoints</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>As a guest user, we don't store your personal or location data.

Allow bp pulse to track how you use the app and help improve the experience for everyone.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Roboto-Black.ttf</string>
		<string>Roboto-BlackItalic.ttf</string>
		<string>Roboto-BoldItalic.ttf</string>
		<string>Roboto-Italic.ttf</string>
		<string>Roboto-Light.ttf</string>
		<string>Roboto-LightItalic.ttf</string>
		<string>Roboto-MediumItalic.ttf</string>
		<string>Roboto-Thin.ttf</string>
		<string>Roboto-ThinItalic.ttf</string>
		<string>AralPOS-Regular.ttf</string>
		<string>Roboto-Bold.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>Roboto-Regular.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
    <string>fetch</string>
		<string>remote-notification</string>
	</array>
  <key>NSUserNotificationUsageDescription</key>
<string>We need to send you notifications for important updates</string>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
