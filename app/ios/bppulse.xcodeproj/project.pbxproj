// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* bppulseTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* bppulseTests.m */; };
		065857602B2347CB007FC439 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = AB43B9D9286EA83E002ED69E /* AppDelegate.mm */; };
		065857612B2347CB007FC439 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		065857652B2347CB007FC439 /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AB99E83B264E9516000D839E /* BootSplash.storyboard */; };
		065857662B2347CB007FC439 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		06FA349F2CAD87DF0001CA65 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 06FA349E2CAD87DF0001CA65 /* GoogleService-Info.plist */; };
		06FA34A12CAD87F00001CA65 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 06FA34A02CAD87F00001CA65 /* GoogleService-Info.plist */; };
		0EF82270AF8B8D5424C0F91F /* libPods-bppulse.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A4CC425C6B75462F0810C606 /* libPods-bppulse.a */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		15632D50FA024F318A41E2EB /* Roboto-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 2CDD9C450BB84E8DB38FC88E /* Roboto-MediumItalic.ttf */; };
		2144EE5B2C6D4A0B00F384D7 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = AB43B9D9286EA83E002ED69E /* AppDelegate.mm */; };
		2144EE5C2C6D4A0B00F384D7 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2144EE612C6D4A0B00F384D7 /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AB99E83B264E9516000D839E /* BootSplash.storyboard */; };
		2144EE622C6D4A0B00F384D7 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		329F9CF558E3483180F1027F /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC015E8320D2441EB5D1A2D9 /* Roboto-Regular.ttf */; };
		336EAEE52732497698D5249C /* Roboto-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7700B80024264F84A378AFD0 /* Roboto-Black.ttf */; };
		35F6782D904F64926F1D0653 /* libPods-bppulse-aralpulse.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 9447745A9038496B8649E16B /* libPods-bppulse-aralpulse.a */; };
		4DCBD98BA6BC4D959DC16FCE /* Roboto-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 27D4FD6D4ADD42B0AA401512 /* Roboto-LightItalic.ttf */; };
		95A68C2F275946F4BBE6E7DB /* AralPOS-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A7D5AADDFEB5439CBB633F42 /* AralPOS-Regular.ttf */; };
		A7FD7F0C5F992B316BCDBA6A /* BuildFile in Frameworks */ = {isa = PBXBuildFile; };
		A96B04F4463D4A45A7B4CC25 /* Roboto-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4996D7AE60D34DBBB85DD193 /* Roboto-Medium.ttf */; };
		AB43B9DA286EA83E002ED69E /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = AB43B9D9286EA83E002ED69E /* AppDelegate.mm */; };
		AB4B466C26B312D900327F64 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = AB4B466B26B312D900327F64 /* GoogleService-Info.plist */; };
		AB8DE71199B6E2D72343C58B /* libPods-bppulse-bppulseTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C6D6CDE2133B247CFDD086A6 /* libPods-bppulse-bppulseTests.a */; };
		AB99E83C264E9516000D839E /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AB99E83B264E9516000D839E /* BootSplash.storyboard */; };
		AF990CCDA74944A6B8152CBA /* Roboto-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 26252E3C50114F0FB15490AE /* Roboto-Thin.ttf */; };
		B89F5359992E9CF41ED43FC4 /* BuildFile in Frameworks */ = {isa = PBXBuildFile; };
		BD3362B0112743C1833490BE /* Roboto-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 00C87B459BAF4CAFA5D2B17C /* Roboto-BoldItalic.ttf */; };
		BEF2A71140D54C41B5245E5C /* Roboto-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A7D961663A7047F980B80E39 /* Roboto-ThinItalic.ttf */; };
		C93993D566864FD7B8A20348 /* Roboto-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7BB900174EA3412D82C932D0 /* Roboto-Light.ttf */; };
		CA77DC2CACAB4E959D1ECEB5 /* Roboto-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C962AA4B72C44DEB8C7E79C7 /* Roboto-BlackItalic.ttf */; };
		CABB9D430BAE3CDC3AEEA9C0 /* libPods-bppulse-bpUSpulse.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 80F764C1980C2FD7FCE8E002 /* libPods-bppulse-bpUSpulse.a */; };
		EDE0345F643F4B9799F47394 /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0D1C95BE00524EC2B2A0759F /* Roboto-Bold.ttf */; };
		F6528696CD994AD5929D18E6 /* Roboto-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 101A6D6E9DA04B2E93D10D41 /* Roboto-Italic.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = bppulse;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00C87B459BAF4CAFA5D2B17C /* Roboto-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-BoldItalic.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-BoldItalic.ttf"; sourceTree = "<group>"; };
		00E356EE1AD99517003FC87E /* bppulseTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = bppulseTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* bppulseTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = bppulseTests.m; sourceTree = "<group>"; };
		060C14C42CAD5C58006C89F6 /* bppulse.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = bppulse.entitlements; path = bppulse/bppulse.entitlements; sourceTree = "<group>"; };
		060C14C52CAD5C99006C89F6 /* aralpulse.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = aralpulse.entitlements; path = aralpulse/aralpulse.entitlements; sourceTree = "<group>"; };
		0658577A2B2347CB007FC439 /* aralpulse.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = aralpulse.app; sourceTree = BUILT_PRODUCTS_DIR; };
		06C7AC7CE414A83A5D00C296 /* Pods-bppulse.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.prod.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.prod.xcconfig"; sourceTree = "<group>"; };
		06FA349E2CAD87DF0001CA65 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "aralpulse/GoogleService-Info.plist"; sourceTree = "<group>"; };
		06FA34A02CAD87F00001CA65 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "bpUSpulse/GoogleService-Info.plist"; sourceTree = "<group>"; };
		06FA34A32CAEE5B80001CA65 /* bpUSpulse.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = bpUSpulse.entitlements; path = bpUSpulse/bpUSpulse.entitlements; sourceTree = "<group>"; };
		09F245B7B3CBA3E9E89B7887 /* Pods-bppulse-bppulseTests.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.prod.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.prod.xcconfig"; sourceTree = "<group>"; };
		0D1C95BE00524EC2B2A0759F /* Roboto-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Bold.ttf"; path = "../src/assets/fonts/Roboto-Bold.ttf"; sourceTree = "<group>"; };
		0F62A3D8856C3D40555B058C /* Pods-bppulse-aralpulse.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.release.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.release.xcconfig"; sourceTree = "<group>"; };
		0FAE8EA5C22212B4C906CC4D /* Pods-bppulse-bppulseTests.preprod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.preprod.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.preprod.xcconfig"; sourceTree = "<group>"; };
		101A6D6E9DA04B2E93D10D41 /* Roboto-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Italic.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Italic.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* bppulse.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = bppulse.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = bppulse/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = bppulse/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = bppulse/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = bppulse/main.m; sourceTree = "<group>"; };
		2144EE6E2C6D4A0B00F384D7 /* bpUSpulse.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = bpUSpulse.app; sourceTree = BUILT_PRODUCTS_DIR; };
		21AF5DEAE37E0288D1141C57 /* Pods-bppulse.performance.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.performance.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.performance.xcconfig"; sourceTree = "<group>"; };
		21C664CA1601736A9A7F1712 /* Pods-bppulse-bpUSpulse.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.release.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.release.xcconfig"; sourceTree = "<group>"; };
		26252E3C50114F0FB15490AE /* Roboto-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Thin.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Thin.ttf"; sourceTree = "<group>"; };
		27D4FD6D4ADD42B0AA401512 /* Roboto-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-LightItalic.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-LightItalic.ttf"; sourceTree = "<group>"; };
		2B5BBA6AEAF86FEA12BEF488 /* Pods-bppulse-bppulseTests.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.dev.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.dev.xcconfig"; sourceTree = "<group>"; };
		2CDD9C450BB84E8DB38FC88E /* Roboto-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-MediumItalic.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-MediumItalic.ttf"; sourceTree = "<group>"; };
		2E7901F007568ED9061E08D9 /* Pods-bppulse.preprod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.preprod.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.preprod.xcconfig"; sourceTree = "<group>"; };
		37D34B527A358863FC80398E /* Pods-bppulse-aralpulse.performance.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.performance.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.performance.xcconfig"; sourceTree = "<group>"; };
		38E0C5A04D26CD464DF37D29 /* Pods-bppulse-aralpulse.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.dev.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.dev.xcconfig"; sourceTree = "<group>"; };
		3DD16C42855384ADCAB12A45 /* Pods-bppulse-aralpulse.test.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.test.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.test.xcconfig"; sourceTree = "<group>"; };
		4996D7AE60D34DBBB85DD193 /* Roboto-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Medium.ttf"; path = "../src/assets/fonts/Roboto-Medium.ttf"; sourceTree = "<group>"; };
		4B7D536B75680CB34D06543E /* Pods-bppulse.test.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.test.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.test.xcconfig"; sourceTree = "<group>"; };
		561900409DCD030A3E874534 /* Pods-bppulse.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.dev.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.dev.xcconfig"; sourceTree = "<group>"; };
		5DF07272E77309C5C7822393 /* Pods-bppulse-bpUSpulse.test.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.test.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.test.xcconfig"; sourceTree = "<group>"; };
		6A583A0DE57540D5F41981BE /* Pods-bppulse-aralpulse.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.prod.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.prod.xcconfig"; sourceTree = "<group>"; };
		6A880B30BAC1160813FB4AB0 /* Pods-bppulse.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.debug.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.debug.xcconfig"; sourceTree = "<group>"; };
		7700B80024264F84A378AFD0 /* Roboto-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Black.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Black.ttf"; sourceTree = "<group>"; };
		7BB900174EA3412D82C932D0 /* Roboto-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Light.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Light.ttf"; sourceTree = "<group>"; };
		7E128CC4F06C9F3153201A09 /* Pods-bppulse-bppulseTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.release.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.release.xcconfig"; sourceTree = "<group>"; };
		802D34AE687B00D147FB7D6D /* Pods-bppulse-bppulseTests.performance.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.performance.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.performance.xcconfig"; sourceTree = "<group>"; };
		80F764C1980C2FD7FCE8E002 /* libPods-bppulse-bpUSpulse.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bppulse-bpUSpulse.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		841C2F01D13348EBBE75DC0B /* Roboto-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Bold.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Bold.ttf"; sourceTree = "<group>"; };
		9447745A9038496B8649E16B /* libPods-bppulse-aralpulse.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bppulse-aralpulse.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		98A82EED5AA44B6F0B06ADAF /* Pods-bppulse.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse.release.xcconfig"; path = "Target Support Files/Pods-bppulse/Pods-bppulse.release.xcconfig"; sourceTree = "<group>"; };
		99336C0160CED184F0705316 /* Pods-bppulse-bppulseTests.test.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.test.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.test.xcconfig"; sourceTree = "<group>"; };
		A3F4A31E6110FB318564D881 /* Pods-bppulse-aralpulse.preprod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.preprod.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.preprod.xcconfig"; sourceTree = "<group>"; };
		A4CC425C6B75462F0810C606 /* libPods-bppulse.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bppulse.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A69793EDD46D47278243263F /* Roboto-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Regular.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Regular.ttf"; sourceTree = "<group>"; };
		A7D5AADDFEB5439CBB633F42 /* AralPOS-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "AralPOS-Regular.ttf"; path = "../src/assets/fonts/AralPOS-Regular.ttf"; sourceTree = "<group>"; };
		A7D961663A7047F980B80E39 /* Roboto-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-ThinItalic.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-ThinItalic.ttf"; sourceTree = "<group>"; };
		A84700E36679BDB115B20A21 /* Pods-bppulse-bpUSpulse.performance.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.performance.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.performance.xcconfig"; sourceTree = "<group>"; };
		A9D36A1097DB4497B73E76A9 /* AralPOS-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "AralPOS-Regular.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/AralPOS-Regular.ttf"; sourceTree = "<group>"; };
		AB43B9D9286EA83E002ED69E /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = bppulse/AppDelegate.mm; sourceTree = "<group>"; };
		AB4B466B26B312D900327F64 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "bppulse/GoogleService-Info.plist"; sourceTree = "<group>"; };
		AB99E83B264E9516000D839E /* BootSplash.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = BootSplash.storyboard; path = bppulse/BootSplash.storyboard; sourceTree = "<group>"; };
		AC0E2F8B195545998FC5F6B7 /* Roboto-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Medium.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-Medium.ttf"; sourceTree = "<group>"; };
		B672BCB3B2195B558E071F58 /* Pods-bppulse-bpUSpulse.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.debug.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.debug.xcconfig"; sourceTree = "<group>"; };
		BBBED69696D8503A0589EA31 /* Pods-bppulse-bppulseTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bppulseTests.debug.xcconfig"; path = "Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests.debug.xcconfig"; sourceTree = "<group>"; };
		C6D6CDE2133B247CFDD086A6 /* libPods-bppulse-bppulseTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-bppulse-bppulseTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C91F2BA4314A0C3D1677939C /* Pods-bppulse-bpUSpulse.dev.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.dev.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.dev.xcconfig"; sourceTree = "<group>"; };
		C962AA4B72C44DEB8C7E79C7 /* Roboto-BlackItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-BlackItalic.ttf"; path = "../../node_modules/@bp/pulse-mobile-sdk/assets/fonts/Roboto-BlackItalic.ttf"; sourceTree = "<group>"; };
		CC015E8320D2441EB5D1A2D9 /* Roboto-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Roboto-Regular.ttf"; path = "../src/assets/fonts/Roboto-Regular.ttf"; sourceTree = "<group>"; };
		D0691A3DB283533E125D1E9F /* Pods-bppulse-bpUSpulse.prod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.prod.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.prod.xcconfig"; sourceTree = "<group>"; };
		E02C2E2923DC41968919DE55 /* Pods-bppulse-aralpulse.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-aralpulse.debug.xcconfig"; path = "Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse.debug.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F4101779638D427E89AA9534 /* UniversforBP-Roman 2.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "UniversforBP-Roman 2.ttf"; path = "../src/assets/fonts/UniversforBP-Roman 2.ttf"; sourceTree = "<group>"; };
		FE7FE050993B8AEAF95D9061 /* Pods-bppulse-bpUSpulse.preprod.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-bppulse-bpUSpulse.preprod.xcconfig"; path = "Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse.preprod.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				AB8DE71199B6E2D72343C58B /* libPods-bppulse-bppulseTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		065857622B2347CB007FC439 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				B89F5359992E9CF41ED43FC4 /* BuildFile in Frameworks */,
				35F6782D904F64926F1D0653 /* libPods-bppulse-aralpulse.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A7FD7F0C5F992B316BCDBA6A /* BuildFile in Frameworks */,
				0EF82270AF8B8D5424C0F91F /* libPods-bppulse.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2144EE5D2C6D4A0B00F384D7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				CABB9D430BAE3CDC3AEEA9C0 /* libPods-bppulse-bpUSpulse.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* bppulseTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* bppulseTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = bppulseTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* bppulse */ = {
			isa = PBXGroup;
			children = (
				060C14C42CAD5C58006C89F6 /* bppulse.entitlements */,
				AB43B9D9286EA83E002ED69E /* AppDelegate.mm */,
				AB4B466B26B312D900327F64 /* GoogleService-Info.plist */,
				AB99E83B264E9516000D839E /* BootSplash.storyboard */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = bppulse;
			sourceTree = "<group>";
		};
		26DFB124F7A447F6B52FC863 /* Resources */ = {
			isa = PBXGroup;
			children = (
				F4101779638D427E89AA9534 /* UniversforBP-Roman 2.ttf */,
				A9D36A1097DB4497B73E76A9 /* AralPOS-Regular.ttf */,
				7700B80024264F84A378AFD0 /* Roboto-Black.ttf */,
				C962AA4B72C44DEB8C7E79C7 /* Roboto-BlackItalic.ttf */,
				841C2F01D13348EBBE75DC0B /* Roboto-Bold.ttf */,
				00C87B459BAF4CAFA5D2B17C /* Roboto-BoldItalic.ttf */,
				101A6D6E9DA04B2E93D10D41 /* Roboto-Italic.ttf */,
				7BB900174EA3412D82C932D0 /* Roboto-Light.ttf */,
				27D4FD6D4ADD42B0AA401512 /* Roboto-LightItalic.ttf */,
				AC0E2F8B195545998FC5F6B7 /* Roboto-Medium.ttf */,
				2CDD9C450BB84E8DB38FC88E /* Roboto-MediumItalic.ttf */,
				A69793EDD46D47278243263F /* Roboto-Regular.ttf */,
				26252E3C50114F0FB15490AE /* Roboto-Thin.ttf */,
				A7D961663A7047F980B80E39 /* Roboto-ThinItalic.ttf */,
				A7D5AADDFEB5439CBB633F42 /* AralPOS-Regular.ttf */,
				0D1C95BE00524EC2B2A0759F /* Roboto-Bold.ttf */,
				4996D7AE60D34DBBB85DD193 /* Roboto-Medium.ttf */,
				CC015E8320D2441EB5D1A2D9 /* Roboto-Regular.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				A4CC425C6B75462F0810C606 /* libPods-bppulse.a */,
				9447745A9038496B8649E16B /* libPods-bppulse-aralpulse.a */,
				80F764C1980C2FD7FCE8E002 /* libPods-bppulse-bpUSpulse.a */,
				C6D6CDE2133B247CFDD086A6 /* libPods-bppulse-bppulseTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		39693736E464924EFC3B49B4 /* Pods */ = {
			isa = PBXGroup;
			children = (
				6A880B30BAC1160813FB4AB0 /* Pods-bppulse.debug.xcconfig */,
				98A82EED5AA44B6F0B06ADAF /* Pods-bppulse.release.xcconfig */,
				561900409DCD030A3E874534 /* Pods-bppulse.dev.xcconfig */,
				4B7D536B75680CB34D06543E /* Pods-bppulse.test.xcconfig */,
				2E7901F007568ED9061E08D9 /* Pods-bppulse.preprod.xcconfig */,
				21AF5DEAE37E0288D1141C57 /* Pods-bppulse.performance.xcconfig */,
				06C7AC7CE414A83A5D00C296 /* Pods-bppulse.prod.xcconfig */,
				E02C2E2923DC41968919DE55 /* Pods-bppulse-aralpulse.debug.xcconfig */,
				0F62A3D8856C3D40555B058C /* Pods-bppulse-aralpulse.release.xcconfig */,
				38E0C5A04D26CD464DF37D29 /* Pods-bppulse-aralpulse.dev.xcconfig */,
				3DD16C42855384ADCAB12A45 /* Pods-bppulse-aralpulse.test.xcconfig */,
				A3F4A31E6110FB318564D881 /* Pods-bppulse-aralpulse.preprod.xcconfig */,
				37D34B527A358863FC80398E /* Pods-bppulse-aralpulse.performance.xcconfig */,
				6A583A0DE57540D5F41981BE /* Pods-bppulse-aralpulse.prod.xcconfig */,
				B672BCB3B2195B558E071F58 /* Pods-bppulse-bpUSpulse.debug.xcconfig */,
				21C664CA1601736A9A7F1712 /* Pods-bppulse-bpUSpulse.release.xcconfig */,
				C91F2BA4314A0C3D1677939C /* Pods-bppulse-bpUSpulse.dev.xcconfig */,
				5DF07272E77309C5C7822393 /* Pods-bppulse-bpUSpulse.test.xcconfig */,
				FE7FE050993B8AEAF95D9061 /* Pods-bppulse-bpUSpulse.preprod.xcconfig */,
				A84700E36679BDB115B20A21 /* Pods-bppulse-bpUSpulse.performance.xcconfig */,
				D0691A3DB283533E125D1E9F /* Pods-bppulse-bpUSpulse.prod.xcconfig */,
				BBBED69696D8503A0589EA31 /* Pods-bppulse-bppulseTests.debug.xcconfig */,
				7E128CC4F06C9F3153201A09 /* Pods-bppulse-bppulseTests.release.xcconfig */,
				2B5BBA6AEAF86FEA12BEF488 /* Pods-bppulse-bppulseTests.dev.xcconfig */,
				99336C0160CED184F0705316 /* Pods-bppulse-bppulseTests.test.xcconfig */,
				0FAE8EA5C22212B4C906CC4D /* Pods-bppulse-bppulseTests.preprod.xcconfig */,
				802D34AE687B00D147FB7D6D /* Pods-bppulse-bppulseTests.performance.xcconfig */,
				09F245B7B3CBA3E9E89B7887 /* Pods-bppulse-bppulseTests.prod.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				06FA34A32CAEE5B80001CA65 /* bpUSpulse.entitlements */,
				060C14C52CAD5C99006C89F6 /* aralpulse.entitlements */,
				06FA349E2CAD87DF0001CA65 /* GoogleService-Info.plist */,
				06FA34A02CAD87F00001CA65 /* GoogleService-Info.plist */,
				13B07FAE1A68108700A75B9A /* bppulse */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* bppulseTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				39693736E464924EFC3B49B4 /* Pods */,
				26DFB124F7A447F6B52FC863 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* bppulse.app */,
				00E356EE1AD99517003FC87E /* bppulseTests.xctest */,
				0658577A2B2347CB007FC439 /* aralpulse.app */,
				2144EE6E2C6D4A0B00F384D7 /* bpUSpulse.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* bppulseTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "bppulseTests" */;
			buildPhases = (
				101D2066D10889FC9E5EBDC1 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				7BABB27B64B09EBDF1B35E15 /* [CP] Embed Pods Frameworks */,
				3288A44CC2A15EBE41F445A0 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = bppulseTests;
			productName = bppulseTests;
			productReference = 00E356EE1AD99517003FC87E /* bppulseTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		0658575C2B2347CB007FC439 /* aralpulse */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 065857722B2347CB007FC439 /* Build configuration list for PBXNativeTarget "aralpulse" */;
			buildPhases = (
				AEDEDA1769683C799E4283B4 /* [CP] Check Pods Manifest.lock */,
				0658575E2B2347CB007FC439 /* Start Packager */,
				0658575F2B2347CB007FC439 /* Sources */,
				065857622B2347CB007FC439 /* Frameworks */,
				065857642B2347CB007FC439 /* Resources */,
				0658576D2B2347CB007FC439 /* Bundle React Native code and images */,
				D7BED1A13DB91D9F11B643E8 /* [CP] Embed Pods Frameworks */,
				FE52101427BD87BE35BCF7D8 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = aralpulse;
			productName = bppulse;
			productReference = 0658577A2B2347CB007FC439 /* aralpulse.app */;
			productType = "com.apple.product-type.application";
		};
		13B07F861A680F5B00A75B9A /* bppulse */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "bppulse" */;
			buildPhases = (
				4C85F3FBCFF93F0AD13A8556 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				8820D1B37A9B09EECEB55255 /* [CP] Embed Pods Frameworks */,
				47429368540EF2614BF133B8 /* [CP] Copy Pods Resources */,
				DDCEBD9A0F7AE84307090DFB /* [CP-User] [RNFB] Core Configuration */,
				84666A487C8500270749A57F /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = bppulse;
			productName = bppulse;
			productReference = 13B07F961A680F5B00A75B9A /* bppulse.app */;
			productType = "com.apple.product-type.application";
		};
		2144EE572C6D4A0B00F384D7 /* bpUSpulse */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2144EE662C6D4A0B00F384D7 /* Build configuration list for PBXNativeTarget "bpUSpulse" */;
			buildPhases = (
				661438ABA3F419105160B248 /* [CP] Check Pods Manifest.lock */,
				2144EE592C6D4A0B00F384D7 /* Start Packager */,
				2144EE5A2C6D4A0B00F384D7 /* Sources */,
				2144EE5D2C6D4A0B00F384D7 /* Frameworks */,
				2144EE5F2C6D4A0B00F384D7 /* Resources */,
				2144EE632C6D4A0B00F384D7 /* Bundle React Native code and images */,
				637B7396A4F05F823641DD06 /* [CP] Embed Pods Frameworks */,
				0BD5ED8BEE263FE66876AFC8 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = bpUSpulse;
			productName = bppulse;
			productReference = 2144EE6E2C6D4A0B00F384D7 /* bpUSpulse.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "bppulse" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* bppulse */,
				00E356ED1AD99517003FC87E /* bppulseTests */,
				0658575C2B2347CB007FC439 /* aralpulse */,
				2144EE572C6D4A0B00F384D7 /* bpUSpulse */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		065857642B2347CB007FC439 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				06FA349F2CAD87DF0001CA65 /* GoogleService-Info.plist in Resources */,
				065857652B2347CB007FC439 /* BootSplash.storyboard in Resources */,
				065857662B2347CB007FC439 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AB99E83C264E9516000D839E /* BootSplash.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				AB4B466C26B312D900327F64 /* GoogleService-Info.plist in Resources */,
				336EAEE52732497698D5249C /* Roboto-Black.ttf in Resources */,
				CA77DC2CACAB4E959D1ECEB5 /* Roboto-BlackItalic.ttf in Resources */,
				BD3362B0112743C1833490BE /* Roboto-BoldItalic.ttf in Resources */,
				F6528696CD994AD5929D18E6 /* Roboto-Italic.ttf in Resources */,
				C93993D566864FD7B8A20348 /* Roboto-Light.ttf in Resources */,
				4DCBD98BA6BC4D959DC16FCE /* Roboto-LightItalic.ttf in Resources */,
				15632D50FA024F318A41E2EB /* Roboto-MediumItalic.ttf in Resources */,
				AF990CCDA74944A6B8152CBA /* Roboto-Thin.ttf in Resources */,
				BEF2A71140D54C41B5245E5C /* Roboto-ThinItalic.ttf in Resources */,
				95A68C2F275946F4BBE6E7DB /* AralPOS-Regular.ttf in Resources */,
				EDE0345F643F4B9799F47394 /* Roboto-Bold.ttf in Resources */,
				A96B04F4463D4A45A7B4CC25 /* Roboto-Medium.ttf in Resources */,
				329F9CF558E3483180F1027F /* Roboto-Regular.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2144EE5F2C6D4A0B00F384D7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				06FA34A12CAD87F00001CA65 /* GoogleService-Info.plist in Resources */,
				2144EE612C6D4A0B00F384D7 /* BootSplash.storyboard in Resources */,
				2144EE622C6D4A0B00F384D7 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		0658575E2B2347CB007FC439 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		0658576D2B2347CB007FC439 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		0BD5ED8BEE263FE66876AFC8 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		101D2066D10889FC9E5EBDC1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bppulse-bppulseTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		2144EE592C6D4A0B00F384D7 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		2144EE632C6D4A0B00F384D7 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		3288A44CC2A15EBE41F445A0 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		47429368540EF2614BF133B8 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse/Pods-bppulse-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse/Pods-bppulse-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse/Pods-bppulse-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4C85F3FBCFF93F0AD13A8556 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bppulse-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		637B7396A4F05F823641DD06 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse-bpUSpulse/Pods-bppulse-bpUSpulse-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		661438ABA3F419105160B248 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bppulse-bpUSpulse-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7BABB27B64B09EBDF1B35E15 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse-bppulseTests/Pods-bppulse-bppulseTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		84666A487C8500270749A57F /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		8820D1B37A9B09EECEB55255 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse/Pods-bppulse-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse/Pods-bppulse-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse/Pods-bppulse-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AEDEDA1769683C799E4283B4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-bppulse-aralpulse-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D7BED1A13DB91D9F11B643E8 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DDCEBD9A0F7AE84307090DFB /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FE52101427BD87BE35BCF7D8 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-bppulse-aralpulse/Pods-bppulse-aralpulse-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* bppulseTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0658575F2B2347CB007FC439 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				065857602B2347CB007FC439 /* AppDelegate.mm in Sources */,
				065857612B2347CB007FC439 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				AB43B9DA286EA83E002ED69E /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2144EE5A2C6D4A0B00F384D7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2144EE5B2C6D4A0B00F384D7 /* AppDelegate.mm in Sources */,
				2144EE5C2C6D4A0B00F384D7 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* bppulse */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BBBED69696D8503A0589EA31 /* Pods-bppulse-bppulseTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7E128CC4F06C9F3153201A09 /* Pods-bppulse-bppulseTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Release;
		};
		065857732B2347CB007FC439 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E02C2E2923DC41968919DE55 /* Pods-bppulse-aralpulse.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		065857742B2347CB007FC439 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0F62A3D8856C3D40555B058C /* Pods-bppulse-aralpulse.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		065857752B2347CB007FC439 /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 38E0C5A04D26CD464DF37D29 /* Pods-bppulse-aralpulse.dev.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Dev;
		};
		065857762B2347CB007FC439 /* Test */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3DD16C42855384ADCAB12A45 /* Pods-bppulse-aralpulse.test.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Test;
		};
		065857772B2347CB007FC439 /* Preprod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A3F4A31E6110FB318564D881 /* Pods-bppulse-aralpulse.preprod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Preprod;
		};
		065857782B2347CB007FC439 /* Performance */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 37D34B527A358863FC80398E /* Pods-bppulse-aralpulse.performance.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Performance;
		};
		065857792B2347CB007FC439 /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6A583A0DE57540D5F41981BE /* Pods-bppulse-aralpulse.prod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = aralpulse/aralpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = aralpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Aral pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.aral";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Prod;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6A880B30BAC1160813FB4AB0 /* Pods-bppulse.debug.xcconfig */;
			buildSettings = {
				AF_APP_ID = 111158679;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.debug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.debug";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 98A82EED5AA44B6F0B06ADAF /* Pods-bppulse.release.xcconfig */;
			buildSettings = {
				AF_APP_ID = 1515768723;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = YEHYU4D47N;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 4.1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "BP Pulse Global EV App Production";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "BP Pulse";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2144EE672C6D4A0B00F384D7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B672BCB3B2195B558E071F58 /* Pods-bppulse-bpUSpulse.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.14.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.us.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		2144EE682C6D4A0B00F384D7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 21C664CA1601736A9A7F1712 /* Pods-bppulse-bpUSpulse.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.14.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.bppulse.us;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		2144EE692C6D4A0B00F384D7 /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C91F2BA4314A0C3D1677939C /* Pods-bppulse-bpUSpulse.dev.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.us.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Dev;
		};
		2144EE6A2C6D4A0B00F384D7 /* Test */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5DF07272E77309C5C7822393 /* Pods-bppulse-bpUSpulse.test.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.us.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Test;
		};
		2144EE6B2C6D4A0B00F384D7 /* Preprod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FE7FE050993B8AEAF95D9061 /* Pods-bppulse-bpUSpulse.preprod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.us.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Preprod;
		};
		2144EE6C2C6D4A0B00F384D7 /* Performance */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A84700E36679BDB115B20A21 /* Pods-bppulse-bpUSpulse.performance.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.us.debug";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Performance;
		};
		2144EE6D2C6D4A0B00F384D7 /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D0691A3DB283533E125D1E9F /* Pods-bppulse-bpUSpulse.prod.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bpUSpulse/bpUSpulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.14.0;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = bpUSpulse/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "bp US pulse";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.bppulse.us;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Prod;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		ABA0D813268E1F9C00319A73 /* Dev */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Dev;
		};
		ABA0D814268E1F9C00319A73 /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 561900409DCD030A3E874534 /* Pods-bppulse.dev.xcconfig */;
			buildSettings = {
				AF_APP_ID = 111158679;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.debug";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Dev;
		};
		ABA0D815268E1F9C00319A73 /* Dev */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2B5BBA6AEAF86FEA12BEF488 /* Pods-bppulse-bppulseTests.dev.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Dev;
		};
		ABA0D816268E1FA500319A73 /* Test */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Test;
		};
		ABA0D817268E1FA500319A73 /* Test */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4B7D536B75680CB34D06543E /* Pods-bppulse.test.xcconfig */;
			buildSettings = {
				AF_APP_ID = 111158679;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.debug";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Test;
		};
		ABA0D818268E1FA500319A73 /* Test */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 99336C0160CED184F0705316 /* Pods-bppulse-bppulseTests.test.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Test;
		};
		ABA0D819268E1FA900319A73 /* Preprod */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Preprod;
		};
		ABA0D81A268E1FA900319A73 /* Preprod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2E7901F007568ED9061E08D9 /* Pods-bppulse.preprod.xcconfig */;
			buildSettings = {
				AF_APP_ID = 111158679;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = YEHYU4D47N;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.debug";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "BP Pulse Global EV App Wildcard";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "BP Pulse Ad Hoc Debug";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Preprod;
		};
		ABA0D81B268E1FA900319A73 /* Preprod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0FAE8EA5C22212B4C906CC4D /* Pods-bppulse-bppulseTests.preprod.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Preprod;
		};
		ABA0D81C268E1FB300319A73 /* Prod */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Prod;
		};
		ABA0D81D268E1FB300319A73 /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 06C7AC7CE414A83A5D00C296 /* Pods-bppulse.prod.xcconfig */;
			buildSettings = {
				AF_APP_ID = 1515768723;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.prod.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = YEHYU4D47N;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "BP Pulse Global EV App Production";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "BP Pulse";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Prod;
		};
		ABA0D81E268E1FB300319A73 /* Prod */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 09F245B7B3CBA3E9E89B7887 /* Pods-bppulse-bppulseTests.prod.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Prod;
		};
		EAA032302971A53100F738C7 /* Performance */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Performance;
		};
		EAA032312971A53100F738C7 /* Performance */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 21AF5DEAE37E0288D1141C57 /* Pods-bppulse.performance.xcconfig */;
			buildSettings = {
				AF_APP_ID = "";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = bppulse/bppulse.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 4.1.0;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulse/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.aml.ev-app.debug";
				PRODUCT_NAME = bppulse;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Performance;
		};
		EAA032322971A53100F738C7 /* Performance */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 802D34AE687B00D147FB7D6D /* Pods-bppulse-bppulseTests.performance.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3T4NPNJU5J;
				INFOPLIST_FILE = bppulseTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/bppulse.app/bppulse";
			};
			name = Performance;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "bppulseTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
				ABA0D815268E1F9C00319A73 /* Dev */,
				ABA0D818268E1FA500319A73 /* Test */,
				ABA0D81B268E1FA900319A73 /* Preprod */,
				EAA032322971A53100F738C7 /* Performance */,
				ABA0D81E268E1FB300319A73 /* Prod */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		065857722B2347CB007FC439 /* Build configuration list for PBXNativeTarget "aralpulse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				065857732B2347CB007FC439 /* Debug */,
				065857742B2347CB007FC439 /* Release */,
				065857752B2347CB007FC439 /* Dev */,
				065857762B2347CB007FC439 /* Test */,
				065857772B2347CB007FC439 /* Preprod */,
				065857782B2347CB007FC439 /* Performance */,
				065857792B2347CB007FC439 /* Prod */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "bppulse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
				ABA0D814268E1F9C00319A73 /* Dev */,
				ABA0D817268E1FA500319A73 /* Test */,
				ABA0D81A268E1FA900319A73 /* Preprod */,
				EAA032312971A53100F738C7 /* Performance */,
				ABA0D81D268E1FB300319A73 /* Prod */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2144EE662C6D4A0B00F384D7 /* Build configuration list for PBXNativeTarget "bpUSpulse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2144EE672C6D4A0B00F384D7 /* Debug */,
				2144EE682C6D4A0B00F384D7 /* Release */,
				2144EE692C6D4A0B00F384D7 /* Dev */,
				2144EE6A2C6D4A0B00F384D7 /* Test */,
				2144EE6B2C6D4A0B00F384D7 /* Preprod */,
				2144EE6C2C6D4A0B00F384D7 /* Performance */,
				2144EE6D2C6D4A0B00F384D7 /* Prod */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "bppulse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
				ABA0D813268E1F9C00319A73 /* Dev */,
				ABA0D816268E1FA500319A73 /* Test */,
				ABA0D819268E1FA900319A73 /* Preprod */,
				EAA032302971A53100F738C7 /* Performance */,
				ABA0D81C268E1FB300319A73 /* Prod */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
