jobs:
  - template: shared/jobs/test_coverage.yml@pipelines
    parameters:
      mainFolder: $(Pipeline.Workspace)/main
      prFolder: $(Pipeline.Workspace)/pr
      testCommand: cd app && yarn test:ci --forceExit
      projectName: b40b1406-1bfa-452b-94d5-79447fc92e9b
      repoId: 280cf776-c9e5-4886-a446-c7b9e1a276d0
      steps:
        - checkout: git://bp_pulse/bp-pulse-mobile@main
          path: main
        - checkout: self
          path: pr
