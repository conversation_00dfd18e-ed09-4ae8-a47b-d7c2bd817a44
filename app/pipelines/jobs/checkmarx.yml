# https://create.bpglobal.com/section/technologies/services/development-security-tools/security-tools-pipeline-integration-guides/azure-devops-integration/sast-pipeline-integration-azure-devops-~-yaml
# https://create.bpglobal.com/section/technologies/services/development-security-tools/security-tools-pipeline-integration-guides/azure-devops-integration/sca-pipeline-integration-azure-devops-~-yaml
jobs:
  - job: SASTScanJob
    displayName: SAST Scan
    pool:
      vmImage: 'ubuntu-latest'

    continueOnError: true

    steps:
      - template: /templates-common/cx_ado_template/cx-ado-template.yml@bp-via-checkmarx
        parameters:
          releaseBranches:
            - main
          repoNames:
            - $(Build.Repository.Name)
          repoIDS:
            - $(Build.Repository.ID)
          repoBranchNames:
            - $(Build.Repository.Name)
          snowApplicationPackageNumber: SPKG0022546
          snowApplicationPackageName: EV-Platform
          projectInformalName: OTG-EMSP
          itServiceName: bp pulse digital channel and services
          itServiceNumber: ITSVC0010677
          scaServiceConnection: checkmarx-sca-SPKG0022546
          sastServiceConnection: checkmarx-sast-SPKG0022546
          isMicroService: no
          microserviceOrMonolithicAppName: $(Build.Repository.Name)-app
          sca: false
          incrementalScan: true
          vulnerabilityThreshold: true
          thresholdHigh: 0

  - job: SCAScanJob
    displayName: SCA Scan
    pool:
      vmImage: 'ubuntu-latest'

    continueOnError: true

    steps:
      - template: /templates-common/cx_ado_template/cx-ado-template.yml@bp-via-checkmarx
        parameters:
          releaseBranches:
            - main
          repoNames:
            - $(Build.Repository.Name)
          repoIDS:
            - $(Build.Repository.ID)
          repoBranchNames:
            - $(Build.Repository.Name)
          snowApplicationPackageNumber: SPKG0022546
          snowApplicationPackageName: EV-Platform
          projectInformalName: OTG-EMSP
          itServiceName: bp pulse digital channel and services
          itServiceNumber: ITSVC0010677
          scaServiceConnection: checkmarx-sca-SPKG0022546
          sastServiceConnection: checkmarx-sast-SPKG0022546
          isMicroService: no
          microserviceOrMonolithicAppName: $(Build.Repository.Name)-app
          sast: false
          incrementalScan: true
          scaThreshold: true
          scaHigh: 0
