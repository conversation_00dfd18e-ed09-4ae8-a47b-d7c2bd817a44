parameters:
  - name: brand
    type: string

jobs:
  - job: build_android
    displayName: build android
    timeoutInMinutes: 180
    pool:
      vmImage: ubuntu-latest

    variables:
      - group: Appium_E2E

    steps:
      - template: ../steps/secure_e2e_test_gmail.yml
        parameters:
          brand: ${{ parameters.brand }}

      # Maven v3
      # Build, test, and deploy with Apache Maven.
      - task: Maven@3

        env:
          SAUCE_ACCESS_KEY: $(SAUCE_ACCESS_KEY)
          TWILIO_ACCOUNT_SID: $(TWILIO_ACCOUNT_SID)
          TWILIO_AUTH_TOKEN: $(TWILIO_AUTH_TOKEN)
          FORGEROCK_USERNAME: $(FORGEROCK_USERNAME)
          FORGEROCK_PASSWORD: $(FORGEROCK_PASSWORD)
          FORGEROCK_CLIENT_ID: $(FORGEROCK_CLIENT_ID)
          FORGEROCK_CLIENT_SECRET: $(FORGEROCK_CLIENT_SECRET)
          E2E_CIP_CLIENT_ID: $(E2E_CIP_CLIENT_ID)
          E2E_CIP_CLIENT_SECRET: $(E2E_CIP_CLIENT_SECRET)
          MULE_CLIENT_ID: $(MULE_CLIENT_ID)
          MULE_CLIENT_SECRET: $(MULE_CLIENT_SECRET)
          FACEBOOK_PASSWOR1: $(FACEBOOK_PASSWOR1)
          FACEBOOK_PASSWOR2: $(FACEBOOK_PASSWOR2)
          FACEBOOK_PASSWORD_US_1: $(FACEBOOK_PASSWORD_US_1)
          GMAIL_PASSWORD1: $(GMAIL_PASSWORD1)
          GMAIL_PASSWORD_US_1: $(GMAIL_PASSWORD_US_1)
          APP_BRAND: ${{ parameters.brand }}
          TEST_EMAIL: $(TEST_EMAIL)
          MOBILE_PLATFORM: Android
          ${{ if eq(parameters.brand, 'bpUS') }}:
            CUCUMBER_FILTER_TAGS: '@Android and @bpUS'
          ${{ if eq(parameters.brand, 'bp') }}:
            CUCUMBER_FILTER_TAGS: '@Android and @bpGlobal'

        inputs:
          mavenPOMFile: 'app/e2e/AppiumJava/pom.xml' # string. Required. Maven POM file. Default: pom.xml.
          goals: 'test -DfailIfNoTests=false -Dsurefire.suiteXmlFiles=src/test/resources/testng.xml'
          #goals: 'package' # string. Goal(s). Default: package.
          #options: # string. Options.
          # JUnit Test Results
          #publishJUnitResults: true # boolean. Publish to Azure Pipelines. Default: true.
          testResultsFiles: '**/surefire-reports/TEST-*.xml' # string. Required when publishJUnitResults = true. Test results files. Default: **/surefire-reports/TEST-*.xml.
          #testRunTitle: # string. Optional. Use when publishJUnitResults = true. Test run title.
          #allowBrokenSymlinks: true # boolean. Alias: allowBrokenSymbolicLinks. Optional. Use when publishJUnitResults = true. Allow broken symbolic links. Default: true.
          # Code Coverage
          #codeCoverageToolOption: 'None' # 'None' | 'Cobertura' | 'JaCoCo'. Alias: codeCoverageTool. Code coverage tool. Default: None.
          #codeCoverageClassFilter: # string. Alias: classFilter. Optional. Use when codeCoverageTool != None. Class inclusion/exclusion filters.
          #codeCoverageClassFilesDirectories: # string. Alias: classFilesDirectories. Optional. Use when codeCoverageTool = JaCoCo. Class files directories.
          #codeCoverageSourceDirectories: # string. Alias: srcDirectories. Optional. Use when codeCoverageTool = JaCoCo. Source files directories.
          #codeCoverageFailIfEmpty: false # boolean. Alias: failIfCoverageEmpty. Optional. Use when codeCoverageTool != None. Fail when code coverage results are missing. Default: false.
          #codeCoverageRestoreOriginalPomXml: false # boolean. Alias: restoreOriginalPomXml. Optional. Use when codeCoverageTool != None. Restore original pom.xml after task execution. Default: false.
          # Advanced
          javaHomeOption: 'JDKVersion' # 'JDKVersion' | 'Path'. Alias: javaHomeSelection. Required. Set JAVA_HOME by. Default: JDKVersion.
          #jdkVersionOption: 'default' # 'default' | '1.17' | '1.11' | '1.10' | '1.9' | '1.8' | '1.7' | '1.6'. Alias: jdkVersion. Optional. Use when javaHomeSelection = JDKVersion. JDK version. Default: default.
          #jdkDirectory: # string. Alias: jdkUserInputPath. Required when javaHomeSelection = Path. JDK path.
          #jdkArchitectureOption: 'x64' # 'x86' | 'x64'. Alias: jdkArchitecture. Optional. Use when jdkVersion != default. JDK architecture. Default: x64.
          mavenVersionOption: 'Default'
