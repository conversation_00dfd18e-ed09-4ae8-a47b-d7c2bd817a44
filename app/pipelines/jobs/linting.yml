jobs:
  - job: unit_tests
    displayName: linting
    pool:
      vmImage: 'ubuntu-latest'
    steps:
      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - script: |
          yarn types
        workingDirectory: app
        displayName: types check

      - script: |
          yarn lint
        workingDirectory: app
        displayName: linting
