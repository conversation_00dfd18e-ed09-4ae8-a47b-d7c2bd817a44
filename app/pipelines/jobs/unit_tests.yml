jobs:
  - job: unit_tests
    displayName: unit tests
    pool:
      vmImage: 'ubuntu-latest'

    steps:
      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - script: |
          yarn test:ci
        workingDirectory: app
        displayName: unit tests

      - task: PublishTestResults@2
        displayName: 'results: publish'
        condition: succeededOrFailed()
        inputs:
          testRunner: JUnit
          testResultsFiles: 'app/test-results/jest/junit.xml'
          failTaskOnFailedTests: true

      - task: PublishCodeCoverageResults@2
        displayName: 'coverage: publish'
        inputs:
          summaryFileLocation: $(System.DefaultWorkingDirectory)/app/coverage/cobertura-coverage.xml

      - publish: $(System.DefaultWorkingDirectory)/app/coverage
        artifact: TestCoverage

      - publish: $(System.DefaultWorkingDirectory)/app/test-results
        artifact: TestResults
