parameters:
  - name: versionNumber
    type: string
  - name: buildNumber
    type: string
  - name: environment
    type: string
  - name: runMaestro
    type: boolean
    default: false
  - name: useADOAgent
    type: boolean
    default: false
  - name: brand
    type: string

jobs:
  - job: build_ios
    displayName: build iOS
    workspace:
      clean: all
    variables:
      - group: Appium_E2E
      - ${{ if eq(parameters.runMaestro, true) }}:
          - group: 'bp-pulse-maestro-${{ parameters.environment }}'
          - group: 'Appium_E2E'

    ${{ if eq(parameters.useADOAgent, true) }}:
      pool:
        vmImage: 'macOS-15'
    ${{ if eq(parameters.useADOAgent, false) }}:
      pool: MobileBuildAgentPool

    steps:
      - checkout: self
        persistCredentials: true
        clean: true
        path: s/

      - task: InstallAppleCertificate@2
        inputs:
          certSecureFile: $(IOS_CERT_FILE)
          certPwd: $(P12_CERT_PASSWORD)
          keychain: 'temp'
          deleteCert: true
          ${{ if eq(parameters.useADOAgent, false) }}:
            opensslPkcsArgs: '-legacy'
        displayName: install apple certificate

      - task: InstallAppleProvisioningProfile@1
        displayName: 'Install Apple Provisioning Profile'
        inputs:
          provisioningProfileLocation: secureFiles
          provProfileSecureFile: $(IOS_PROVISIONING_PROFILE)
          removeProfile: true

      - task: UpdateiOSVersionInfoPlist@1
        inputs:
          infoPlistPath: 'app/ios/$(IOS_SCHEME)/Info.plist'
          bundleShortVersionString: '${{ parameters.versionNumber }}' # Optional. Default is: $(MAJOR).$(MINOR).$(PATCH)
          bundleVersion: '${{ parameters.buildNumber }}' # Optional. Default is: $(NUMBER_OF_COMMITS)

      - script: | # Show the content of the Info.plist file for each environment
          cat app/ios/$(IOS_SCHEME)/Info.plist
        displayName: show info plist

      - ${{ if eq(parameters.useADOAgent, true) }}: # we can remove this section when the following issue is resolved: https://github.com/actions/runner-images/issues/9308
          - script: | # install cocoapods 1.15.
              gem install cocoapods -v 1.15.2
            displayName: install cocoapods 1.15.2

      - ${{ if eq(parameters.useADOAgent, true) }}:
          - script: | # set xcode version location
              export XCODE_16_DEVELOPER_DIR=/Applications/Xcode_16.2.app
            displayName: set xcode developer dir path

      - template: ../steps/secure_google_services.yml
        parameters:
          os: ios

      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml
      - script: |
          git config --global url."https://$(System.AccessToken)@dev.azure.com/".insteadOf "https://dev.azure.com/"
        displayName: Git give accesstoken
      - script: |
          sed -i '' -e 's,https://dev.azure.com/,https://$(System.AccessToken)@dev.azure.com/,g' Podfile
        displayName: Apply git access token to podfile
        workingDirectory: app/ios
      - template: ../steps/pod_install.yml

      - script: |
          echo Build number: ${{ parameters.buildNumber }}, Version number: ${{ parameters.versionNumber }}
          yarn generate-app-env
        displayName: generate app env
        # Env vars set in the variables of the pipeline
        workingDirectory: app
        env:
          G_MAPS_URL: $(G_MAPS_URL)
          G_MAPS_KEY: $(G_MAPS_KEY)
          G_MAPS_IOS: $(G_MAPS_IOS)
          API_GATEWAY_KEY: $(API_GATEWAY_KEY)

      - ${{ if eq(parameters.runMaestro, false) }}:
          - ${{ if ne(parameters.environment, 'prod') }}:
              - template: '../steps/generate_app_icon.yml'
                parameters:
                  versionNumber: ${{ parameters.versionNumber }}
                  environment: ${{ parameters.environment }}
                  os: ios
                  useADOAgent: ${{ parameters.useADOAgent }}

          - task: Xcode@5
            displayName: 'Build IPA'
            inputs:
              xcodeVersion: '16'
              actions: 'clean'
              configuration: '$(RELEASE_VARIANT)'
              sdk: 'iphoneos'
              xcWorkspacePath: 'app/ios/bppulse.xcworkspace'
              scheme: '$(IOS_SCHEME)'
              packageApp: true
              exportPath: 'output'
              signingOption: 'manual'
              signingIdentity: '$(APPLE_CERTIFICATE_SIGNING_IDENTITY)'
              provisioningProfileUuid: '$(APPLE_PROV_PROFILE_UUID)'
              useXcpretty: false

          - ${{ if or(and(eq(parameters.environment, 'dev'), eq(parameters.brand, 'bpUS')), eq(parameters.environment, 'test'), eq(parameters.environment, 'preprod')) }}:
              - script: |
                  curl --connect-timeout 60 -v -F "payload=@$(IOS_SCHEME).ipa" -F name="$(IOS_SCHEME)$(RELEASE_VARIANT).ipa" --anyauth -u "$SAUCE_USERNAME:$SAUCE_ACCESS_KEY"  "https://api.eu-central-1.saucelabs.com/v1/storage/upload?overwrite=true"
                displayName: Upload ipa to SauceLabs
                workingDirectory: output

          - ${{ if or(and(eq(parameters.environment, 'dev'), eq(parameters.brand, 'bpUS')), eq(parameters.environment, 'test'), eq(parameters.environment, 'preprod'), eq(parameters.environment, 'prod')) }}:
              - template: ../steps/ios_test_flight.yml
                parameters:
                  buildNumber: ${{ parameters.buildNumber }}
                  versionNumber: ${{ parameters.versionNumber }}
                  brand: ${{ parameters.brand }}

          - ${{ if or(eq(parameters.environment, 'preprod'), eq(parameters.environment, 'prod')) }}:
              - task: PublishBuildArtifacts@1
                inputs:
                  pathToPublish: 'output/$(IOS_SCHEME).ipa'
                  artifactName: iOS ipa

          - ${{ if ne(parameters.environment, 'pr') }}:
              - script: |
                  find $(Build.SourcesDirectory)/ -name "*.dSYM" | xargs -I \{\} ./Pods/FirebaseCrashlytics/upload-symbols -gsp ./$(IOS_SCHEME)/GoogleService-Info.plist -p ios \{\}
                workingDirectory: app/ios
                displayName: Upload dSYM to Firebase

      - ${{ if eq(parameters.runMaestro, true) }}:
          - template: ../steps/maestro.yml
            parameters:
              versionNumber: ${{ parameters.versionNumber }}
              buildNumber: ${{ parameters.buildNumber }}
              environment: ${{ parameters.environment }}
