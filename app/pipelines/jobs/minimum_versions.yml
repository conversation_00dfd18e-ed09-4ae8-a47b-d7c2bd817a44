jobs:
  - job: minimum_versions
    displayName: Minimum versions
    continueOnError: true
    steps:
      - checkout: self
        persistCredentials: true
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - script: |
          mv package.json package-new.json
          git fetch origin main:main
          git checkout origin/main -- package.json
        workingDirectory: app

      - script: |
          npx ts-node scripts/minimum-versions/minimum-versions.ts
        displayName: Minimum versions
        workingDirectory: app
