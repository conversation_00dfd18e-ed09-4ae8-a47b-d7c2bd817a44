parameters:
  - name: versionNumber
    type: string
  - name: buildNumber
    type: string
  - name: environment
    type: string
  - name: useADOAgent
    type: boolean
    default: true
  - name: brand
    type: string

jobs:
  - job: build_android
    displayName: build Android

    variables:
      - group: Appium_E2E

    ${{ if eq(parameters.useADOAgent, true) }}:
      pool:
        vmImage: 'macOS-15'
    ${{ if eq(parameters.useADOAgent, false) }}:
      pool: MobileBuildAgentPool

    steps:
      - ${{ if eq(parameters.useADOAgent, false) }}:
          - script: |
              echo $PATH && java --version
            displayName: java version and PATH

      - ${{ if eq(parameters.useADOAgent, true) }}:
          - task: JavaToolInstaller@1
            inputs:
              versionSpec: '11'
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'

      - template: ../steps/secure_android_keystore.yml
      - template: ../steps/secure_google_services.yml

      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - template: ../steps/gradle_properties.yml
        parameters:
          VERSION_CODE: ${{ parameters.buildNumber }}
          VERSION_NUMBER: ${{ parameters.versionNumber }}

      - script: |
          yarn generate-app-env
        displayName: generate .env file
        workingDirectory: app
        env:
          G_MAPS_URL: $(G_MAPS_URL)
          G_MAPS_KEY: $(G_MAPS_KEY)
          G_MAPS_ANDROID: $(G_MAPS_ANDROID)
          API_GATEWAY_KEY: $(API_GATEWAY_KEY)

      - ${{ if ne(parameters.environment, 'prod') }}:
          - template: '../steps/generate_app_icon.yml'
            parameters:
              versionNumber: ${{ parameters.versionNumber }}
              environment: ${{ parameters.environment }}
              useADOAgent: ${{ parameters.useADOAgent }}

      - template: ../steps/gradle_build.yml
        parameters:
          tasks: $(BUNDLE_FLAVOR)
          useADOAgent: ${{ parameters.useADOAgent }}

      #use google_play if any of the following is true:
      # non-PR env
      # non-bpUS brand
      # bpUS brand in non-prod and preprod env
      - ${{ if and(ne(parameters.environment, 'pr'), or(ne(parameters.brand, 'bpUS'), and(ne(parameters.environment, 'prod'), ne(parameters.environment, 'preprod')))) }}:
          - template: ../steps/google_play.yml
            parameters:
              environment: ${{ parameters.environment }}
              buildNumber: ${{ parameters.buildNumber }}
              versionNumber: ${{ parameters.versionNumber }}
              brand: ${{ parameters.brand }}
      # use us_google_play if brand is bpUS and env is preprod or prod
      - ${{ if and(or(eq(parameters.environment, 'prod'), eq(parameters.environment, 'preprod')), eq(parameters.brand, 'bpUS')) }}:
          - template: ../steps/us_google_play.yml
            parameters:
              environment: ${{ parameters.environment }}
              buildNumber: ${{ parameters.buildNumber }}
              versionNumber: ${{ parameters.versionNumber }}
              brand: ${{ parameters.brand }}

      - ${{ if or(and(eq(parameters.environment, 'dev'), eq(parameters.brand, 'bpUS')), eq(parameters.environment, 'test'), eq(parameters.environment, 'preprod')) }}:
          - script: |
              cd android/app/build/outputs/bundle/
              echo "apk file: $(AAB_FILE_NAME)"
              cd $(STAGE)Release/
              curl --connect-timeout 60 -v -F "payload=@$(AAB_FILE_NAME)" -F name="$(AAB_FILE_NAME)" --anyauth -u "$SAUCE_USERNAME:$SAUCE_ACCESS_KEY"  "https://api.eu-central-1.saucelabs.com/v1/storage/upload?overwrite=true"
            displayName: Upload aab to SauceLabs
            workingDirectory: app

