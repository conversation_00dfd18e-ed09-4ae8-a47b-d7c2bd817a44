steps:
  - checkout: MaestroServer
    fetchDepth: 1
  - script: |
      sh app/scripts/generate-maestro-env-vars/generate-maestro-server-env.sh > maestro-server/.env
    displayName: Generate maestro server env
    env:
      TWILIO_ACCOUNT_SID: $(TWILIO_ACCOUNT_SID)
      TWILIO_AUTHTOKEN: $(TWILIO_AUTHTOKEN)
  - script: |
      yarn set version 1.22.22
      yarn install
    workingDirectory: maestro-server
  - script: |
      yarn start > maestro-server.log &
    displayName: Start maestro server
    workingDirectory: maestro-server
