steps:
  - script: yarn extract-deps
    displayName: 'Extract dependencies'
    workingDirectory: app

  - powershell: |
      $webhookUrl = $env:WEBHOOK_URL
      $releaseVariant = $env:RELEASE_VARIANT
      $buildNumber = $env:BUILD_NUMBER
      $versionNumber = $env:VERSION_NUMBER
      $branchName = $env:BRANCH_NAME
      $branchName = $env:BRANCH_NAME
      $message = $env:message
      $brand = $env:brand
      $dependencies = Get-Content -Path ./app/dependencies.txt
      $branchOrTag = ''
      $tag = & git describe --tags --exact-match 2>$null
      if ($LASTEXITCODE -eq 0) {
        $branchOrTag = "Tag: $tag"
      } else {
        $branchOrTag = "Branch: $branchName"
      }
      $body = @{
        text = "$message!`n`nBrand: $brand`n`nEnvironment: $releaseVariant`n`nBuild Number: $buildNumber`n`nApp Version: $versionNumber`n`nBuild Source: $branchOrTag`n`nDependency versions:`n`n$dependencies"
      }
      Invoke-RestMethod -Uri $webhookUrl -Method Post -Body ($body | ConvertTo-Json) -ContentType "application/json"
    displayName: 'Post to Microsoft Teams'
    ignoreLASTEXITCODE: true
    env:
      message: ${{ parameters.message }}
      WEBHOOK_URL: $(BUILD_NOTIFICATION_WEBHOOK)
      RELEASE_VARIANT: $(ReleaseVariant)
      BUILD_NUMBER: ${{ parameters.BUILD_NUMBER }}
      VERSION_NUMBER: ${{ parameters.VERSION_NUMBER }}
      BRANCH_NAME: $(Build.SourceBranchName)
      brand: ${{ parameters.brand }}
