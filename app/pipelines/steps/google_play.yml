steps:
  - task: GooglePlayRelease@4
    inputs:
      serviceConnection: BPCM Google Play
      action: 'SingleBundle'
      applicationId: $(ANDROID_BUNDLE_ID)
      bundleFile: 'app/android/app/build/outputs/bundle/$(STAGE)Release/$(AAB_FILE_NAME)'
      ${{ if eq(parameters.environment, 'dev') }}:
        track: internal
        isDraftRelease: false
      ${{ elseif eq(parameters.environment, 'test') }}:
        track: Test
        isDraftRelease: false
      ${{ elseif eq(parameters.environment, 'preprod') }}:
        track: Preprod
        isDraftRelease: false
      ${{ elseif eq(parameters.environment, 'prod') }}:
        track: production
        isDraftRelease: true
      ${{ else }}:
        track: internal
        isDraftRelease: false
      ${{ if eq(parameters.environment, 'prod') }}:
        releaseName: ${{ parameters.versionNumber }}
      ${{ else }}:
        releaseName: '${{ parameters.versionNumber }} ${{ parameters.environment }}'
    displayName: publish aab to playstore

  - template: ./teams_post_message.yml
    parameters:
      message: 'A new Android Play Store build has completed!'
      BUILD_NUMBER: ${{ parameters.buildNumber }}
      VERSION_NUMBER: ${{ parameters.versionNumber }}
      brand: ${{ parameters.brand }}