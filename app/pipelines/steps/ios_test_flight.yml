steps:
  - task: AppStoreRelease@1
    displayName: 'publish iOS app to TestFlight'
    inputs:
      authType: 'ApiKey'
      apiKeyId: $(APP_STORE_CONNECT_API_KEY_KEY_ID)
      apiKeyIssuerId: $(APP_STORE_CONNECT_API_KEY_ISSUER_ID)
      apitoken: $(APP_STORE_CONNECT_API_KEY_KEY_BASE64)
      appIdentifier: $(APP_STORE_BUNDLE_ID)
      appType: 'iOS'
      releaseTrack: 'TestFlight'
      ipaPath: 'output/$(IOS_SCHEME).ipa'
      shouldSkipWaitingForProcessing: true

  - template: ./teams_post_message.yml
    parameters:
      message: 'A new ios app test flight build has completed!'
      BUILD_NUMBER: ${{ parameters.buildNumber }}
      VERSION_NUMBER: ${{ parameters.versionNumber }}
      brand: ${{ parameters.brand }}
