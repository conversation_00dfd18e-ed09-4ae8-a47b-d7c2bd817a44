parameters:
  - name: VERSION_CODE
    type: string
    default: ''

  - name: VERSION_NUMBER
    type: string
    default: ''

steps:
  - script: |
      yarn generate-gradle-properties
      cat android/gradle.properties
    env:
      ANDROID_KEY_PASS: $(ANDROID_KEY_PASS)
      ANDROID_STORE_PASS: $(ANDROID_STORE_PASS)
      ARTIFACTORY_USERNAME: $(ARTIFACTORY_USERNAME)
      ARTIFACTORY_PASSWORD: $(ARTIFACTORY_PASSWORD)
      VERSION_CODE: ${{ parameters.VERSION_CODE }}
      VERSION_NAME: ${{ parameters.VERSION_NUMBER }}
    displayName: generate gradle.properties
    workingDirectory: app
