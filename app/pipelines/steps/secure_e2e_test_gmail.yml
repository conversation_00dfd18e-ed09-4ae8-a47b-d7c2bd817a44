parameters:
  - name: brand
    type: string

steps:
  - ${{ if eq(parameters.brand, 'bp') }}:
      - task: DownloadSecureFile@1
        name: gmailSecretGlobal
        displayName: fetching gmail secret
        inputs:
          secureFile: '<EMAIL>'

      - script: |
          mkdir -p app/e2e/AppiumJava/src/test/resources/testData/emailCredentials
          cp $(gmailSecretGlobal.secureFilePath) app/e2e/AppiumJava/src/test/resources/testData/emailCredentials

      - task: DownloadSecureFile@1
        name: gmailTokenGlobal
        displayName: fetching gmail token
        inputs:
          secureFile: '<EMAIL>'

      - script: |
          cp $(gmailTokenGlobal.secureFilePath) app/e2e/AppiumJava/src/test/resources/testData/emailCredentials/StoredCredential

  - ${{ if eq(parameters.brand, 'bpUS') }}:
      - task: DownloadSecureFile@1
        name: gmailSecretUS
        displayName: fetching gmail secret
        inputs:
          secureFile: '<EMAIL>'

      - script: |
          mkdir -p app/e2e/AppiumJava/src/test/resources/testData/emailCredentials
          cp $(gmailSecretUS.secureFilePath) app/e2e/AppiumJava/src/test/resources/testData/emailCredentials

      - task: DownloadSecureFile@1
        name: gmailTokenUS
        displayName: fetching gmail token
        inputs:
          secureFile: '<EMAIL>'

      - script: |
          cp $(gmailTokenUS.secureFilePath) app/e2e/AppiumJava/src/test/resources/testData/emailCredentials/StoredCredential
