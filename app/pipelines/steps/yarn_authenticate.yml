steps:
  - template: ./corepack_enable.yml

  # without registry set npmAthetication won't add a token
  - script: |
      echo "@bp:registry=https://pkgs.dev.azure.com/bp-digital/DST-Digital_Cross-Business_Platforms/_packaging/apolloxi-component-library/npm/registry/" >> .npmrc
      echo "always-auth=true" >> .npmrc
    displayName: generate .npmrc

  - task: npmAuthenticate@0
    inputs:
      workingFile: .npmrc

  # replacing npmAuthIdent (which is base64(user:password)) with npmAuthToken (JWT token generated by npmAuthentication)
  - script: |
      yarn config set npmScopes.bp.npmAuthToken $(awk -F "=" '/_authToken/ {print $2}' .npmrc)
    displayName: set npmAuthToken
