steps:
  - ${{ if eq(parameters.os, 'android') }}:
      - script: |
          ls -R app/android/app/build/outputs/bundle/
        displayName: 'List bundle output directory'

      - task: AppCenterDistribute@3
        inputs:
          serverEndpoint: AppcenterPulseApps
          appSlug: BP-Pulse-Apps/$(APPCENTER_NAME_ANDROID)
          appFile: app/android/app/build/outputs/bundle/$(STAGE)Release/$(AAB_FILE_NAME)
          symbolsOption: Android
          destinationType: groups
          releaseNotesOption: input
          releaseNotesInput: 'Release Build'
        displayName: publish android aab to appcenter

      - template: ../steps/teams_post_message.yml
        parameters:
          message: 'A new Android App center build has completed!'
          BUILD_NUMBER: ${{ parameters.buildNumber }}
          VERSION_NUMBER: ${{ parameters.versionNumber }}
          brand: ${{ parameters.brand }}

  - ${{ if eq(parameters.os, 'ios') }}:
      - task: AppCenterDistribute@3
        inputs:
          serverEndpoint: AppcenterPulseApps
          appSlug: BP-Pulse-Apps/$(APPCENTER_NAME_IOS)
          appFile: 'output/$(IOS_SCHEME).ipa'
          symbolsOption: Apple
          destinationType: groups
          releaseNotesOption: input
          releaseNotesInput: 'Release Build'
        displayName: publish iOS app to appcenter

      - template: ../steps/teams_post_message.yml
        parameters:
          message: 'A new ios App center build has completed!'
          BUILD_NUMBER: ${{ parameters.buildNumber }}
          VERSION_NUMBER: ${{ parameters.versionNumber }}
          brand: ${{ parameters.brand }}
