parameters:
  - name: versionNumber
    type: string
  - name: buildNumber
    type: string
  - name: environment
    type: string

steps:
  #  The following are required on Azure hosted agents,
  #  but don't need to run on self hosted

  - template: ../steps/maestro_cli_install.yml

  # - script: |
  #     sudo xcode-select -s /Applications/Xcode_14.2.app
  #   displayName: Select Xcode 14.2

  - template: ../steps/idb_install.yml

  # - script: |
  #     git config --global url."https://$(System.AccessToken)@dev.azure.com/".insteadOf "https://dev.azure.com/"
  #   displayName: Git give accesstoken

  # Use Ruby version
  # Use the specified version of Ruby from the tool cache, optionally adding it to the PATH
  # - task: UseRubyVersion@0
  #   inputs:
  #     versionSpec: '>= 2.7.0'
  #     addToPath: true

  - template: ../steps/maestro_server.yml

  - script: |
      export APP_VERSION=${{ parameters.versionNumber }}
      export BUILD_NUMBER=${{ parameters.buildNumber }}
      sh scripts/generate-maestro-env-vars/generate-maestro-env.sh > maestro/.env
    displayName: generate maestro env
    # Env vars set in the variables of the pipeline
    env:
      MAESTRO_GMAIL_PASSWORD: $(MAESTRO_GMAIL_PASSWORD)
      MAESTRO_PROTON_PASSWORD: $(MAESTRO_PROTON_PASSWORD)
      ENVIRONMENT: ${{ parameters.environment }}
    workingDirectory: app

  - script: |
      xcrun simctl boot "iPhone 15"
    displayName: launch simulator
    continueOnError: true

  - script: |
      xcrun xcodebuild -scheme 'bppulse' -workspace 'app/ios/bppulse.xcworkspace' -configuration '$(RELEASE_VARIANT)' -sdk 'iphonesimulator' -destination 'platform=iOS Simulator,name=iPhone 15' -derivedDataPath build
    displayName: Build for simulator

  - script: |
      xcrun simctl install "iPhone 15" "build/Build/Products/$(RELEASE_VARIANT)-iphonesimulator/bppulse.app"
    displayName: install app on simulator

  - script: |
      sh ./scripts/runMaestro.sh < /dev/null
    displayName: run maestro tests
    continueOnError: true
    workingDirectory: app

  - task: PublishBuildArtifacts@1
    inputs:
      pathToPublish: './app/logs/.maestro/tests'
      artifactName: debugLogs

  - task: PublishTestResults@2
    displayName: 'results: publish'
    condition: succeededOrFailed()
    inputs:
      testRunTitle: Maestro
      testRunner: JUnit
      testResultsFiles: './app/report.xml'
      failTaskOnFailedTests: true
