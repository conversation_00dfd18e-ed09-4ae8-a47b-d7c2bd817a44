steps:
  # This is to force the ci to only clear the cache when the lock file has been changed by a developer and not the ci pipeline
  - script: |
      cp app/ios/Podfile.lock app/ios/Podfile.lockkey

  - task: Cache@2
    inputs:
      key: 'pods | "$(Agent.OS)" | app/ios/Podfile.lockkey'
      path: 'app/ios/Pods'
    displayName: 'CocoaPods cache'

  - task: CocoaPods@0
    inputs:
      workingDirectory: app/ios
      forceRepoUpdate: true
    displayName: 'pod install'
    retryCountOnTaskFailure: 1
