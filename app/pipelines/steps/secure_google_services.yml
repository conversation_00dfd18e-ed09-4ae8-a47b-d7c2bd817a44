parameters:
  - name: os
    default: android
    values:
      - android
      - ios

steps:
  - ${{ if eq(parameters.os, 'android') }}:
      - task: DownloadSecureFile@1
        name: plist
        displayName: fetching google plist
        inputs:
          secureFile: $(ANDROID_GOOGLE_SERVICES)

      - script: |
          cp $(plist.secureFilePath) android/app/google-services.json
        displayName: apply firebase config
        workingDirectory: app

  - ${{ if eq(parameters.os, 'ios') }}:
      - task: DownloadSecureFile@1
        name: plist
        displayName: fetching google plist
        inputs:
          secureFile: $(IOS_GOOGLE_SERVICES)

      - script: |
          cp $(Agent.TempDirectory)/$(IOS_GOOGLE_SERVICES) ios/$(IOS_SCHEME)/GoogleService-Info.plist
        displayName: apply firebase config
        workingDirectory: app
