parameters:
  - name: versionNumber
    type: string
  - name: environment
    type: string
  - name: useADOAgent
    type: boolean
  - name: os
    default: android
    values:
      - android
      - ios

steps:
  - ${{ if eq(parameters.useADOAgent, true) }}:
      - script: |
          brew install imagemagick
        displayName: Install imagemagick

      - script: |
          sudo gem install badge
          echo ${{ parameters.environment }}-${{ parameters.versionNumber }}-orange
        displayName: Install badge

  - ${{ if eq(parameters.os, 'android') }}:
      - script: |
          badge --shield "${{ parameters.environment }}-${{ parameters.versionNumber }}-orange" --no_badge --glob "./**/{ic_launcher.png,ic_launcher_round}"
        workingDirectory: app/android
        displayName: Generate app icon badge

  - ${{ if eq(parameters.os, 'ios') }}:
      - script: |
          badge --shield "${{ parameters.environment }}-${{ parameters.versionNumber }}-orange" --no_badge --glob "./**/*.png"
        workingDirectory: app/ios/bppulse/Images.xcassets/AppIcon.appiconset
        displayName: Generate app icon badge
