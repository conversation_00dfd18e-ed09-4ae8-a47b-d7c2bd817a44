parameters:
  - name: tasks
    type: string
    default: 'bundleProd'
  - name: useADOAgent
    type: boolean
    default: true

steps:
  - ${{ if eq(parameters.useADOAgent, true) }}:
      - task: Cache@2
        inputs:
          key: 'gradle2 | "$(Agent.OS)" | app/android/app/build.gradle'
          path: $(Agent.BuildDirectory)/.gradle/caches/
        displayName: Configure gradle caching

  - task: Gradle@3
    inputs:
      gradleWrapperFile: 'app/android/gradlew'
      tasks: ${{ parameters.tasks }}
      options: '--build-cache --gradle-user-home $(Agent.BuildDirectory)/.gradle'
      workingDirectory: 'app/android'
    displayName: Build
