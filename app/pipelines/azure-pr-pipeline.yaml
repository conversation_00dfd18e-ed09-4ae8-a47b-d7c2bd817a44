# mobile pipeline: using React Native

trigger:
  batch: true
  branches:
    include:
      - main
  # Pipeline will trigger with any push change made to main branch.
  paths:
    exclude:
      - README.md
      - mfes/*

parameters:
  - name: versionNumber
    displayName: Version Number
    type: string
    default: '1'
  - name: buildNumber
    displayName: Build Number
    type: string
    default: 'null'
  - name: useADOAgent
    displayName: Use ADO agent (true) or EC2 self hosted (false). False (EC2) for Maestro.
    type: boolean
    default: false
  - name: brand
    displayName: Brand to use
    type: string
    default: bp
    values:
      - bp
      - aral
      - bpUS

resources:
  repositories:
    # Checkmarx SAST
    - repository: AdsTemplatesSecurity
      type: git
      name: DevOps-SRE/ads-ado-templates-security
      ref: refs/tags/2.x
    # Checkmarx SCA
    - repository: dses-sec-templates
      type: git
      name: DS-DSES/dses-sec-templates
    - repository: bp-via-checkmarx
      type: git
      name: Vulnerability_Identification_and_Awareness/bp-via-checkmarx
    - repository: pipelines
      type: git
      name: bp_pulse/bp-pulse-pipelines

variables:
  - group: 'bp-pulse-mobile-app-dev'
  - ${{ if eq(parameters.brand, 'aral') }}:
      - group: 'aral-pulse-mobile-app-dev'
  - ${{ if eq(parameters.brand, 'bpUS') }}:
      - group: 'us-pulse-mobile-app-dev'

stages:
  - template: stages/validate.yml
    parameters:
      versionNumber: ${{ parameters.versionNumber }}
      buildNumber: $(Build.BuildId)
      environment: pr
      brand: ${{ parameters.brand }}
      useADOAgent: ${{ parameters.useADOAgent }}
