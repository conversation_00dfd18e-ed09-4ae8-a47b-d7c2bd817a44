parameters:
  - name: versionNumber
    type: string
  - name: buildNumber
    type: string
  - name: environment
    type: string
  - name: brand
    type: string
  - name: useADOAgent
    type: boolean
    default: true

stages:
  - stage: unit_tests
    displayName: Unit Tests
    dependsOn: []
    jobs:
      - template: ../jobs/unit_tests.yml

  # Temporary disable test coverage check until we can update it to work with the monorepo structure
  # - stage: test_coverage
  #   displayName: Test Coverage
  #   dependsOn: []
  #   jobs:
  #     - template: ../jobs/test_coverage.yml

  - stage: linting
    displayName: Linting
    dependsOn: []
    jobs:
      - template: ../jobs/linting.yml

  - stage: audit
    displayName: Audit Dependencies
    dependsOn: []
    jobs:
      - template: ../jobs/audit_dependencies.yml

  - stage: check_versions
    displayName: Check versions
    dependsOn: []
    jobs:
      - template: ../jobs/minimum_versions.yml

  - stage: checkmarx
    displayName: Checkmarx Scan
    dependsOn: []
    jobs:
      - template: ../jobs/checkmarx.yml

  - stage: sonarqube
    displayName: Sonarqube Scan
    dependsOn: unit_tests
    jobs:
      - template: shared/jobs/sonarqube.yml@pipelines
        parameters:
          sonarService: 'sonarqube-bpulse-otg-emsp'
          projectKey: 'com.bp.bp-pulse-mobile-app:bp-pulse-mobile-app'

  - stage: build_ios
    displayName: Build ios
    dependsOn: []
    jobs:
      - template: ../jobs/build_ios.yml
        parameters:
          versionNumber: ${{ parameters.versionNumber }}
          buildNumber: ${{ parameters.buildNumber }}
          environment: ${{ parameters.environment }}
          useADOAgent: ${{ parameters.useADOAgent }}
          brand: ${{ parameters.brand }}

  - stage: build_android
    displayName: Build android
    dependsOn: []
    jobs:
      - template: ../jobs/build_android.yml
        parameters:
          versionNumber: ${{ parameters.versionNumber }}
          buildNumber: ${{ parameters.buildNumber }}
          environment: ${{ parameters.environment }}
          useADOAgent: ${{ parameters.useADOAgent }}
          brand: ${{ parameters.brand }}
