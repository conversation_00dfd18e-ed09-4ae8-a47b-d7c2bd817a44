parameters:
  - name: versionNumber
    type: string
  - name: buildNumber
    type: string
  - name: applications
    type: object
  - name: platforms
    type: object
    default:
      - 'ios'
      - 'android'
  - name: iosAppCenterBuild
    type: boolean
    default: false
  - name: environment
    type: string
  - name: runMaestro
    type: boolean
    default: false
  - name: useADOAgent
    type: boolean
    default: true
  - name: brand
    type: string

# excluding unit tests, linting , checkmarx and sonarqube from test
stages:
  - ${{ if eq(parameters.runMaestro, false) }}:
      - ${{ if ne(parameters.environment, 'test') }}:
          - stage: unit_tests
            displayName: unit_tests
            dependsOn: []
            jobs:
              - template: ../jobs/unit_tests.yml

          - stage: linting
            displayName: linting
            dependsOn: []
            jobs:
              - template: ../jobs/linting.yml

          - stage: checkmarx
            displayName: Checkmarx Scan
            dependsOn: []
            jobs:
              - template: ../jobs/checkmarx.yml

          - stage: sonarqube
            displayName: Sonarqube Scan
            dependsOn: unit_tests
            jobs:
              - template: shared/jobs/sonarqube.yml@pipelines
                parameters:
                  sonarService: 'sonarqube-bpulse-otg-emsp'
                  projectKey: 'com.bp.bp-pulse-mobile-app:bp-pulse-mobile-app'

      - stage: build_android
        displayName: Build android
        dependsOn: []
        jobs:
          - template: ../jobs/build_android.yml
            parameters:
              versionNumber: ${{ parameters.versionNumber }}
              buildNumber: ${{ parameters.buildNumber }}
              environment: ${{ parameters.environment }}
              useADOAgent: ${{ parameters.useADOAgent }}
              brand: ${{ parameters.brand }}

      - stage: e2e_ios
        displayName: iOS E2E
        dependsOn: build_ios
        jobs:
          - template: ../jobs/node_e2e_ios.yml
            parameters:
              brand: ${{ parameters.brand }}

      - stage: e2e_Android
        displayName: Android E2E
        dependsOn: build_android
        jobs:
          - template: ../jobs/node_e2e_android.yml
            parameters:
              brand: ${{ parameters.brand }}

  - stage: build_ios
    displayName: Build ios
    dependsOn: []
    jobs:
      - template: ../jobs/build_ios.yml
        parameters:
          versionNumber: ${{ parameters.versionNumber }}
          buildNumber: ${{ parameters.buildNumber }}
          environment: ${{ parameters.environment }}
          runMaestro: ${{ parameters.runMaestro }}
          useADOAgent: ${{ parameters.useADOAgent }}
          brand: ${{ parameters.brand }}
