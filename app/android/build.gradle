// Top-level build file where you can add configuration options common to all sub-projects/modules
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 34
        kotlinVersion = "1.8.10"

        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = "23.1.7779620"

        // react-native-inappbrowser
        androidXAnnotation = "1.2.0"
        androidXBrowser = "1.3.0"

        // For react-native-geolocation-service and react-native-device-info
        googlePlayServicesVersion = "21.0.1"
        googlePlayServicesIidVersion = "17.0.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlinVersion}")

        // firebase
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'

    }
}

// Configure Kotlin compile tasks outside of the buildscript block
tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }
}
