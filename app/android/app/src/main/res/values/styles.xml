<resources xmlns:tools="http://schemas.android.com/tools">

  <!-- Base application theme. -->
  <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
      <!-- Customize your theme here. -->
      <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
  </style>

  <!-- BootTheme should inherit from Theme.BootSplash or Theme.BootSplash.EdgeToEdge -->
  <style name="BootTheme" parent="Theme.BootSplash">
       <!-- Apply color + style to the status bar (true = dark-content, false = light-content) -->
      <item name="android:statusBarColor" tools:targetApi="m">@color/bootsplash_background</item>
      <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
      <item name="bootSplashBackground">@color/bootsplash_background</item>
      <item name="bootSplashLogo">@drawable/bootsplash_logo</item>
      <item name="postBootSplashTheme">@style/AppTheme</item>
  </style>

</resources>