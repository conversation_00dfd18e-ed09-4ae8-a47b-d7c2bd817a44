GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.5)
      rexml
    addressable (2.8.1)
      public_suffix (>= 2.0.2, < 6.0)
    artifactory (3.0.15)
    atomos (0.1.3)
    aws-eventstream (1.2.0)
    aws-partitions (1.644.0)
    aws-sdk-core (3.159.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.525.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.58.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.114.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.5.2)
      aws-eventstream (~> 1, >= 1.0.2)
    babosa (1.0.4)
    badge (0.13.0)
      fastimage (>= 1.6)
      fastlane (>= 2.0)
      mini_magick (>= 4.9.4, < 5.0.0)
    claide (1.1.0)
    colored (1.2)
    colored2 (3.1.2)
    commander (4.6.0)
      highline (~> 2.0.0)
    declarative (0.0.20)
    digest-crc (0.6.4)
      rake (>= 12.0.0, < 14.0.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.8.1)
    emoji_regex (3.2.3)
    excon (0.93.0)
    faraday (1.10.2)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-cookie_jar (0.0.7)
      faraday (>= 0.8.0)
      http-cookie (~> 1.0.0)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.0)
      faraday (~> 1.0)
    fastimage (2.2.6)
    fastlane (2.210.1)
      CFPropertyList (>= 2.3, < 4.0.0)
      addressable (>= 2.8, < 3.0.0)
      artifactory (~> 3.0)
      aws-sdk-s3 (~> 1.0)
      babosa (>= 1.0.3, < 2.0.0)
      bundler (>= 1.12.0, < 3.0.0)
      colored
      commander (~> 4.6)
      dotenv (>= 2.1.1, < 3.0.0)
      emoji_regex (>= 0.1, < 4.0)
      excon (>= 0.71.0, < 1.0.0)
      faraday (~> 1.0)
      faraday-cookie_jar (~> 0.0.6)
      faraday_middleware (~> 1.0)
      fastimage (>= 2.1.0, < 3.0.0)
      gh_inspector (>= 1.1.2, < 2.0.0)
      google-apis-androidpublisher_v3 (~> 0.3)
      google-apis-playcustomapp_v1 (~> 0.1)
      google-cloud-storage (~> 1.31)
      highline (~> 2.0)
      json (< 3.0.0)
      jwt (>= 2.1.0, < 3)
      mini_magick (>= 4.9.4, < 5.0.0)
      multipart-post (~> 2.0.0)
      naturally (~> 2.2)
      optparse (~> 0.1.1)
      plist (>= 3.1.0, < 4.0.0)
      rubyzip (>= 2.0.0, < 3.0.0)
      security (= 0.1.3)
      simctl (~> 1.6.3)
      terminal-notifier (>= 2.0.0, < 3.0.0)
      terminal-table (>= 1.4.5, < 2.0.0)
      tty-screen (>= 0.6.3, < 1.0.0)
      tty-spinner (>= 0.8.0, < 1.0.0)
      word_wrap (~> 1.0.0)
      xcodeproj (>= 1.13.0, < 2.0.0)
      xcpretty (~> 0.3.0)
      xcpretty-travis-formatter (>= 0.0.3)
    fastlane-plugin-badge (1.5.0)
      badge (~> 0.13.0)
    gh_inspector (1.1.3)
    google-apis-androidpublisher_v3 (0.29.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-apis-core (0.9.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
      webrick
    google-apis-iamcredentials_v1 (0.15.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-apis-playcustomapp_v1 (0.11.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-apis-storage_v1 (0.19.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-cloud-core (1.6.0)
      google-cloud-env (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.6.0)
      faraday (>= 0.17.3, < 3.0)
    google-cloud-errors (1.3.0)
    google-cloud-storage (1.43.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-iamcredentials_v1 (~> 0.1)
      google-apis-storage_v1 (~> 0.19.0)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    googleauth (1.2.0)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    highline (2.0.3)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    jmespath (1.6.1)
    json (2.6.2)
    jwt (2.5.0)
    memoist (0.16.2)
    mini_magick (4.11.0)
    mini_mime (1.1.2)
    multi_json (1.15.0)
    multipart-post (2.0.0)
    nanaimo (0.3.0)
    naturally (2.2.1)
    optparse (0.1.1)
    os (1.1.4)
    plist (3.6.0)
    public_suffix (5.0.0)
    rake (13.0.6)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.2.5)
    rouge (2.0.7)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    security (0.1.3)
    signet (0.17.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simctl (1.6.8)
      CFPropertyList
      naturally
    terminal-notifier (2.0.0)
    terminal-table (1.8.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    trailblazer-option (0.1.2)
    tty-cursor (0.7.1)
    tty-screen (0.8.1)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (1.8.0)
    webrick (1.7.0)
    word_wrap (1.0.0)
    xcodeproj (1.22.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.3.0)
      rexml (~> 3.2.4)
    xcpretty (0.3.0)
      rouge (~> 2.0.7)
    xcpretty-travis-formatter (1.0.1)
      xcpretty (~> 0.2, >= 0.0.7)

PLATFORMS
  ruby

DEPENDENCIES
  fastlane
  fastlane-plugin-badge

BUNDLED WITH
   1.17.2
