const fs = require('fs');
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const client = require('twilio')(accountSid, authToken);

async function getSortedMessageData() {
  try {
    const phoneNumbers = await client.incomingPhoneNumbers.list({
      actorSid: accountSid,
    });
    const messageData = [];

    for (const phoneNumber of phoneNumbers) {
      const messages = await client.messages.list({
        to: phoneNumber.phoneNumber,
      });
      if (messages.length > 0) {
        messageData.push({
          accountSid: phoneNumber.accountSid,
          phoneNumber: phoneNumber.phoneNumber,
          lastMessageDate: messages[0].dateCreated,
          phoneNumberID: phoneNumber.sid,
        });
      }
    }

    messageData.sort(
      (a, b) => new Date(a.lastMessageDate) - new Date(b.lastMessageDate),
    );
    return messageData;
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

function exportTableToTXT(messageData, filePath = 'message_data.txt') {
  let textData = 'accountSid\tphoneNumber\tlastMessageDate\tphoneNumberID\n';

  messageData.forEach(message => {
    textData += `${message.accountSid}\t${message.phoneNumber}\t${message.lastMessageDate}\t${message.phoneNumberID}\n`;
  });

  fs.writeFile(filePath, textData, err => {
    if (err) {
      console.error('Error writing file:', err);
    } else {
      console.log('File saved successfully to:', filePath);
    }
  });
}

getSortedMessageData()
  .then(messageData => exportTableToTXT(messageData))
  .catch(error => console.error('Error:', error));

// eslint-disable-next-line no-unused-vars
async function deleteInactivePhoneNumbers() {
  try {
    const phoneNumbers = await client.incomingPhoneNumbers.list({
      actorSid: accountSid,
    });

    const cutoffDate = new Date('2024-01-01');

    for (const phoneNumber of phoneNumbers) {
      const messages = await client.messages.list({
        to: phoneNumber.phoneNumber,
        limit: 1, // Fetch only the most recent message
      });

      if (
        messages.length === 0 ||
        new Date(messages[0].dateCreated) < cutoffDate
      ) {
        // Delete the phone number
        try {
          await client.incomingPhoneNumbers(phoneNumber.sid).remove();
          console.log(`Phone number ${phoneNumber.phoneNumber} deleted.`);
        } catch (error) {
          console.error(
            `Error deleting phone number ${phoneNumber.phoneNumber}:`,
            error,
          );
        }
      }
    }
  } catch (error) {
    console.error('Error fetching or deleting phone numbers:', error);
  }
}

// Call the function to delete inactive phone numbers
//deleteInactivePhoneNumbers();
