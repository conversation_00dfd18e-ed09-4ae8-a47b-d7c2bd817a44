# Using Mae<PERSON> with the bp Pulse app

## Table of Contents

1. [Prerequisites & Setup](#prereqs)
   - Homebew
   - Maestro CLI
   - Facebook IDB
   - Maestro Server
2. [Install compatible build](#install)
3. [Environment variables](#environment)
4. [Run the tests](#run)
5. [Development](#dev)
6. [Continuous Integration](#ci)

## <a id="prereqs" name="prereqs"></a>Prerequisites & Setup

You need to have [Homebrew](https://brew.sh/) installed to install the Facebook IDB tool

Instructions below are for Mac only, refer to the [Maestro install guide](https://maestro.mobile.dev/getting-started/installing-maestro) for additional guidance

Install the Maestro CLI

```
yarn maestro:install
```

Install the Facebook IDB tool for iOS

```
brew tap facebook/fb
brew install facebook/fb/idb-companion
```

If you want to use flows that require the Maestro server (phone number & email verifictation) you will need to setup and run the [Maestro server](https://dev.azure.com/bp-digital/bp_pulse/_git/maestro-server)

## <a id="install" name="install"></a>Install compatible build

While you can run maestro using the normal simulator build used for development, this build has error messages & logs that can break the maestro flow.

To avoid this you can either disable logs by adding the following to App.tsx

```
LogBox.ignoreAllLogs();
```

You will also need to change the APP_ID environment variable in maestro/.env to `com.aml.ev-app.debug`

To avoid doing the above you can create and install a dedicated iOS simulator release build for your simulator of choice

```
xcrun xcodebuild -scheme 'bppulse' -workspace 'ios/bppulse.xcworkspace' -configuration release -sdk 'iphonesimulator' -destination 'platform=iOS Simulator,name=iPhone 14' -derivedDataPath build
```

```
xcrun simctl install "iPhone 14" "build/Build/Products/Release-iphonesimulator/bppulse.app"
```

## <a id="environment" name="environment"></a>Environment variables

The environment variables used for Maestro are stored in `meastro/.env`

- Run `git secret reveal` to decrypt the secret file `.env.dev`
- On first installation, run `cp maestro/.env.dev maestro/.env` to create an `.env` file for local development

To pass the environment variables to maestro you need to add `@.env` as the second argument to `maestro test`

## <a id="run" name="run"></a>Run the tests

Run all test flows

```
yarn maestro:test
```

You can also run tests individually, eg:

```
maestro test maestro/map/guest/map.yaml @maestro/.env
```

Though this depends on the currect app state to be correct when starting your test

## <a id="dev" name="dev"></a>Development

To develop new Maestro tests you can manually get the app into the appropriate state required and then write the test manually with maestro running in continuous mode, retrying the flow everytime you update it

```
maestro test -c maestro/myFlow.yaml
```

You can also use Maestro studio which is very helpful for viewing the app hierarchy and selecting the correct IDs

```
yarn maestro:studio
```

View the [Maestro docs](https://maestro.mobile.dev/getting-started/writing-your-first-flow) for more guidance

## <a id="ci" name="ci"></a>Continuous integration

This is a work in progress

You can try running the pipeline by checking the "Run Maestro" option when running the `bp-pulse-mobile-app-dev` pipeline

The pipeline will create a dedicated simulator build for iOS and attempt to run the maestro index file, running all flows that have been added

Until Maestro is mature enough we will run it separately from our current build pipeline

You can view test artifacts in the pipeline artifacts, this will give you a log of the run and a screenshot of failed flows.
Get more info in the [Maestro docs](https://maestro.mobile.dev/troubleshooting/debug-output)
