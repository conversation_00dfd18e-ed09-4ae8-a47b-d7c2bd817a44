appId: ${APP_ID}
name: Launch bp pulse
---
- runFlow: ../pom/elements/loadElements.yaml
- runFlow: ../pom/objects/loadObjects.yaml

- clearKeychain

- launchApp:
    clearState: true
    permissions: { location: always, notifications: allow, usertracking: allow }

# If Android location permissions required
- runFlow:
    when:
      visible:
        id: 'com.android.permissioncontroller:id/permission_message'
      platform: Android
    commands:
      - tapOn: 'While using the app'

# Notice for guest users on Android
- runFlow:
    when:
      visible: 'Guest user data'
      platform: Android
    commands:
      - tapOn: 'Continue'

# Make sure bootsplash is hidden before starting
- extendedWaitUntil:
    notVisible:
      id: 'BootSplashLogo'
    timeout: 180000

# If ATT permissions required
- runFlow:
    when:
      visible: 'Allow “bp pulse” to track your activity across other companies’ apps and websites?'
      platform: iOS
    commands:
      - tapOn: 'Allow'

#  Make sure the app has loaded the map screen first
- assertVisible:
    id: 'MapScreen'
