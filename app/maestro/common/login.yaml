appId: ${APP_ID}
name: Common Login Flow
---
# When number is not null, run mobile login flow
- runFlow:
    when:
      true: ${NUMBER != null}
    file: ../auth/loginMobile.yaml
    env:
      LOGIN_NUMBER: ${NUMBER}
      USER_NAME: ${FIRST_NAME}

# When number is null, run email login flow
- runFlow:
    when:
      true: ${NUMBER == null}
    file: ../auth/loginEmail.yaml
    env:
      LOGIN_EMAIL: ${EMAIL}
      USER_NAME: ${FIRST_NAME}

# Hide notifications modal if visisble
- runFlow:
    when:
      visible: Turn on notifications
    commands:
      - tapOn: "I'M IN"
      - runFlow:
          when:
            visible: Allow
          commands:
            - tapOn: Allow

#  Verify login was successful
- tapOn: Profile
- assertVisible: Hi ${USER_NAME}
