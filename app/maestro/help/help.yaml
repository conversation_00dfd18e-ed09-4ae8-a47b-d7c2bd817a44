appId: ${APP_ID}
name: Help (Guest)
onFlowStart:
  - runFlow: ../common/launchApp.yaml
---
- tapOn: ${output.t.bottomTabs.help}
- assertVisible: ${output.t.helpScreen.selectLocation.SubHeading}
# If multiple countries available, select UK
- runFlow:
    when:
      visible: ${output.t.helpScreen.selectLocation.SubHeading}
    commands:
      # BUG: Countries do not use translations here
      # - tapOn: '${output.t.country.GB}'
      - tapOn: 'United Kingdom'
      - tapOn: ${output.t.helpScreen.selectLocation.ButtonText}
- assertVisible: ${output.t.helpScreen.callAnytime}

- runFlow: viewFAQs.yaml
