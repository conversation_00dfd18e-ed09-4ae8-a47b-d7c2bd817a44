appId: ${APP_ID}
name: Add Payment Card
onFlowStart:
  - runFlow: ../common/launchApp.yaml
  - runScript: ../pom/objects/users.js
  - runFlow:
      file: ../common/login.yaml
      env:
        EMAIL: ${output.users.paygwallet.email}
        # NUMBER: ${output.users.paygwallet.number}
        FIRST_NAME: ${output.users.paygwallet.name}
onFlowComplete:
  - runFlow: subflows/removeCard.yaml
  - runFlow: ../common/logout.yaml
---
- tapOn: 'Profile'
- tapOn:
    id: 'PaymentDetails'

- tapOn:
    id: 'PaymentWebView button'

- tapOn: 'Enter your card number'
- inputText: '5123 4567 8901 2346'

- tapOn: 'MM'
- tapOn: '06'
- tapOn: 'YY'
- tapOn: '30'
- runFlow:
    when:
      platform: Android
    commands:
      - tapOn:
          id: 'cvv'
- runFlow:
    when:
      platform: iOS
    commands:
      - tapOn: '000'
- inputText: 123
- pressKey: tab

- scrollUntilVisible:
    element:
      text: 'Cardholders name'
    centerElement: true

# When using text Android is very flaky, using ID instead
- runFlow:
    when:
      platform: iOS
    commands:
      - tapOn: 'Enter your card holders name'
- runFlow:
    when:
      platform: Android
    commands:
      - tapOn:
          id: billingNameInput

- inputText: 'Test'
- tapOn:
    point: '1%,50%'

- tapOn: 'Enter your street name and number'
- inputText: '10 Test Street'
- tapOn:
    point: '1%,50%'

- scrollUntilVisible:
    element:
      text: 'Enter your town or city name'
    centerElement: true

- tapOn: 'Enter your town or city name'
- inputText: 'Test'
- tapOn:
    point: '1%,50%'

- tapOn: 'Enter your postal or zip code'
- inputText: 'Test'
- tapOn:
    point: '1%,50%'

- scrollUntilVisible:
    element:
      text: 'Enter your mobile number'
    centerElement: true

- tapOn: 'Enter your mobile number'
- inputText: '07123456789'
- tapOn:
    point: '1%,50%'

- tapOn: 'Enter your email address'
- inputText: '<EMAIL>'
- tapOn:
    point: '1%,50%'

# Workaround for Android
- runFlow:
    when:
      platform: iOS
    commands:
      - tapOn: 'Continue to card authorisation'
- runFlow:
    when:
      platform: Android
    commands:
      - tapOn:
          point: '50%, 92%'

- assertVisible: '3DS'

- waitForAnimationToEnd

- assertVisible:
    id: 'linkedCardItem'
- assertVisible: 'Mastercard...2346'

- tapOn:
    id: 'BackPress'
