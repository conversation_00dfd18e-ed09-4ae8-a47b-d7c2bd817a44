appId: com.aml.ev-app.debug
name: Login Number (PAYG)
onFlowStart:
  - runFlow: ../common/launchApp.yaml
  - runFlow: ../pom/elements/loadElements.yaml
---
- tapOn: Profile
- tapOn: ${output.profile.createAccountButton.text}
# If iOS pop up
- runFlow:
    when:
      visible: This allows the app and website to share information about you.
    commands:
      - tapOn: Continue
- assertVisible: 'Enter your number'
# When CIP hasn't been localised and used US as default country, select UK
- runFlow:
    when:
      visible: 'United States (+1)'
    commands:
      - tapOn: 'United States (+1)'
      - swipe:
          direction: UP
      - tapOn: 'United Kingdom (+44)'
- tapOn: '************'
# Purchase number from Twilio
- runScript:
    file: scripts/purchaseTwilioNumber.js
    env:
      NAME: ${ONBOARDING_EMAIL}
- inputText: ${output.number}
- pressKey: enter
- runFlow:
    file: subflows/verifyYourNumber.yaml
    env:
      NUMBER: ${output.fullNumber}
# Retry if code fails
- runFlow:
    when:
      visible: ${output.cip.verifyPhone.incorrectCode}
    file: subflows/verifyYourNumber.yaml
    env:
      NUMBER: ${output.fullNumber}
- assertVisible: Add email address
- tapOn: email
- eraseText
- runScript:
    file: scripts/createRegistrationEmail.js
    env:
      ONBOARDING_EMAIL: ${ONBOARDING_EMAIL}
- inputText: ${output.registrationEmail}
- pressKey: enter
- assertVisible: Verify your email
- runFlow: subflows/proton.yaml
- runFlow:
    file: subflows/openMagicLink.yaml
    env:
      TITLE: 'Verify your email address'
      BUTTON_TEXT: Verify
- assertVisible: Customise the bp pulse app
- tapOn: 'first name'
- inputText: 'First'
- tapOn: return
- tapOn: 'last name'
- inputText: 'Last'
- tapOn: return
# Accept terms and conditions
- tapOn:
    text: 'checkbox'
    index: 1
# Jump right in!
- tapOn:
    id: 'continueButton'
- assertVisible: Setting up your account
# - waitForAnimationToEnd:
#     timeout: 60000
