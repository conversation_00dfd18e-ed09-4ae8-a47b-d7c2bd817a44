const input = {
  friendlyName: `Maestro ${ENVIRONMENT} ${BUILD_NUMBER}`,
};

if (NAME !== 'null') {
  input.friendlyName = NAME;
}

const response = http.post(`${MAESTRO_SERVER_URL}/twilio/create`, {
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ name: input.friendlyName }),
});

const number = response.body;
output.countryCode = '+44';
output.number = number.split('+44')[1];
output.fullNumber = number;
