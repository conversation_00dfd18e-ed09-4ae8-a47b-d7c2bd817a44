appId: ${APP_ID}
name: <PERSON><PERSON> (Email)
onFlowStart:
  - runFlow: ../pom/elements/loadElements.yaml
---
- tapOn: Profile
- tapOn:
    id: ${output.profile.loginButton.id}
# If iOS pop up
- runFlow:
    when:
      visible: This allows the app and website to share information about you.
    commands:
      - tapOn: Continue
- extendedWaitUntil:
    visible: 'Enter your details'
    timeout: 20000
- tapOn: ${output.cip.login.input}
- inputText: ${LOGIN_EMAIL}
- pressKey: enter
- assertVisible: Check your email
- runFlow: subflows/proton.yaml
- runFlow:
    file: subflows/openMagicLink.yaml
    env:
      TITLE: 'One-time login link'
      BUTTON_TEXT: 'Tap to log in'
      EMAIL: ${LOGIN_EMAIL}

# WIP Use the email verification code
# - runFlow: subflows/verifyEmailWithCode.yaml

- tapOn:
    text: Profile
    repeat: 2
    delay: 500
- assertVisible: Hi ${USER_NAME}
