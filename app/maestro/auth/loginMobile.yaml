appId: ${APP_ID}
name: <PERSON>gin (Number)
onFlowStart:
  - runFlow: ../pom/elements/loadElements.yaml
---
- tapOn: ${output.profile.title}
- tapOn:
    id: ${output.profile.loginButton.id}
# If iOS pop up
- runFlow:
    when:
      visible: This allows the app and website to share information about you.
      platform: iOS
    commands:
      - tapOn: Continue
- extendedWaitUntil:
    visible: 'Enter your details'
    timeout: 20000

# This is a workaround for Android which cannot find the input text
# There may be a fix coming in the next version of Maestro (1.41)
# See androidWebViewHierarchy: devtools
- runFlow:
    when:
      platform: iOS
    commands:
      - tapOn: ${output.cip.login.input}
- runFlow:
    when:
      platform: Android
    commands:
      - tapOn:
          point: '50%,35%'

- inputText: ${LOGIN_NUMBER}
- pressKey: enter

- runFlow:
    file: subflows/verifyYourNumber.yaml
    env:
      NUMBER: ${LOGIN_NUMBER}
# Retry if code fails
- runFlow:
    when:
      visible: ${output.cip.verifyPhone.incorrectCode}
    file: subflows/verifyYourNumber.yaml
    env:
      NUMBER: ${LOGIN_NUMBER}

# If redirect page gets stuck, press continue.
- runFlow:
    when:
      visible: 'Click below to complete your log in.'
    commands:
      - tapOn: 'Continue'

# If redirect page is still stuck, reload page.
- runFlow:
    when:
      visible: 'Click below to complete your log in.'
    commands:
      - tapOn:
          id: 'ReloadButton'
