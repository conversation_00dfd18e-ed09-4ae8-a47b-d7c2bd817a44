appId: com.aml.ev-app
---
- launchApp:
    appId: com.apple.mobilesafari
    clearState: true

# Wait for page to load
- waitForAnimationToEnd:
    timeout: 5000

# If gmail is already open
- runFlow:
    when:
      notVisible: Email or phone
    commands:
      - tapOn: Address
      - inputText: gmail.com
      - tapOn: go

- tapOn: Email or phone
- inputText: ${GMAIL_EMAIL}
- tapOn: Done
- tapOn: Next
- inputText: ${GMAIL_PASSWORD}
- tapOn: Done
- tapOn: Next

# If safari offers to save password, select not now
- runFlow:
    when:
      visible: Would you like to save this password to use with apps and websites?
    commands:
      - tapOn:
          point: '50%,93%'

# If gmail app suggested
- runFlow:
    when:
      visible: Upgrade to a smarter gmail
    commands:
      - tapOn: I am not interested

- assertVisible: Primary
