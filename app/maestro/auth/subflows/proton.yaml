appId: ${APP_ID}
---
- runFlow:
    when:
      platform: iOS
    commands:
      - launchApp:
          appId: com.apple.mobilesafari
- runFlow:
    when:
      platform: Android
    commands:
      - launchApp:
          appId: com.android.chrome

# Wait for page to load
- waitForAnimationToEnd:
    timeout: 5000

# If proton mail is already open
- runFlow:
    when:
      notVisible: Email or phone
    commands:
      - tapOn: Address
      - inputText: mail.proton.me
      - pressKey: Enter

# If not logged in already
- runFlow:
    when:
      visible: 'Sign in'
    commands:
      - tapOn: Email or username
      - inputText: ${PROTON_EMAIL}
      - tapOn: Done
      - tapOn: Password
      - inputText: ${PROTON_PASSWORD}
      - tapOn: Done
      - tapOn:
          text: 'Sign in'
          index: 1

# If safari offers to save password, select not now
- runFlow:
    when:
      visible: Would you like to save this password to use with apps and websites?
    commands:
      - tapOn:
          point: '50%,93%'

# If proton welcome screen shows
- runFlow:
    when:
      visible: 'Select a service to continue'
    commands:
      - tapOn: 'Proton Mail'

- assertVisible: Inbox
