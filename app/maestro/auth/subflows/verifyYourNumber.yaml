appId: com.aml.ev-app.debug
name: Verify your number
---
- assertVisible: 'Enter your code'
- runScript:
    file: ../scripts/getVerificationCode.js
    env:
      NUMBER: ${NUMBER}
- inputText: ${output.code[0]}
- inputText: ${output.code[1]}
- inputText: ${output.code[2]}
- inputText: ${output.code[3]}
- inputText: ${output.code[4]}
- inputText: ${output.code[5]}

# Workaround for Android where pressing enter does not work
- runFlow:
    when:
      platform: iOS
    commands:
      - pressKey: enter
- runFlow:
    when:
      platform: Android
    commands:
      - tapOn: 'chevron-right'
