appId: ${APP_ID}
---
# Tap on top email
- tapOn:
    text: ${TITLE}
    index: 0

# Open quoted text if shown
- runFlow:
    when:
      visible: Show quoted text
    commands:
      - scroll
      - tapOn: Show quoted text

# If email is not visible, scroll and open the next email
# This is necessary when running parallel tests with email login
- repeat:
    while:
      notVisible: More details about ${EMAIL} <${EMAIL}>
    times: 2
    commands:
      - scroll
      - runFlow:
          when:
            visible: More details about bp Accounts Team <<EMAIL>>
          commands:
            - tapOn: More details about bp Accounts Team <<EMAIL>>
- assertVisible: More details about ${EMAIL} <${EMAIL}>

- scrollUntilVisible:
    centerElement: true
    element:
      text: ${BUTTON_TEXT}

- tapOn: ${BUTTON_TEXT}
- assertNotVisible: Link expired

# Confirm open link using protonmail
- runFlow:
    when:
      visible: Confirm.*
    commands:
      - tapOn: Confirm.*

# Wait for page to load
- waitForAnimationToEnd:
    timeout: 5000

# # Open bp pulse app on iOS
- runFlow:
    when:
      visible: Open
    commands:
      - tapOn: Open
      # Wait for page to load
      - waitForAnimationToEnd:
          timeout: 5000
