appId: ${APP_ID}
name: Charge Wallet UK
onFlowStart:
  - runFlow: ../common/launchApp.yaml
  - runFlow:
      file: ../common/login.yaml
      env:
        EMAIL: ${output.users.paygWalletWithCard.email}
        # NUMBER: ${output.users.paygWalletWithCard.number}
        FIRST_NAME: ${output.users.paygWalletWithCard.name}
onFlowComplete:
  - runFlow: ../common/logout.yaml
---
- runFlow:
    file: subflows/charge.yaml
    env:
      CHARGEPOINT_ID: ${output.chargepoints.successUK.id}
      LATITUDE: ${output.chargepoints.successUK.location.latitude}
      LONGITUDE: ${output.chargepoints.successUK.location.longitude}
