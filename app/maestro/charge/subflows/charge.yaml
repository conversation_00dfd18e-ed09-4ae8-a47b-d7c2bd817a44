appId: ${APP_ID}
name: Charge
---
- runFlow:
    file: chargepointSearch.yaml
    env:
      CHARGEPOINT_ID: ${CHARGEPOINT_ID}
- tapOn:
    id: 'ConnectorCard'
    index: 0
- tapOn: Charge using (socket|connector) 1
- setLocation:
    latitude: ${LATITUDE}
    longitude: ${LONGITUDE}
- tapOn: 'Confirm your vehicle is connected'
- repeat:
    times: 4
    commands:
      - waitForAnimationToEnd
- assertVisible: 'Stop charging'
- tapOn: 'Stop charging'
- tapOn: 'Stop charging'
- repeat:
    times: 4
    commands:
      - waitForAnimationToEnd
- assertVisible: 'Charge summary'
- tapOn: 'Done'
