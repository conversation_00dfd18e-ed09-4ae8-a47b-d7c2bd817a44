appId: ${APP_ID}
name: Charge History
onFlowStart:
  - runFlow: ../common/launchApp.yaml
  - runScript: ../pom/objects/users.js
  - runFlow:
      file: ../common/login.yaml
      env:
        EMAIL: ${output.users.paygWalletWithCard.email}
        # NUMBER: ${output.users.paygWalletWithCard.number}
        FIRST_NAME: ${output.users.paygWalletWithCard.name}
---
- tapOn: Profile
- tapOn: Recent transactions
- assertVisible: Charge activity
- tapOn:
    id: 'list-item'
    index: 0
- assertVisible: Charge point operator
- scrollUntilVisible:
    element:
      text: Download VAT receipt
- tapOn: Download VAT receipt
- assertVisible: Chargemaster_Ltd_VAT_receipt.*
- tapOn:
    id: 'UICloseButtonBackground'
- tapOn:
    id: 'leftArrow'
- assertVisible: Recent transactions
- waitForAnimationToEnd:
    timeout: 1000
- tapOn:
    id: 'leftArrow'
- assertVisible: Profile
