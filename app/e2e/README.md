# BP Pulse Java Appium E2E tests

This repo contains the source code for Java Appium E2E tests

## Table of Contents

1. [Install & run application](#install)

- set path to bp pulse android apk in /AppiumJava/src/test/resources/testng.xml
- set environment variables:
  TWILIO_ACCOUNT_SID = your sid
  TWILIO_AUTH_TOKEN = your token
  PHONE_NUMBER = +44-********
- start appium server
- in terminal go to AppiumJava folder and run 'mvn test'
