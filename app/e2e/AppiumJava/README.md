# Requirments

https://dev.azure.com/bp-digital/bp_pulse/_wiki/wikis/bp_pulse/172566/Appium-Automation-Framework-Setup

# Run tests from the local machine

- open the /bp-pulse-mobile/app/e2e/AppiumJava/pom.xml as a project in IntelliJ
- make sure the following plugins are installed in Intellij [EnvFile](https://plugins.jetbrains.com/plugin/7861-envfile) and [Cucumber for Java](https://plugins.jetbrains.com/plugin/7212-cucumber-for-java)
- open the terminal and run in bp-pulse-mobile folder 'git secret reveal' to encrypt the '/bp-pulse-mobile/app/e2e/AppiumJava/.env.secret' You should get the '.env' encrypted
- set the var LOCAL_MODE=true to run it locally as well as setting the APP_BRAND=bp (for global tests) or bpUS (for US based tests)
- to run automation in SauceLabs set LOCAL_MODE=false and set the SauceLabs SAUCE_USERNAME and SAUCE_ACCESS_KEY
- open the context menu over the src/test/resources/testng.xml->More Run/Debug->Modify Run Conf...->select tab EnvFile->select checkbox Enable EnvFile->click + .env file
- start Android or iOS device/emulator/simulator
- open the terminal window and start the appium server for Android: "appium --allow-insecure chromedriver_autodownload" or iOS "appium --allow-insecure safaridriver_autodownload"
