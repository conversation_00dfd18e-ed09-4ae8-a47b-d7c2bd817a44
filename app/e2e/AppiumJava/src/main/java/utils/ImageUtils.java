package utils;

import org.apache.commons.codec.binary.Base64;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.ScreenshotException;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.awt.image.Raster;
import java.awt.image.WritableRaster;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ImageUtils {

  protected static final Logger logger = Logger.getLogger(ImageUtils.class.getName());

  public static BufferedImage cropBuffImage(BufferedImage img, int xPercent, int yPercent, int widthPercent, int heightPercent) {
    int x = img.getWidth() * xPercent / 100;
    int y = img.getHeight() * yPercent / 100;
    int width = img.getWidth() * widthPercent / 100;
    int height = img.getHeight() * heightPercent / 100;

    img = img.getSubimage(x, y, width, height);
    ColorModel colorModel = img.getColorModel();
    Raster raster = img.getRaster();
    WritableRaster writableRaster = raster.createCompatibleWritableRaster();
    writableRaster.setDataElements(0, 0, raster);
    img = new BufferedImage(colorModel, writableRaster, colorModel.isAlphaPremultiplied(), null);
    return img;
  }

  public static void saveElement(WebElement we) {
    long milis = System.currentTimeMillis();
    saveElement(we, String.valueOf(milis));
  }

  public static void saveElement(WebElement we, String name) {
    File folder = new File("src/test/resources/images/temp/");
    if (!folder.exists()) {
      folder.mkdirs();
    }
    File out = new File(folder.getPath() + "/img_" + name + ".png");
    saveBufferedImage(elementToBufferedImage(we), out);
  }

  public static void saveBufferedImage(BufferedImage img, File outputFile) {
    try {
      ImageIO.write(img, "png", outputFile);
    } catch (IOException e) {
      logger.log(Level.INFO, "error", e);
    }
  }

  public static int compareImages(BufferedImage image1, BufferedImage image2) {
    int width = image1.getWidth();
    int height = image1.getHeight();

    long diff = 0;
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        int rgb1 = image1.getRGB(x, y);
        int rgb2 = 0;
        if (image2 != null) {
          rgb2 = image2.getRGB(x, y);
        }

        int red1 = (rgb1 >> 16) & 0xFF;
        int green1 = (rgb1 >> 8) & 0xFF;
        int blue1 = rgb1 & 0xFF;

        int red2 = (rgb2 >> 16) & 0xFF;
        int green2 = (rgb2 >> 8) & 0xFF;
        int blue2 = rgb2 & 0xFF;

        diff += Math.abs(red1 - red2);
        diff += Math.abs(green1 - green2);
        diff += Math.abs(blue1 - blue2);
      }
    }
    double totalPixels = width * height * (double) 3; // 3 for RGB channels
    return 100 - (int) ((diff / totalPixels) / 255 * 100);
  }

  public static int cropAndCompareImages(BufferedImage image1, BufferedImage image2, int xPercent, int yPercent, int widthPercent, int heightPercent) {
    BufferedImage croppedImage1 = cropBuffImage(image1, xPercent, yPercent, widthPercent, heightPercent);
    BufferedImage croppedImage2 = cropBuffImage(image2, xPercent, yPercent, widthPercent, heightPercent);
    return compareImages(croppedImage1, croppedImage2);
  }

  public static BufferedImage elementToBufferedImage(WebElement element) {
    try {
      String elementScreenshotAs = element.getScreenshotAs(OutputType.BASE64);
      byte[] dataBytes = Base64.decodeBase64(elementScreenshotAs);
      InputStream is = new ByteArrayInputStream(dataBytes);
      return ImageIO.read(is);
    } catch (IOException | ScreenshotException e) {
      logger.log(Level.INFO, "error", e);
    }
    return null;
  }

  public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
    BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
    Graphics2D g = resizedImage.createGraphics();
    g.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
    g.dispose();
    return resizedImage;
  }

  public static Dimension getDimension(File image) {
    BufferedImage bufferedRefImage = null;
    try {
      bufferedRefImage = ImageIO.read(image);
    } catch (IOException e) {
      logger.log(Level.INFO, "error", e);
    }
    return new Dimension(bufferedRefImage.getWidth(), bufferedRefImage.getHeight());
  }

  public static BufferedImage fileToBufferedImage(File image) {
    BufferedImage bufferedRefImage = null;
    try {
      bufferedRefImage = ImageIO.read(image);
    } catch (IOException e) {
      logger.log(Level.INFO, "error", e);
    }
    return bufferedRefImage;
  }

  public static List<WebElement> findMapItems(List<WebElement> likelyItems, File referenceImage, int xPercent, int yPercent, int widthPercent, int heightPercent) {
    List<WebElement> mapItems = new ArrayList<>();
    BufferedImage refImage = fileToBufferedImage(referenceImage);
    for (WebElement likelyItem : likelyItems) {
      BufferedImage elementImage = elementToBufferedImage(likelyItem);
      if (elementImage != null) { //elementToBufferedImage may throw ScreenshotException
        if (elementImage.getWidth() != refImage.getWidth() || elementImage.getHeight() != refImage.getHeight()) {
          elementImage = resizeImage(elementImage, refImage.getWidth(), refImage.getHeight());
        }
        int matchPercent = cropAndCompareImages(elementImage, refImage, xPercent, yPercent, widthPercent, heightPercent);
        if (matchPercent > 95) {
          mapItems.add(likelyItem);
        }
      }
    }
    return mapItems;
  }
}
