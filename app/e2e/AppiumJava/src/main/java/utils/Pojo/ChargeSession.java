package utils.Pojo;

import java.util.Date;

public class ChargeSession {
  private String id;
  private String address;
  private CreditCard creditCard;
  private boolean isFavourite;
  private String connectorName;
  private String connectorType;
  private float energyDelivered;
  private Date chargingDateTime;
  private int status;
  private float totalCost;

  public int getStatus() {
    return status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public CreditCard getCreditCard() {
    return creditCard;
  }

  public void setCreditCard(CreditCard creditCard) {
    this.creditCard = creditCard;
  }

  public boolean isFavourite() {
    return isFavourite;
  }

  public void setFavourite(boolean favourite) {
    isFavourite = favourite;
  }

  public String getConnectorName() {
    return connectorName;
  }

  public void setConnectorName(String connectorName) {
    this.connectorName = connectorName;
  }

  public String getConnectorType() {
    return connectorType;
  }

  public void setConnectorType(String connectorType) {
    this.connectorType = connectorType;
  }

  public float getEnergyDelivered() {
    return energyDelivered;
  }

  public void setEnergyDelivered(float energyDelivered) {
    this.energyDelivered = energyDelivered;
  }

  public Date getChargingDateTime() {
    return chargingDateTime;
  }

  public void setChargingDateTime(Date chargingDateTime) {
    this.chargingDateTime = chargingDateTime;
  }

  public float getTotalCost() {
    return totalCost;
  }

  public void setTotalCost(float totalCost) {
    this.totalCost = totalCost;
  }

}
