package utils.Pojo;

import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;

public class User {

  private String id;
  private String firstName;
  private String lastName;
  private String phoneNumber;
  private String email;
  private String language;
  private String country;
  private String countryCode;
  private String street;
  private String houseNumber;
  private String postalCode;
  private String town;
  private String timeZone;
  private String phonePrefix;
  private List<String> cards;
  private String defaultCard;
  private CreditCard newCard;
  private double latitude;
  private double longitude;
  private ChargeSession chargeSession;
  private String chargerId;
  private List<Pair<String, Boolean>> marketingPreferences = new ArrayList<>();

  public ChargeSession getChargeSession() {
    return chargeSession;
  }

  public void setChargeSession(ChargeSession chargeSession) {
    this.chargeSession = chargeSession;
  }

  public String getId() {
    return id;
  }

  public String getFirstName() {
    return firstName;
  }

  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public String getLastName() {
    return lastName;
  }

  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public String getPhoneNumber() {
    return phoneNumber;
  }

  public void setPhoneNumber(String phoneNumber) {
    this.phoneNumber = phoneNumber;
  }

  public String getEmail() {
    return email;
  }

  public String getTimeZone() {
    return timeZone;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getLanguage() {
    return language;
  }

  public void setLanguage(String language) {
    this.language = language;
  }

  public String getCountry() {
    return country;
  }

  public String getCountryCode() {
    return countryCode;
  }

  public void setCountryCode(String countryCode) {
    this.countryCode = countryCode;
  }

  public String getStreet() {
    return street;
  }

  public String getHouseNumber() {
    return houseNumber;
  }

  public String getPostalCode() {
    return postalCode;
  }

  public String getTown() {
    return town;
  }

  public String getPhonePrefix() {
    return phonePrefix;
  }

  public CreditCard getNewCard() {
    return newCard;
  }

  public void setNewCard(CreditCard newCard) {
    this.newCard = newCard;
  }

  public List<String> getCards() {
    return cards;
  }

  public void setCards(List<String> cards) {
    this.cards = cards;
  }

  public String getDefaultCard() {
    return defaultCard;
  }

  public void setDefaultCard(String defaultCard) {
    this.defaultCard = defaultCard;
  }

  public double getLatitude() {
    return latitude;
  }

  public double getLongitude() {
    return longitude;
  }

  public String getChargerId() {
    return chargerId;
  }

  public List<Pair<String, Boolean>> getMarketingPreferences() {
    return marketingPreferences;
  }

  public void setMarketingPreferences(List<Pair<String, Boolean>> marketingPreferences) {
    this.marketingPreferences = marketingPreferences;
  }

}
