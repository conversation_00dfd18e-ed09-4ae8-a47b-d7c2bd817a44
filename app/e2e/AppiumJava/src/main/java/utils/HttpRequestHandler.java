package utils;


import java.io.IOException;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class HttpRequestHandler {

  public static final Logger logger = Logger.getLogger(HttpRequestHandler.class.getName());

  public HttpResponse<String> send_HTTP_Request(HttpRequest request) {
    HttpClient client = HttpClient.newHttpClient();
    HttpResponse<String> response;
    try {
      response = client.send(request, BodyHandlers.ofString());
    } catch (IOException | InterruptedException e) {
      throw new RuntimeException(e);
    }
    return response;
  }

  public String buildFormData(Map<String, String> params) {
    String encodedFormData = params.entrySet().stream()
      .map(entry -> URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8) + "=" + URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8))
      .collect(Collectors.joining("&"));
    return encodedFormData;
  }


  public void pringLog(HttpResponse<String> response) {
    logger.log(Level.WARNING, "Error: Received HTTP status code " + response.statusCode());
    logger.log(Level.WARNING, "Response body: \n" + response.body() + "\n");
  }
}
