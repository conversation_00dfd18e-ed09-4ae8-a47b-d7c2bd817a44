package utils;

import utils.Pojo.User;

import java.util.Locale;
import java.util.ResourceBundle;

public class Translator {

  protected ResourceBundle translatedStrings;

  public Translator(User user) {
    String language = user.getLanguage();
    String countryCode = user.getCountryCode();

    if (language == null || countryCode == null) {
      translatedStrings = ResourceBundle.getBundle("Translations", new Locale("en", "GB"));
    } else {
      translatedStrings = ResourceBundle.getBundle("Translations", new Locale(language, countryCode));
    }
  }

  public String getTranslatedString(String value) {
    return translatedStrings.getString(value);
  }

}
