package utils;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.MessagePart;
import org.apache.commons.codec.binary.Base64;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Gmail_API {

  private Gmail service;
  private static final Logger logger = Logger.getLogger(Gmail_API.class.getName());
  private final String email;

  public Gmail_API(String email) {
    this.email = email;
    NetHttpTransport httpTransport = null;
    try {
      httpTransport = GoogleNetHttpTransport.newTrustedTransport();
    } catch (IOException | GeneralSecurityException e) {
      logger.log(Level.INFO, "Exception ", e);
    }
    GsonFactory jsonFactory = GsonFactory.getDefaultInstance();
    try {
      service = new Gmail.Builder(httpTransport, jsonFactory, getCredentials(httpTransport, jsonFactory)).setApplicationName("e2e tests").build();
    } catch (IOException e) {
      logger.log(Level.INFO, "Exception ", e);
    }
  }

  private Credential getCredentials(final NetHttpTransport httpTransport, GsonFactory jsonFactory) throws IOException {
    String emailSecretFilePath = "/testData/emailCredentials/gmailSecret_" + email + ".json";

    GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(jsonFactory, new InputStreamReader(Gmail_API.class.getResourceAsStream(emailSecretFilePath)));
    GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(httpTransport, jsonFactory, clientSecrets,
      Set.of(GmailScopes.MAIL_GOOGLE_COM)).setDataStoreFactory(new FileDataStoreFactory(Paths.get("src/test/resources/testData/emailCredentials/").toFile())).setAccessType("offline").build();
    LocalServerReceiver receiver = new LocalServerReceiver.Builder().setPort(8888).build();
    return new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");
  }

  public String readMail(String query) {
    // Calculate the timestamp for the desired time range
    long currentTime = System.currentTimeMillis() / 1000; // Convert to seconds
    long timeRangeInSeconds = 10; // reading email not older then this value
    long startTime = currentTime - timeRangeInSeconds;

    // Construct the query to filter messages not older than the specified time range
    query = query + " after:" + startTime;

    // Retrieve the ListMessagesResponse using the query
    ListMessagesResponse response = null;
    Instant startTimee = Instant.now();
    while (Duration.between(startTimee, Instant.now()).toSeconds() < 60) {//waiting this amount of seconds for an email based on query
      try {
        response = service.users().messages().list("me").setQ(query).execute();
      } catch (IOException e) {
        logger.log(Level.INFO, "Exception ", e);
      }
      if (response.getMessages() != null) {
        break;
      }
    }

    List<Message> messages = response.getMessages();
    if (messages != null) {
      for (Message message : messages) {
        Message fullMessage = null;
        try {
          fullMessage = service.users().messages().get("me", message.getId()).execute();
        } catch (IOException e) {
          logger.log(Level.INFO, "Exception ", e);
        }
        return getHtmlPart(fullMessage);
      }
    }
    return null;
  }

  private String getHtmlPart(Message message) {
    if (message.getPayload().getMimeType().equals("text/html")) {
      return getBodyData(message.getPayload());
    } else if (message.getPayload().getMimeType().equals("multipart/alternative")) {
      for (MessagePart messagePart : message.getPayload().getParts()) {
        if (messagePart.getMimeType().equals("text/html")) {
          return getBodyData(messagePart);
        }
      }
    }
    return null;
  }

  private String getBodyData(MessagePart messagePart) {
    String bodyData = messagePart.getBody().getData();
    byte[] dataBytes = Base64.decodeBase64(bodyData);
    return new String(dataBytes);
  }

}
