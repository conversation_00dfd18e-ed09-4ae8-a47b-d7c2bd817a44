package utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CIP_API {
  private final HttpRequestHandler httpRequestHandler;

  public CIP_API() {
    this.httpRequestHandler = new HttpRequestHandler();
  }

  public String getToken() {
    Map<String, String> params = new HashMap<>();
    params.put("grant_type", "client_credentials");
    params.put("response_type", "code");
    params.put("scope", "b2c-profile");
    params.put("client_id", System.getenv("E2E_CIP_CLIENT_ID"));
    params.put("client_secret", System.getenv("E2E_CIP_CLIENT_SECRET"));

    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create("https://stg-energyid.bpglobal.com/am/oauth2/access_token"))
      .header("Content-Type", "application/x-www-form-urlencoded")
      .POST(HttpRequest.BodyPublishers.ofString(httpRequestHandler.buildFormData(params)))
      .build();

    HttpResponse<String> response = httpRequestHandler.send_HTTP_Request(request);
    JsonObject jsonObject = JsonParser.parseString(response.body()).getAsJsonObject();
    return jsonObject.get("access_token").getAsString();
  }

  public String createUser(String accessToken, String email) {
    String password = System.getenv("E2E_CIP_USER_PASSWORD");
    String requestBody = "{\n" +
      "  \"email\": \"" + email + "\",\n" +
      "  \"password\": \"" + password + "\"\n" +
      "}";

    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create("https://api-001-nonprod.bpglobal.com/tst/ieeo-identitymgmt-testuser/papi/v1/identity/user?realm=B2C"))
      .header("client_id", System.getenv("MULE_CLIENT_ID"))
      .header("client_secret", System.getenv("MULE_CLIENT_SECRET"))
      .header("x-correlation-id", String.valueOf(UUID.randomUUID()))
      .header("Authorization", "Bearer " + accessToken)
      .header("Content-Type", "application/json")
      .POST(HttpRequest.BodyPublishers.ofString(requestBody))
      .build();

    HttpResponse<String> response = httpRequestHandler.send_HTTP_Request(request);
    if (response.statusCode() != HttpURLConnection.HTTP_CREATED) {
      httpRequestHandler.pringLog(response);
      return null;
    }
    JsonObject jsonObject = JsonParser.parseString(response.body()).getAsJsonObject();
    return jsonObject.get("externalId").getAsString();
  }
}
