package utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ForgeRock_API {
  public static final Logger logger = Logger.getLogger(ForgeRock_API.class.getName());
  private static final String endpointUrl = "https://openam-bp-euw1-staging.id.forgerock.io";
  private static final String urlPath = "/openidm/managed/bravo_user";
  private final HttpRequestHandler httpRequestHandler;

  public ForgeRock_API() {
    this.httpRequestHandler = new HttpRequestHandler();
  }

  public String getToken() {
    Map<String, String> params = new HashMap<>();
    params.put("grant_type", "password");
    params.put("username", System.getenv("FORGEROCK_USERNAME"));
    params.put("password", System.getenv("FORGEROCK_PASSWORD"));
    params.put("scope", "fr:idm:*");
    params.put("client_id", System.getenv("FORGEROCK_CLIENT_ID"));
    params.put("client_secret", System.getenv("FORGEROCK_CLIENT_SECRET"));

    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create(endpointUrl + "/am/oauth2/realms/root/realms/alpha/access_token?auth_chain=PasswordGrant"))
      .header("Content-Type", "application/x-www-form-urlencoded")
      .POST(HttpRequest.BodyPublishers.ofString(httpRequestHandler.buildFormData(params)))
      .build();

    HttpResponse<String> response = httpRequestHandler.send_HTTP_Request(request);
    JsonObject jsonObject = JsonParser.parseString(response.body()).getAsJsonObject();
    return jsonObject.get("access_token").getAsString();
  }

  public String getForgeRockUserId(String forgeRockToken, String queryFilter, String searchFilter) {
    String query = "?null=null&_prettyPrint=true&_queryFilter=" + StringUtilities.encodedUrl(queryFilter + " eq '" + searchFilter + "'");

    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create(endpointUrl + urlPath + query))
      .header("Authorization", "Bearer " + forgeRockToken)
      .GET()
      .build();

    HttpResponse<String> response = httpRequestHandler.send_HTTP_Request(request);
    JsonObject jsonObject = JsonParser.parseString(response.body()).getAsJsonObject();
    JsonArray resultArray = jsonObject.getAsJsonArray("result");
    if (resultArray.size() == 1) {
      JsonObject firstResult = resultArray.get(0).getAsJsonObject();
      return firstResult.get("_id").getAsString();
    } else if (resultArray.isEmpty()) {
      return "";
    } else {
      logger.log(Level.WARNING, "Unexpected size " + resultArray.size());
    }
    return null;
  }


  public int deleteUser(String accesToken, String forgeRockUserId) {
    HttpRequest request = HttpRequest.newBuilder()
      .uri(URI.create(endpointUrl + urlPath + "/" + forgeRockUserId))
      .header("Authorization", "Bearer " + accesToken)
      .DELETE()
      .build();

    HttpResponse<String> response = httpRequestHandler.send_HTTP_Request(request);
    return response.statusCode();
  }
}
