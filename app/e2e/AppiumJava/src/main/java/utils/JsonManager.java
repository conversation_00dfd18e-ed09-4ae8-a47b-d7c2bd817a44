package utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import utils.Pojo.CreditCard;
import utils.Pojo.User;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class JsonManager {

  private static final Logger logger = Logger.getLogger(JsonManager.class.getName());
  private static Random rand;

  static {
    try {
      rand = SecureRandom.getInstanceStrong();
    } catch (NoSuchAlgorithmException e) {
      logger.log(Level.INFO, "error", e);
    }
  }

  public static User getUser(String id, boolean isAndroid) {
    if (isAndroid) {
      id += "_Android";
    } else {
      id += "_iOS";
    }
    // Work around until bpUS gets a test environment
    String brand = System.getenv("APP_BRAND");
    String env = System.getenv("RELEASE_VARIANT");
    if (brand.equals("bpUS")) {
      env = "Test";
    }
    File jsonFile = new File("src/test/resources/testData/users/users" + env + ".json");
    try {
      Gson gson = new GsonBuilder().create();
      Reader reader = new FileReader(jsonFile);
      User[] users = gson.fromJson(reader, User[].class);
      for (User user : users) {
        if (user.getId().equals(id)) {
          reader.close();
          return user;
        }
      }
      logger.log(Level.WARNING, "User " + id + " cannot be found in " + jsonFile.getPath());
      reader.close();
    } catch (Exception e) {
      logger.log(Level.INFO, "error", e);
    }
    return null;
  }

  public static void validateUsers() {
    File folder = new File("src/test/resources/testData/users");
    File[] listOfFiles = folder.listFiles();
    Set<String> emails = new HashSet<>();//emails and phones should be unique across all user files
    Set<String> phones = new HashSet<>();


    for (File file : listOfFiles) {
      System.out.println("Scanning file ------------- " + file.getName() + "-------------");
      Set<String> ids = new HashSet<>();
      try {
        Gson gson = new GsonBuilder().create();
        Reader reader = new FileReader(file);
        User[] users = gson.fromJson(reader, User[].class);
        for (User user : users) {
          findUser(user.getId().replace("_iOS", "").replace("_Android", ""));
          if (!ids.add(user.getId())) {
            logger.log(Level.WARNING, "The " + user.getId() + " is not unique in " + file.getPath());
          }
          if (!emails.add(user.getEmail())) {
            logger.log(Level.WARNING, "The " + user.getEmail() + " is not unique in " + file.getPath());
          }
          if (user.getPhoneNumber() != null && !phones.add(user.getPhoneNumber())) {
            logger.log(Level.WARNING, "The " + user.getPhoneNumber() + " is not unique in " + file.getPath());
          }
        }
        reader.close();
      } catch (Exception e) {
        logger.log(Level.INFO, "error", e);
      }
    }
    System.out.println();
    System.out.print("All users in ");
    for (File file : listOfFiles) {
      System.out.print(file.getName() + ", ");
    }
    System.out.println("have unique id's phones and emails");
  }

  public static CreditCard getSomeCreditCard(List<CreditCard> cards) {
    int randomIndex = rand.nextInt(cards.size());
    return cards.get(randomIndex);
  }

  public static List<CreditCard> getTestCards() {
    try {
      Gson gson = new GsonBuilder().create();
      Reader reader = new FileReader("src/test/resources/testData/cards.json");
      return Arrays.asList(gson.fromJson(reader, CreditCard[].class));
    } catch (FileNotFoundException e) {
      logger.log(Level.INFO, "error", e);
    }
    return null;
  }

  public static void findUser(String userId) {
    String folderPath = "src/test/resources/features";
    List<Path> resultFiles = searchInFiles(Paths.get(folderPath), userId);
    if (resultFiles.isEmpty()) {
      System.out.println(userId + " is not used");
      return;
    }

    if (resultFiles.size() > 1) {
      System.out.print(userId + " is used in more feature files: ");
      for (Path file : resultFiles) {
        System.out.print(file.getFileName() + ", ");
      }
      System.out.println();
    }
  }

  public static List<Path> searchInFiles(Path folder, String searchString) {
    List<Path> matchingFiles = new ArrayList<>();
    try {
      Files.walkFileTree(folder, new SimpleFileVisitor<>() {
          public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
              if (Files.isRegularFile(file) && containsString(file, searchString)) {
                  matchingFiles.add(file);
              }
              return FileVisitResult.CONTINUE;
          }
      });
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    return matchingFiles;
  }

  private static boolean containsString(Path file, String searchString) {
    try {
      return Files.lines(file).anyMatch(line -> line.contains(searchString));
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

}
