package utils;

import io.appium.java_client.AppiumDriver;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.tuple.Pair;
import utils.Pojo.CreditCard;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

public class StringUtilities {

  private static final Logger logger = Logger.getLogger(StringUtilities.class.getName());

  private StringUtilities() {
    String imageUtils = "ImageUtils";
    throw new IllegalStateException(imageUtils + " class");
  }

  public static String getStringBefore(String value, String a) {
    // Return substring containing all characters before a string.
    int posA = value.indexOf(a);
    if (posA == -1) {
      return "";
    }
    return value.substring(0, posA);
  }

  public static String getStringAfter(String input, String after) {
    int lastIndex = input.lastIndexOf(after);
    if (lastIndex != -1) {
      return input.substring(lastIndex + after.length());
    } else return null;
  }

  public static String substringBetween(String str, String open, String close) {
    if (str == null || open == null || close == null) {
      return null;
    }
    int start = str.indexOf(open);
    if (start != -1) {
      int end = str.indexOf(close, start + open.length());
      if (end != -1) {
        return str.substring(start + open.length(), end);
      }
    }
    return null;
  }

  public static String removeEmailPlusAddressing(String str, String start, String end) {
    String toRemove = substringBetween(str, start, end);
    return str.replace("+" + toRemove, "");
  }

  public static String getLastChars(String input, int number) {
    return input.substring(input.length() - number);
  }

  public static String decodedUrl(String encodedUrl) {
    String decodedUrl = StringEscapeUtils.unescapeHtml4(encodedUrl);
    try {
      URI uri = new URI(decodedUrl);
      decodedUrl = uri.toString();
    } catch (URISyntaxException e) {
      logger.log(Level.INFO, "Exception ", e);
    }
    return decodedUrl;
  }

  public static String encodedUrl(String url) {
    try {
      return URLEncoder.encode(url, "UTF-8");
    } catch (UnsupportedEncodingException e) {
      logger.log(Level.INFO, "Exception ", e);
    }
    return null;
  }

  public static Date formatDateString(String dateStr) {
    String[] possiblePatterns = {
      "d MMMM h:mm a",  // "11 July 10:05 AM"
      "MMMM dd hh:mm a",  // "July 11 10:05 AM"
      "dd MMMM HH:mm",  // "11 July 10:05"
      "EEEE MMMM d yyyy HH.mm"  //"Thursday July 11 2024 10.05"
    };

    for (String pattern : possiblePatterns) {
      try {
        SimpleDateFormat formatter = new SimpleDateFormat(pattern, Locale.ENGLISH);
        Date date = formatter.parse(dateStr);
        if (getYearFromDate(date) == 1970) {
          date = setYearForDate(date);
        }
        return date;
      } catch (ParseException e) {
        logger.log(Level.INFO, "Exception ", e.getMessage());
      }
    }
    throw new IllegalArgumentException("Invalid date format: " + dateStr);
  }

  public static String formatDate(Date date, String format) {
    SimpleDateFormat dateFormat = new SimpleDateFormat(format);
    return dateFormat.format(date);
  }

  public static int getYearFromDate(Date currentDate) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(currentDate);
    return calendar.get(Calendar.YEAR);
  }

  public static Date setYearForDate(Date currentDate) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(currentDate);
    int currentYear = Calendar.getInstance().get(Calendar.YEAR);
    calendar.set(Calendar.YEAR, currentYear);
    return calendar.getTime();
  }

  public static ZoneId getTimeZone(String dateTimeString) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
    try {
      ZonedDateTime dateTime = ZonedDateTime.parse(dateTimeString, formatter);
      return dateTime.getZone();
    } catch (DateTimeParseException e) {
      logger.log(Level.INFO, "Exception ", e);
      return null;
    }
  }

  public static Date addZoneOffset(Date date, int seconds) {
    Instant instant = date.toInstant();
    Instant resultInstant = instant.plusSeconds(seconds);
    return Date.from(resultInstant);
  }

  public static String calculateStartChargeDate(String deviceTime, String startChargeTime, String chargePointTimeZone) {
    // The time on the invoice is based on the CP time zone.
    // If the device time zone is different from the CP time zone
    // then the expected time on the invoice needs to be calculated

    ZoneId deviceTimeZone = getTimeZone(deviceTime);
    ZoneOffset deviceZoneOffset = deviceTimeZone.getRules().getStandardOffset(Instant.now());
    ZoneId chargePointZoneId = ZoneId.of(chargePointTimeZone);
    ZonedDateTime zonedDateTime = ZonedDateTime.now(chargePointZoneId);
    ZoneOffset zoneOffsetChargePoint = zonedDateTime.getOffset();
    int zoneDifference = zoneOffsetChargePoint.getTotalSeconds() - deviceZoneOffset.getTotalSeconds();

    Date chargingDate = StringUtilities.formatDateString(startChargeTime);
    chargingDate = addZoneOffset(chargingDate, zoneDifference);
    return StringUtilities.formatDate(chargingDate, "dd-MM-yyyy HH:mm");
  }

  public static long calculateMinutesDifference(String dateTime, String offsetDateTime) {
    //difference between e.g. 31 May 12:50 and 2024-05-31T12:51:30+02:00
    Date startDateTime = StringUtilities.formatDateString(dateTime);
    startDateTime = setYearForDate(startDateTime);
    OffsetDateTime endDateTime = OffsetDateTime.parse(offsetDateTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    return ChronoUnit.MINUTES.between(startDateTime.toInstant(), endDateTime.toInstant());
  }

  public static void savePageSourceToFile(AppiumDriver driver) {
    savePageSourceToFile("source", driver);
  }

  public static void savePageSourceToFile(String fileName, AppiumDriver driver) {
    String pageSource = driver.getPageSource();
    File file = new File(fileName + ".xml");
    FileWriter writer;
    try {
      writer = new FileWriter(file);
      writer.write(pageSource);
      writer.close();

    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static String getNameAndLastFour(CreditCard card) {
    String lastFour = StringUtilities.getLastChars(card.getCardNumber(), 4);
    return card.getCardBrand() + "..." + lastFour;
  }

  public static CreditCard nameAndLastFourToCardObject(String nameAndLastFour, List<CreditCard> creditCards) {
    for (CreditCard card : creditCards) {
      String brand = getStringBefore(nameAndLastFour, "...");
      String lastFour = getLastChars(nameAndLastFour, 4);
      String lastFourObj = getLastChars(card.getCardNumber(), 4);
      if (card.getCardBrand().equals(brand) && lastFourObj.equals(lastFour)) {
        return card;
      }
    }
    return null;
  }

  public static List<CreditCard> cardsToObjects(List<String> existingCards, List<CreditCard> creditCards) {
    List<CreditCard> cards = new ArrayList<>();
    for (String card : existingCards) {
      cards.add(nameAndLastFourToCardObject(card, creditCards));
    }
    return cards;
  }

  public static int getUnsecuredCardsCount(List<CreditCard> creditCards) {
    int count = 0;
    for (CreditCard card : creditCards) {
      if (card.isSecured()) {
        count++;
      }
    }
    return count;
  }

  public static List<Pair<String, Boolean>> removeDuplicates(List<Pair<String, Boolean>> pairList) {
    Set<Pair<String, Boolean>> set = new HashSet<>(pairList);
    return new ArrayList<>(set);
  }

  public static void logToXml() {//Pipeline prints page source to log. Removing timestamp from pipeline log and save it to xml
    String inputFilePath = "log.txt";
    String outputFilePath = "log.xml";
    try (
      BufferedReader reader = new BufferedReader(new FileReader(inputFilePath));
      BufferedWriter writer = new BufferedWriter(new FileWriter(outputFilePath))

    ) {
      String line;

      writer.write("<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>");
      while ((line = reader.readLine()) != null) {
        String timestamp = getStringBefore(line, " <");
        line = line.replace(timestamp, "");
        writer.write(line);
        writer.newLine();
      }
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  public static Date getDateAfterSeconds(Long seconds) {
    long currentTimeMillis = System.currentTimeMillis();
    long expirationTimeMillis = currentTimeMillis + TimeUnit.SECONDS.toMillis(seconds);
    return new Date(expirationTimeMillis);
  }

}

