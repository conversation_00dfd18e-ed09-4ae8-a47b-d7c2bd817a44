package utils;

// Install the Java helper library from twilio.com/docs/java/install

import com.twilio.Twilio;
import com.twilio.base.ResourceSet;
import com.twilio.rest.api.v2010.account.Message;

import java.util.Date;

public class Twillio_API {

  // Find your Account SID and Auth Token at twilio.com/console
  // and set the environment variables. See http://twil.io/secure
  public static final String ACCOUNT_SID = System.getenv("TWILIO_ACCOUNT_SID");
  public static final String AUTH_TOKEN = System.getenv("TWILIO_AUTH_TOKEN");

  private Twillio_API() {
    throw new IllegalStateException("Twillio class");
  }

  public static String read_SMS_Message(String phoneNumber) {
    Date sentToTwillio = new Date();
    int timeout = 60000; //Waiting 60 seconds for new message, checking every 5 seconds
    int interval = 5000;
    Twilio.init(ACCOUNT_SID, AUTH_TOKEN);
    ResourceSet<Message> messages;
    while (timeout > 0) {
      messages = Message.reader().read().setLimit(10);

      for (Message message : messages) {
        Date sentToMe = Date.from(message.getDateSent().toInstant());
        long messageAge = sentToTwillio.getTime() - sentToMe.getTime();
        if (messageAge < 60000 && message.getTo().equals(phoneNumber.replace("-", ""))) {
          return message.getBody();
        }
      }
      try {
        Thread.sleep(interval);
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
      }
      timeout = timeout - interval;
    }
    return "";
  }
}

