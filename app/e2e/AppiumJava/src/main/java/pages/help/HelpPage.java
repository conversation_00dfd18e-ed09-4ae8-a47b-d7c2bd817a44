package pages.help;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class HelpPage extends BasePage {

  @AndroidFindBy(id = "HelpPage.CallButton")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name=\"HelpPage.CallButton\"]")
  private WebElement telPhoneButton;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='+44 ************']")
  @iOSXCUITFindBy(iOSNsPredicate = "label == '+44 ************'")
  private WebElement telUKNumber;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text=\"+31 85 002 22 86\"]")
  @iOSXCUITFindBy(iOSNsPredicate = "label == '+31 85 002 22 86'")
  private WebElement telNLNumber;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc=\"+34 900 948 088\"]")
  @iOSXCUITFindBy(iOSNsPredicate = "label == '+34 900 948 088'")
  private WebElement telESNumber;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc=\"************\"]")
  @iOSXCUITFindBy(iOSNsPredicate = "label == '************'")
  private  WebElement telUSNumber;

  @AndroidFindBy(accessibility = "Visit our FAQs page")
  @iOSXCUITFindBy(accessibility = "Visit our FAQs page")
  private WebElement visitFaqPageButton;

  public HelpPage(AppiumDriver driver) {
        super(driver);
    }

  public Boolean telNumberIsDisplayedForCountry(String country) {
    switch (country.toUpperCase()) {
      case "UK":
        return telUKNumber.isDisplayed();
      case "NL":
        return telNLNumber.isDisplayed();
      case "ES":
        return telESNumber.isDisplayed();
      case "US":
        return telUSNumber.isDisplayed();
      default:
        return false;
    }
  }

  public void clickTelPhoneButton() {
    telPhoneButton.click();
  }
}
