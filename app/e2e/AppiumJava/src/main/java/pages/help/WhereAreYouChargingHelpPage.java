package pages.help;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import static org.testng.Assert.assertTrue;

public class WhereAreYouChargingHelpPage extends BasePage {

  @AndroidFindBy(accessibility = "Show help page")
  @iOSXCUITFindBy(accessibility = "ChangeLocation.SubmitButton")
  private WebElement showHelpPageBtn;

  @AndroidFindBy(accessibility = "UK")
  @iOSXCUITFindBy(accessibility = "CountrySelectionCard.RadioButton.UK")
  private WebElement ukSelectorRdo;

  @AndroidFindBy(accessibility = "NL")
  @iOSXCUITFindBy(accessibility = "CountrySelectionCard.RadioButton.NL")
  private WebElement nlSelectorRdo;

  @AndroidFindBy(accessibility = "ES")
  @iOSXCUITFindBy(accessibility = "CountrySelectionCard.RadioButton.ES")
  private WebElement esSelectorRdo;

  @AndroidFindBy(accessibility = "US")
  @iOSXCUITFindBy(accessibility = "CountrySelectionCard.RadioButton.US")
  private WebElement usSelectorRdo;

  public WhereAreYouChargingHelpPage(AppiumDriver driver) {
        super(driver);
    }

  public void verifyWhereAreYouChargingHelpPage() {
    assertTrue(isElementPresent(showHelpPageBtn), " Show Help Button is not present on the page");
    assertTrue(isElementPresent(ukSelectorRdo), " UK Selector Element is not present on the page");
    assertTrue(isElementPresent(nlSelectorRdo), " NL Selector Element is not present on the page");
    assertTrue(isElementPresent(esSelectorRdo), " NL Selector Element is not present on the page");
  }

  public void selectGuestChargingHelpForCountry(String country) {
    switch (country.toUpperCase()) {
      case "UK":
        ukSelectorRdo.click();
        break;
      case "NL":
        nlSelectorRdo.click();
        break;
      case "ES":
        esSelectorRdo.click();
        break;
      case "US":
        usSelectorRdo.click();
    }
  }

  public void clickShowHelpPageButton() {
    showHelpPageBtn.click();
  }

}
