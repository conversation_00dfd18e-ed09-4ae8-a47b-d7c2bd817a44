package pages.facebook;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;

public class LoginToYourFacebookAccountPage extends BasePage {

  @AndroidFindBy(xpath = "//*[contains(@text, 'Log in to your Facebook account')]")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//android.widget.EditText[contains(@hint, 'email')]")
  private WebElement emailField;

  @AndroidFindBy(xpath = "//android.widget.EditText[contains(@hint,'password') or contains(@hint,'Password')]")
  private WebElement passwordField;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='Log in']")
  private WebElement loginButton;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='Not now']")
  private WebElement buttonNotNow;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='Continue as Pulse']")
  private WebElement buttonContinueAsPulse;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='Continue as John']")
  private WebElement buttonContinueAsJohn;

  public LoginToYourFacebookAccountPage(AppiumDriver driver) {
    super(driver);
  }

  public boolean isLoginDisplayed() {
    return loginButton.isDisplayed();
  }

  public void enterFaceBookEmailAddressField(String email) {
    emailField.sendKeys(email);
  }

  public String getEmail() {
    return emailField.getText();
  }

  public void enterPasswordField(String password) {
    passwordField.sendKeys(password);
  }

  public String getPassword() {
    return passwordField.getText();
  }

  public void clickFaceBookLoginButton() {
    loginButton.click();
    if(isElementPresent(buttonNotNow,2000)){
      buttonNotNow.click();
    }
  }

  public void clickContinue() {
    String brand = System.getenv("APP_BRAND");
    if (brand.equals("bpUS")) {
      waitUntilClickable(buttonContinueAsJohn, 5).click();
    } else {
      waitUntilClickable(buttonContinueAsPulse, 5).click();
    }

  }

}
