package pages.facebook;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class AllowCookiesPage extends BasePage {

  @FindBy(xpath = "//button[@title='Allow all cookies']")
  public WebElement allowCookiesButton;

  public AllowCookiesPage(AppiumDriver driver) {
    super(driver);
  }

  public void clickAllowCookiesButton() {
    if (isElementPresent(allowCookiesButton, 2000)) {
      allowCookiesButton.click();
    }
  }

}
