package pages.navigation;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class BottomTabNavigatorPage extends BasePage {

  @AndroidFindBy(accessibility = "Map")
  @iOSXCUITFindBy(accessibility = "Map")
  private WebElement mapIcon;

  @AndroidFindBy(accessibility = "Profile")
  @iOSXCUITFindBy(accessibility = "Profile")
  private WebElement profileIcon;

  @AndroidFindBy(accessibility = "Charge")
  @iOSXCUITFindBy(accessibility = "Charge")
  private WebElement chargeIcon;

  @AndroidFindBy(xpath = "//*[@resource-id='activeChargeAnimation']/android.widget.ImageView")
  private WebElement chargeIconCharging;

  @AndroidFindBy(accessibility = "Help")
  @iOSXCUITFindBy(accessibility = "Help")
  private WebElement helpIcon;

  @AndroidFindBy(id = "HelpPage.CallButton")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name=\"HelpPage.CallButton\"]")
  private WebElement telPhoneButton;

  @AndroidFindBy(xpath = "//*[@resource-id='Header']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Profile']")
  private WebElement textPageTitle;

  public BottomTabNavigatorPage(AppiumDriver driver) {
        super(driver);
    }

  public void clickMapButton() {
    mapIcon.click();
  }

  public void clickHelpButton() {
    helpIcon.click();
  }

  public void clickProfileButton() {
    profileIcon.click();
  }

  public void clickChargeButton() {
    chargeIcon.click();
  }

  public boolean chargeButtonIsCharging() {
    return chargeIconCharging.isDisplayed();
  }
}
