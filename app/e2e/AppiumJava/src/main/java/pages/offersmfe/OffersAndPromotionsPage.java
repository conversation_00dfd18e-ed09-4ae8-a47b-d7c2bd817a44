package pages.offersmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.AndroidFindBys;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import java.time.LocalDate;
import java.util.List;

public class OffersAndPromotionsPage extends BasePage {

  @AndroidFindBy(accessibility = "Login")
  @iOSXCUITFindBy(accessibility = "LoginButton")
  private WebElement btnLogin;

  @AndroidFindBy(accessibility = "Create an account")
  @iOSXCUITFindBy(accessibility = "RegisterButton")
  private WebElement btnCreateAnAccount;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Want to add an offer code?']")
  @iOSXCUITFindBy(accessibility = "Want to add an offer code?")
  private WebElement titleWantToAddAnOfferCode;

  @AndroidFindBy(xpath = "//*[@resource-id='left-button']")
  @iOSXCUITFindBy(accessibility = "left-button")
  private WebElement backBtn;

  @AndroidFindBy(xpath = "//android.widget.TextView[@content-desc='Offers and promotions']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Offers and promotions']")
  private WebElement textPageTitle;

  @AndroidFindBy(id = "nothingToShowYetHeading")
  @iOSXCUITFindBy(accessibility = "nothingToShowYetHeading")
  private WebElement nothingToShowYetHeading;

  @AndroidFindBy(accessibility = "Used and expired offers")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='UserOffersFooter']")
  private WebElement usedAndExpiredOffersButton;

  @AndroidFindBy(xpath = "//android.widget.TextView[@resource-id=\"NothingToShow.Heading\"]")
  @iOSXCUITFindBy(accessibility = "NoUsedOrExpiredOffers")
  private WebElement nothingToShowScreen;

  @AndroidFindBy(id = "NothingToShow.Description")
  @iOSXCUITFindBy(accessibility = "NothingToShow.Description")
  private WebElement nothingToShowDescription;

  @AndroidFindBy(xpath = "//android.view.ViewGroup[@resource-id=\"ComingSoonScreen\"]")
  @iOSXCUITFindBy(accessibility = "Coming soon")
  private WebElement comingSoonScreen;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='CreditOfferCard']")})
  private List<WebElement> activeOfferList;

  @AndroidFindBy(xpath = "//*[@resource-id='FormattedCurrency.0.currency']")
  private WebElement currencySign;

  @AndroidFindBy(xpath = "//*[@resource-id='FormattedCurrency.1.integer']")
  private WebElement currencyAmount;

  @AndroidFindBy(xpath = "//*[@resource-id='FormattedCurrency.2.decimal']")
  private WebElement currencyDot;

  @AndroidFindBy(xpath = "//*[@resource-id='FormattedCurrency.3.fraction']")
  private WebElement currencyDecimal;

 @AndroidFindBy(xpath = "//*[@resource-id='OfferRemainingCredit']/android.widget.TextView[contains(@text,'Expiry')]")
  private WebElement expiryDate;

  @AndroidFindBy(xpath = "//*[@resource-id='OfferDescription']")
  private WebElement textOfferDesc;

  public OffersAndPromotionsPage(AppiumDriver driver) {
    super(driver);
  }

  public void clickBackButton() {
    backBtn.click();
  }

  public boolean wantToAddAnOfferTitleIsDisplayed() {
    return isElementPresent(titleWantToAddAnOfferCode, 5);
  }

  public void clickUsedAndExpiredOffersButton(){
    usedAndExpiredOffersButton.click();
  }

  public boolean nothingToShowScreenIsDisplayed() {
    return isElementPresent(nothingToShowScreen, 5);
  }

  public boolean comingSoonScreenIsDisplayed() {
    return isElementPresent(comingSoonScreen, 5);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public boolean activeListIsDisplayed() {
    return activeOfferList.get(0).isDisplayed();
  }

  public boolean offerCreditAndDateIsDisplayed(String activeCode, String credit, LocalDate expireDate) {
    return activeOfferList.stream().anyMatch(offer -> {
      boolean creditExist = getChainedElement(offer, currencySign) != null && getChainedElement(offer, currencyAmount) != null
        && getChainedElement(offer, currencyDot) != null && getChainedElement(offer, currencyDecimal) != null
        && getChainedElement(offer, textOfferDesc) != null && getChainedElement(offer, expiryDate) != null;

      return creditExist && (getChainedElement(offer, currencySign).getText() + getChainedElement(offer, currencyAmount).getText()
        + getChainedElement(offer, currencyDot).getText() + getChainedElement(offer, currencyDecimal).getText()).contains(credit)
        && getChainedElement(offer, expiryDate).getText().contains(String.valueOf(expireDate.getDayOfMonth()))
        && getChainedElement(offer, expiryDate).getText().contains(String.valueOf(expireDate.getMonthValue()))
        && getChainedElement(offer, expiryDate).getText().contains(String.valueOf(expireDate.getYear()))
      && getChainedElement(offer, textOfferDesc).getText().contains(activeCode);
    });
  }
}
