package pages.offersmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.AndroidFindBys;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import java.util.List;

public class UsedAndExpiredOffersPage extends BasePage {

  @AndroidFindBy(xpath = "//android.widget.TextView[@content-desc='Used and Expired offers']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Used and Expired offers']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath="//*[@resource-id='CreditOfferCard']")})
  private List<WebElement> archivedOfferList;

  @AndroidFindBy(xpath = "//*[@resource-id='OfferDescription']")
  private WebElement textOfferDesc;



  public UsedAndExpiredOffersPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

    public boolean expiredOfferIsDisplayed(String expiredCode) {
      return archivedOfferList.stream().anyMatch(offer ->
        getChainedElement(offer, textOfferDesc) != null
          && getChainedElement(offer, textOfferDesc).getText().contains(expiredCode)
      );
    }
}
