package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.WebElement;
import utils.StringUtilities;

import java.util.List;

public class RecentTransactionsPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Recent transactions']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Recent transactions']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='list-item']")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[@name='list-item']")})
  private List<WebElement> listTransactions;

  @AndroidFindBy(xpath = "//android.widget.TextView[@resource-id='date']")
  @iOSXCUITFindBy(xpath = "//*[@name= 'date']")
  private WebElement textTransactionDateTime;

  @AndroidFindBy(xpath = "//*[@resource-id='price']")
  @iOSXCUITFindBy(xpath = "//*[@name= 'price']")
  private WebElement textTotalCost;

  @AndroidFindBy(xpath = "//*[@resource-id='duration']")
  @iOSXCUITFindBy(xpath = "//*[@name= 'duration']")
  private WebElement textEnergyAndDuration;

  @AndroidFindBy(xpath = "//*[@resource-id='leftArrow']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name=\"left-button\"]")
  private WebElement iconBack;

  @AndroidFindBy(xpath = "//*[@resource-id='ErrorScreen.FillerWrapper']/android.widget.TextView[2]")
  private WebElement textCheckInternet;

  public RecentTransactionsPage(AppiumDriver driver) {
        super(driver);
    }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 5);
    return textPageTitle.getText();
  }

  public String getTextCheckInternet() {
    waitUntilVisible(textCheckInternet, 5);
    return textCheckInternet.getText();
  }

  public boolean isCheckInternetInvisible() {
    return waitUntilInvisible(textCheckInternet, 30);
  }

  public List<WebElement> getListTransactions() {
    return listTransactions;
  }

  public String getTransactionDateTime(int index) {
    if (isAndroid)
      return getChainedElement(listTransactions.get(index), textTransactionDateTime).getText();
    else {
      // extracting just date from transaction item e.g. from "12 October 10:42  £1.05 "
      return StringUtilities.getStringBefore(listTransactions.get(index).getAttribute("label"), "  ");
    }
  }

  public String getEnergyAndDuration(int index){
    return getChainedElement(listTransactions.get(index), textEnergyAndDuration).getText();
  }

  public String getTextTotalCost(int index) {
    if (isAndroid)
      return getChainedElement(listTransactions.get(index), textTotalCost).getText();
    else {
      return StringUtilities.substringBetween(listTransactions.get(index).getAttribute("label"), "  ", " ");
    }
  }

  public void goBack() {
    iconBack.click();
  }
}
