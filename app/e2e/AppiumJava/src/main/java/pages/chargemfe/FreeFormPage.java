package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class FreeFormPage extends BasePage {

  @iOSXCUITFindBy(xpath = "//XCUIElementTypeCell[@name='Freeform']/XCUIElementTypeOther/XCUIElementTypeImage")
  private WebElement iconFreeform;

  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='New Board…']")
  private WebElement textNewBoard;

  @iOSXCUITFindBy(xpath = "//XCUIElementTypeTextField")
  private WebElement inputBoardName;

  public FreeFormPage(AppiumDriver driver) {
        super(driver);
    }

  public void openWithFreeform() {
    iconFreeform.click();
    textNewBoard.click();
    waitMilliseconds(1000);
    inputBoardName.sendKeys("My board " + System.currentTimeMillis()); //unique name
    hideKeyboard();
  }

}
