package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import utils.StringUtilities;

public class ChargeSummaryPage extends BasePage {
  @AndroidFindBy(xpath = "//*[@resource-id='Charge summary']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Charge summary']")
  public WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='blue-header-text']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='blue-header-text']")
  public WebElement textAddress;

  @AndroidFindBy(xpath = "(//*[@resource-id='blue-header-text']/following-sibling::android.widget.TextView) [last()]")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[contains(@name, ' - ')]")
  public WebElement textDateTime;

  @AndroidFindBy(xpath = "//*[@resource-id='Charge status (SoC)']/android.widget.TextView[2]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name, '%')])[last()]")
  public WebElement textStatus;

  @AndroidFindBy(xpath = "//*[@resource-id='Average charge speed (Ø)']/android.widget.TextView[last()]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name, ' kW ')])[last()]")
  public WebElement textAverageSpeed;

  @AndroidFindBy(xpath = "//*[@resource-id='Energy delivered']/android.widget.TextView[last()]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name, ' kWh')])[last()]")
  public WebElement textEnergyDelivered;

  @AndroidFindBy(xpath = "//*[@text='Total cost']/following-sibling::android.widget.TextView[last()]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name, '€ ')])[last()]")
  public WebElement textTotalCost;

  @AndroidFindBy(xpath = "//*[@resource-id='summary-finish']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='summary-finish']")
  public WebElement buttonDone;

  public ChargeSummaryPage(AppiumDriver driver) {
    super(driver);
  }

  public WebElement getTextPageTitle() {
    return textPageTitle;
  }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 60);
    return textPageTitle.getText();
  }

  public String getAddress() {
    waitUntilVisible(textAddress, 60);
    return textAddress.getText();
  }

  public String getEndChargingDateTime() {
    return textDateTime.getText();
  }

  public String getStatus() {
    waitUntilVisible(textStatus, 30);
    if (isAndroid) {
      return textStatus.getText();
    } else {
      String status = textStatus.getText();
      String value = StringUtilities.getStringBefore(status, " ");
      String measure = StringUtilities.substringBetween(status, " ", " ");
      return value + " " + measure;
    }
  }

  public String getAverageSpeed() {
    return textAverageSpeed.getText();
  }

  public String getEnergyDelivered() {
    return textEnergyDelivered.getText();
  }

  public String getTotalCost() {
    return textTotalCost.getText();
  }

  public void clickDone() {
    waitUntilVisible(textAddress,60);
    swipeUp();
    buttonDone.click();
  }

}
