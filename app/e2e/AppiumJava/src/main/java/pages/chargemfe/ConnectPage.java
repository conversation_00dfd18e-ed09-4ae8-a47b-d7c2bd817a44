package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class ConnectPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Connect your vehicle']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Connect your vehicle']")
  public WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='ConnectVehicle.Button']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='ConnectVehicle.Button']")
  public WebElement btnConfirm;

  public ConnectPage(AppiumDriver driver) {
        super(driver);
    }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 20);
    return textPageTitle.getText();
  }

  public void clickConfirm() {
    btnConfirm.click();
  }
}
