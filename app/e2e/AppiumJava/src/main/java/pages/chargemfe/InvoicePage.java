package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class InvoicePage extends BasePage {

  @AndroidFindBy(xpath = "//*[contains(@text, 'B2Mobility')]")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[contains(@name, 'B2Mobility - ')]")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='com.google.android.apps.docs:id/pdf_view']/android.view.ViewGroup | //*[@resource-id='com.microsoft.skydrive:id/ms_pdf_viewer_surfaceview']/android.view.View")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name= 'com.apple.pencilkit.tiledView']/following-sibling::XCUIElementTypeTextView")
  private WebElement textInvoiceContent;

  @AndroidFindBy(xpath = "//*[@text='OneDrive' or @text='Drive PDF Viewer']")
  private WebElement buttonPdfViewer;

  @AndroidFindBy(xpath = "//*[@text='NOT NOW']")
  private WebElement buttonNotNow;

  @AndroidFindBy(xpath = "//*[@resource-id='android:id/button_always']")
  private WebElement buttonAlways;

  @AndroidFindBy(xpath = "//*[@resource-id='android:id/button_always']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='QLOverlayDoneButtonAccessibilityIdentifier']")
  private WebElement buttonDone;

  public InvoicePage(AppiumDriver driver) {
    super(driver);
  }

  public String getInvoiceTitle() {
    waitMilliseconds(2000);
    return textPageTitle.getText();
  }

  public String getInvoiceContent() {
    waitUntilVisible(textInvoiceContent, 30);
    if (isAndroid) {
      return textInvoiceContent.getAttribute("content-desc");
    } else {
      return textInvoiceContent.getText();
    }
  }

  public void settDefaultPdfViewer() {// some saucelab devices have other pdf viewer
    if (isAndroid) {
      if (isElementPresent(buttonPdfViewer, 5000)) {
        buttonPdfViewer.click();
        waitUntilClickable(buttonAlways, 1).click();
      }
      if (isElementPresent(buttonNotNow, 1000)) {
        buttonNotNow.click(); //OneDrive pdf viewer may ask for access to the device storage
      }
    }
  }

  public void clickDone() {
    buttonDone.click();
  }

}
