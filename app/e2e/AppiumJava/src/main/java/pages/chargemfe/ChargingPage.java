package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import utils.StringUtilities;

public class ChargingPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Charging']")
  @iOSXCUITFindBy(xpath = "//*[@name='Charging']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[contains(@text,'%')] | //*[contains(@text,'min')]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name,'%')])[last()]")
  private WebElement textChargeStatus;

  @AndroidFindBy(xpath = "(//*[@resource-id='Charger speed']/android.widget.TextView)[last()]")
  @iOSXCUITFindBy(xpath = "(//*[contains(@name, ' kW ')])[last()]")
  private WebElement textChargerSpeed;

  @AndroidFindBy(xpath = "(//*[@resource-id='Approximate price']/android.widget.TextView)[last()]")
  @iOSXCUITFindBy(xpath = "(//*[contains(@name, ' €')])[last()]")
  private WebElement textApproximatePrice;

  @AndroidFindBy(xpath = "(//*[@resource-id='Energy consumed*']/android.widget.TextView)[last()]")
  @iOSXCUITFindBy(xpath = "(//*[contains(@name, '  kWh')])[last()]")
  private WebElement textEnergyConsumed;

  @AndroidFindBy(xpath = "//*[@resource-id='stop-charge']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='stop-charge']")
  private WebElement btnStopCharge;

  public ChargingPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 120);
    return textPageTitle.getText();
  }

  public WebElement getTextPageTitle() {
    return textPageTitle;
  }

  public String getTextStatus() {
    if (isAndroid) {
      return textChargeStatus.getText();
    } else {
      String percent = StringUtilities.getStringBefore(textChargeStatus.getText(), " Duration");
      percent = StringUtilities.getStringAfter(percent, " ");
      return percent;
    }
  }

  public int getChargingPercentValue() {
    String chargingStatus = (String) handleStaleElementException((textChargeStatus), "getText");

    if (!isAndroid) {
      String percent = StringUtilities.getStringBefore(chargingStatus, " Duration");
      percent = StringUtilities.getStringAfter(percent, " ");
      chargingStatus = percent;
    }

    String numberStr = chargingStatus.replaceAll("[^\\d.]", "");
    return Integer.parseInt(numberStr);
  }

  public String getChargerSpeed() {
    return (String) handleStaleElementException(textChargerSpeed, "getText");
  }

  public String getEnergyConsumed() {
    return (String) handleStaleElementException(textEnergyConsumed, "getText");
  }

  public String getApproximatePrice() {
    return (String) handleStaleElementException(textApproximatePrice, "getText");
  }

  public void clickStopCharge() {
    handleStaleElementException(btnStopCharge, "click");
  }


}
