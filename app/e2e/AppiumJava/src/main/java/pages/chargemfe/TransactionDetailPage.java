package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class TransactionDetailPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Header']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//*[@name='Header']//XCUIElementTypeStaticText")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='vatButton']")
  @iOSXCUITFindBy(xpath = "//*[@name='vatButton']")
  private WebElement buttonDownloadInvoice;

  @AndroidFindBy(xpath = "//*[@resource-id='vatButton']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name=\"left-button\"]")
  private WebElement iconBack;

  public TransactionDetailPage(AppiumDriver driver) {
        super(driver);
    }

  public String getPageTitle() {
    waitForPageLoaded(3);
    waitUntilVisible(textPageTitle, 10);
    return textPageTitle.getText();
  }

  public void clickDownloadInvoice() {
    swipeUp();
    buttonDownloadInvoice.click();
  }

  public void goBack() {
    iconBack.click();
  }
}
