package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class SerialSearchPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Charge']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Charge']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='SerialSearchScreen.Heading']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Enter the charger ID to start\"]")
  private WebElement textEnterChargerId;

  @AndroidFindBy(xpath = "//*[@resource-id='SerialSearchInput']")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name, 'Enter the charger ID to start')])[last()]")
  private WebElement inputChargerId;

  @AndroidFindBy(accessibility = "Find charger")
  @iOSXCUITFindBy(xpath = "//*[@name='Find charger']")
  private WebElement btnFindCharger;

  @AndroidFindBy(xpath = "//*[@resource-id='SerialSearch.ErrorText']")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[@name=\"Enter the charger ID to start Clear No chargers match this ID, please check and try again\"])[2]")
  private WebElement serialSearchErrorText;

  public SerialSearchPage(AppiumDriver driver) {
    super(driver);
  }

  public WebElement getTextPageTitle() {
    return textPageTitle;
  }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 40);
    return textPageTitle.getText();
  }

  public void enterChargerId(String value) {
    inputChargerId.sendKeys(value);
  }

  public String getChargerId() {
    return inputChargerId.getText();
  }

  public void selectFindChargerButton() {
    hideKeyboard();
    btnFindCharger.click();
  }

  public String getTextEnterId() {
    waitUntilVisible(textEnterChargerId,40);
    return textEnterChargerId.getText();
  }

  public String getErrorChargerId() {
    return serialSearchErrorText.getText();
  }
}
