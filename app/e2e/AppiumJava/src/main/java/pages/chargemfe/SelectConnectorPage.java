package pages.chargemfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.AndroidFindBys;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.util.List;

public class SelectConnectorPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Select a connector']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Select a connector' and @index='1']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='SelectConnectorScreen.Address.Details']")
  @iOSXCUITFindBy(xpath = "//*[@name='SelectConnectorScreen.Address.Details']")
  private WebElement textAddress;

  @AndroidFindBy(xpath = "//*[contains(@resource-id, 'SelectConnectorScreen.ChargeID')]")
  @iOSXCUITFindBy(xpath = "//*[contains(@name, 'SelectConnectorScreen.ChargeID')]")
  private WebElement textChargeId;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='ConnectorCard']")})
  @iOSXCUITFindBy(xpath = "//*[@name='ConnectorCard']")
  private List<WebElement> listConnectors;

  @AndroidFindBy(xpath = "//*[@resource-id='RadioButton.Selected']")
  @iOSXCUITFindBy(xpath = "//*[@name='RadioButton.Selected']")
  private WebElement radioSelected;

  @AndroidFindBy(xpath = "//*[@resource-id='AvailabilityTag']//android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//*[@name='AvailabilityTag']")
  private WebElement textAvailability;

  @AndroidFindBy(xpath = "//*[@resource-id='floating-select-connector']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='floating-select-connector']")
  private WebElement buttonCharge;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='Add payment card']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//*[@name='Add payment card']")
  private WebElement buttonAddCard;

  @AndroidFindBy(xpath = "//*[@resource-id='payment-container']//android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//*[@name='payment-container']")
  private WebElement textCreditCard;

  @AndroidFindBy(xpath = "//*[@resource-id='payment-container']//android.widget.Button")
  @iOSXCUITFindBy(xpath = "//*[@name='payment-container']")
  private WebElement buttonAddCardLink;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Add card details']")
  @iOSXCUITFindBy(xpath = "//*[@name='Add card details']")
  private WebElement buttonAddCardDetail;

  public SelectConnectorPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 30);
    return textPageTitle.getText();
  }

  public String getAddress() {
    return textAddress.getText();
  }

  public String getChargeId() {
    return textChargeId.getText();
  }

  public void clickCharge() {
    buttonCharge.click();
  }

  public boolean isChargeButtonEnabled() {
    return waitForElementAttributeToBe(buttonCharge, "enabled", "true", 10);
  }

  public boolean isChargeButtonDisabled() {
    return waitForElementAttributeToBe(buttonCharge, "enabled", "false", 5);
  }

  public String getTextCreditCard() {
    return textCreditCard.getText();
  }

  public void clickAddCard() {
    waitUntilClickable(buttonAddCard, 1).click();
  }

  public WebElement getButtonAddCard() {
    return buttonAddCard;
  }

  public void clickAddCardDetail() {
    buttonAddCardDetail.click();
  }

  public WebElement getButtonAddCardDetail() {
    return buttonAddCardDetail;
  }

  public void selectConnector(String value) {
    WebElement connector;
    if (isAndroid) {
      connector = driver.findElement(By.xpath("//android.widget.TextView[@text='" + value + "']"));
    } else {
      connector = driver.findElement(By.xpath("//XCUIElementTypeOther[@name='ConnectorCard' and contains(@label, '" + value + "')]"));
    }
    connector.click();
  }

  public boolean isChecked(String value) {
    WebElement checked = driver.findElement(By.xpath("//android.widget.TextView[@text='" + value + "']/preceding-sibling::*[@resource-id='RadioButton']"));
    return checked.isDisplayed();
  }

  public WebElement selectAvailableConnector() {
    swipeFromElementToElement(listConnectors.get(0), textAddress, 1000);
    for (WebElement connector : listConnectors) {
      if (isAndroid) {
        if (getChainedElement(connector, textAvailability).getText().equals("AVAILABLE")) {
          connector.click();
          return connector;
        }
      } else {
        if (!connector.getAttribute("label").contains("UNAVAILABLE")) {
          connector.click();
          return connector;
        }
      }
    }
    return null;
  }

  public boolean isConnectorSelected(WebElement connector) {
    return isElementPresent(getChainedElement(connector, radioSelected));
  }

  public List<WebElement> getListConnectors() {
    return listConnectors;
  }

  public void clickAddCardLink() {
    buttonAddCardLink.click();
  }
}
