package pages.mapmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.WebElement;

import java.util.List;

public class MapSearchPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='SearchBar']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Search bar']")
  private WebElement frmSearchBarBox;

  @AndroidFindBy(xpath = "//*[@resource-id='SearchBar.ClearButton']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='SearchBar.ClearButton']")
  private WebElement iconClear;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='SearchResults']//android.widget.Button")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//XCUIElementTypeButton[contains(@name, 'SearchItem')]")})
  private List<WebElement> listSearchResults;

  @AndroidFindBy(xpath = "//*[@resource-id='SearchItem.Title']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='SearchItem.Title']")
  private WebElement resultTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='SearchItem.Address']")
  @iOSXCUITFindBy(xpath = "//*[@name='SearchItem.Location']")
  private WebElement resultAddress;

  public MapSearchPage(AppiumDriver driver) {
    super(driver);
  }

  public void enterSearchTerm(String searchTerm) {
    if (isElementPresent(iconClear, 500)) {
      iconClear.click();
    }
    frmSearchBarBox.click();
    waitUntilVisible(frmSearchBarBox, 5);
    frmSearchBarBox.sendKeys(searchTerm);
  }

  public List<WebElement> getSearchResults() {
    waitMilliseconds(1000);
    return listSearchResults;
  }

  public WebElement getSearchItem(String location, String areaCode) {
    for (WebElement result : listSearchResults) {
      if (isAndroid) {
        String loc = getChainedElement(result, resultTitle).getText();
        String code = getChainedElement(result, resultAddress).getText();
        if (loc.equals(location) && code.equals(areaCode)) {
          return result;
        }
      } else {
        String locationAndCode = getChainedElement(result, resultAddress).getText();
        if (locationAndCode.contains(location) && locationAndCode.contains(areaCode)) {
          return result;
        }
      }

    }
    return null;
  }

}
