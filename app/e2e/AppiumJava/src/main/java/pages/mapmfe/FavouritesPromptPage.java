package pages.mapmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import static org.testng.Assert.assertFalse;

public class FavouritesPromptPage extends BasePage {

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='Log in']/android.widget.TextView")
  @iOSXCUITFindBy(accessibility = "Log in")
  private WebElement favouritesPromptLoginButton;

  @AndroidFindBy(accessibility = "Close")
  @iOSXCUITFindBy(accessibility = "Close")
  private WebElement favouritesPromptCloseButton;

  public FavouritesPromptPage(AppiumDriver driver) {
        super(driver);
    }

  public String favouritesPromptIsDisplayed() {
    return favouritesPromptLoginButton.getText();
  }

  public void clickFavouritesCloseButton() {
    favouritesPromptCloseButton.click();
  }

  public void loginButtonIsNotDisplayed() {
    assertFalse(isElementPresent(favouritesPromptLoginButton), "favouritesPromptLoginButton is incorrectly displayed");
  }
}
