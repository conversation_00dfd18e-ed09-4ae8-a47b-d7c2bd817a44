package pages.mapmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.apache.commons.lang3.tuple.Pair;
import org.openqa.selenium.WebElement;
import utils.StringUtilities;

import java.util.ArrayList;
import java.util.List;

import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

public class FilterPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='MapFiltersScreen.Title']")
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[@name='MapFiltersScreen.Title']")})
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='Filter.Scroll.View']//android.widget.Button")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[@name='Filter.Scroll.View']//XCUIElementTypeButton")})
  private List<WebElement> listFilterItems;

  @AndroidFindBy(xpath = "//android.widget.Button/android.widget.TextView")
  @iOSXCUITFindBy(accessibility = "FilterFooter.Save")
  private WebElement buttonFilterName;

  @AndroidFindBy(xpath = "//android.widget.CheckBox | //android.widget.Switch")
  @iOSXCUITFindBy(accessibility = "FilterFooter.Save")
  private WebElement checkBoxFilterItem;

  @AndroidFindBy(xpath = "//android.widget.Switch")
  @iOSXCUITFindBy(accessibility = "FilterFooter.Save")
  private WebElement radioFavourites;

  @AndroidFindBy(xpath = "//*[@resource-id='FilterFooter.Save']")
  @iOSXCUITFindBy(accessibility = "FilterFooter.Save")
  private WebElement btnSave;

  @AndroidFindBy(accessibility = "Clear")
  @iOSXCUITFindBy(accessibility = "MapFiltersScreen.Clear")
  private WebElement btnClear;

  @AndroidFindBy(xpath = "//android.widget.ScrollView[@resource-id='Filter.Scroll.View']/android.view.ViewGroup/android.widget.Button[6]")
  @iOSXCUITFindBy(accessibility = "UberSwitch")
  private WebElement uberChargeSwitch;

  @AndroidFindBy(xpath = "//android.widget.Switch[@text=\"off\"]")
  @iOSXCUITFindBy(accessibility = "favouritesSwitch")
  private WebElement favouritesSwitch;

  public FilterPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public WebElement getItemByName(String name) {
    for (WebElement item : listFilterItems) {
      if (getChainedElement(item, buttonFilterName).getText().equals(name)) {
        return item;
      }
    }
    return null;
  }

  public void selectItem(String name) {
    if (!isItemSelected(name)) {
      getItemByName(name).click();
    }
  }

  public void clickClear() {
    btnClear.click();
  }

  public void clickSave() {
    btnSave.click();
  }

  public boolean isItemSelected(String name) {
    return isItemSelected(getItemByName(name));
  }

  public boolean isItemSelected(WebElement connectorItem) {
    return  getChainedElement(connectorItem, checkBoxFilterItem).getAttribute("checked").equals("true");
  }

  public boolean isFavouritesSelected(WebElement filterItem) {
    return getChainedElement(filterItem, radioFavourites).getAttribute("checked").equals("true");
  }

  public List<Pair<String, Boolean>> getFilterItems() {
    List<Pair<String, Boolean>> pairList = new ArrayList<>();
    String filterItem = getChainedElement(listFilterItems.get(0), buttonFilterName).getText();
    pairList.add(Pair.of(filterItem, isItemSelected(listFilterItems.get(0))));

    String bottomItem = " ";
    String bottomItemAfterScroll = "";

    while (!bottomItem.equals(bottomItemAfterScroll)) {//scrolling down, when the last item doesn't change we are at the bottom
      for (int i = 1; i < listFilterItems.size() - 1; i++) {// the first and the last item are present but not visible
        filterItem = getChainedElement(listFilterItems.get(i), buttonFilterName).getText();
        pairList.add(Pair.of(filterItem, isItemSelected(listFilterItems.get(i))));
      }
      bottomItem = getChainedElement(listFilterItems.get(listFilterItems.size() - 2), buttonFilterName).getText();
      swipeFromElementToElement(listFilterItems.get(listFilterItems.size() - 2), listFilterItems.get(2), 1000);
      bottomItemAfterScroll = getChainedElement(listFilterItems.get(listFilterItems.size() - 2), buttonFilterName).getText();
    }

    bottomItem = getChainedElement(listFilterItems.get(listFilterItems.size() - 1), buttonFilterName).getText();
    pairList.add(Pair.of(bottomItem, isFavouritesSelected(listFilterItems.get(listFilterItems.size() - 1))));

    pairList = StringUtilities.removeDuplicates(pairList);
    return pairList;
  }

  public void filterPageIsDisplayed() {
    assertTrue(isElementPresent(btnSave), " save button Element is not present on the page");
    assertTrue(isElementPresent(btnClear), " clear button Element is not present on the page");
  }

  public void uberChargeSwitchIsNotDisplayed() {
    assertFalse(isElementPresent(uberChargeSwitch), " the uber charge switch is incorrectly displayed");
  }

}
