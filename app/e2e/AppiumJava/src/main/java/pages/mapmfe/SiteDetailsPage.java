package pages.mapmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.WebElement;
import java.util.List;

public class SiteDetailsPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='SiteDetails.SegmentationTab.Details']")
  private WebElement tabDetails;

  @AndroidFindBy(xpath = "//*[@resource-id='SiteDetailsHeader.AddressStreet']")
  @iOSXCUITFindBy(xpath = "//*[@name='SiteDetailsHeader.AddressStreet']")
  private WebElement textAddress;

  @AndroidFindBy(xpath = "//*[@resource-id='MapBanner.PrimaryButton']")
  @iOSXCUITFindBy(accessibility = "QuickFilterPageButton")
  private WebElement buttonLogin;

  @AndroidFindBy(xpath = "//*[@resource-id='ChargeNowButton']")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[contains(@name,'Charge now')])[last()]")
  private WebElement buttonChargeNow;

  @AndroidFindBy(xpath = "//*[@resource-id='SiteDetailsMapMenu.FavouriteButton']")
  @iOSXCUITFindBy(xpath = "//*[@name='SiteDetailsMapMenu.FavouriteButton']")
  private WebElement iconFavourite;

  @AndroidFindBy(xpath = "//*[@resource-id='SiteDetails.CloseButton']")
  @iOSXCUITFindBy(xpath = "//*[@name='SiteDetails.CloseButton']")
  private WebElement iconClose;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='ChargerCard.CardContainer']//*[@resource-id='FormattedKilowatt']//preceding-sibling::android.widget.TextView[last()]")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[@name='']")})
  private List<WebElement> listConnectors;

  @AndroidFindBy(xpath = "//*[@text='Customer care:']/following-sibling::android.widget.Button/android.widget.TextView")
  private WebElement textPhoneNumber;

  public SiteDetailsPage(AppiumDriver driver) {
        super(driver);
    }

  public String getTextAddress() {
    return textAddress.getText();
  }

  public void loginOrCreateAnAccountButtonIsClicked() {
    buttonLogin.click();
  }

  public void markAsFavourite() {
    iconFavourite.click();
  }

  public void clickChargeNow() {
    swipeUp();
    waitMilliseconds(1000);

    if (isAndroid) {
      buttonChargeNow.click();
    } else {
      //Cannot locate button. It is located at 80% of 'Log in or create an account' element
      int x = buttonChargeNow.getSize().getWidth() / 2; //middle of the screen/button
      int height = buttonChargeNow.getSize().getHeight();
      int y = (int) (buttonChargeNow.getLocation().getY() + height - (height * 0.59));
      tapCoordinates(x, y);
    }
  }

  public void closeDetails() {
    iconClose.click();
  }

  public WebElement getConnector(String type) {
    for (WebElement conn : listConnectors) {
      if (conn.getText().equals(type)) {
        return conn;
      }
    }
    return null;
  }

  public void openDetailsTab() {
    tabDetails.click();
  }

  public String getCustomerCarePhone() {
    swipeUp();
    return textPhoneNumber.getText();
  }

  public boolean favoriteButonIsDisplayed() {
    return isElementPresent(iconFavourite);
  }
}
