package pages.mapmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.Point;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.PageFactory;
import utils.ImageUtils;

import java.awt.*;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class MapPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@content-desc='Bottom Sheet']")
  @iOSXCUITFindBy(accessibility = "HandleComponent")
  private WebElement nearbyChargingLocationsHeader;

  @AndroidFindBy(xpath = "//*[@resource-id='SitesWidgetHeader.HeaderText']")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[@name='Bottom Sheet'])[last()]")
  private WebElement nearbyChargingLocationsTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='MapMenu.Recenter']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='MapMenu.Recenter']")
  private WebElement btnRecenterIcon;

  @iOSXCUITFindBy(accessibility = "Allow While Using App")
  private WebElement allowPrompt;

  @AndroidFindBy(xpath = "//android.widget.Button[contains(@resource-id, 'SiteCard.')]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[@name='adjustable charge point list'])")
  private List<WebElement> listChargeLocations;

  @AndroidFindBy(accessibility = "map filters")
  @iOSXCUITFindBy(accessibility = "QuickFilterPageButton")
  private WebElement filterBtn;

  @AndroidFindBy(xpath = "//android.widget.Button[@resource-id='QuickFiltersButtonEvConnectorType']/android.widget.TextView")
  @iOSXCUITFindBy(accessibility = "QuickFilterPageButton")
  private WebElement buttonAllConnectors;

  @AndroidFindBy(accessibility = "Favourites")
  @iOSXCUITFindBy(accessibility = "QuickFiltersButtonFavourites")
  private WebElement favouritesBtn;

  @AndroidFindBys({@AndroidBy(xpath = "//android.view.View[@content-desc='Map Marker'] | //*[@content-desc='Google Map']/android.view.View")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//XCUIElementTypeButton[contains(@name, 'AIRGMSMarker')]")})
  private List<WebElement> listMapItems;

  public MapPage(AppiumDriver driver) {
    super(driver);
  }

  public void clickFilterButton() {
    filterBtn.click();
  }

  public String getAllConnectorButtonName() {
    waitUntilVisible(buttonAllConnectors, 1000);
    return buttonAllConnectors.getText();
  }

  public void clickCurrentLocation() {
    btnRecenterIcon.click();
  }

  public void clickFavouritesButton() {
    favouritesBtn.click();
  }

  public void clickAllowWhileUsingPrompt() {
    WebElement either = waitForEitherElement(allowPrompt, btnRecenterIcon); //on some devices map is opened without the "Allow While Using App" prompt
    if (either.getText().equals("Allow While Using App")) {
      either.click();
    }
  }

  public boolean theChargingLocationsMenuIsDisplayed() {
    return isElementPresent(nearbyChargingLocationsTitle);
  }

  public void swipeUpTheChargingLocationMenu() {
    Point topPoint = new Point(driver.manage().window().getSize().getWidth() / 2, 0);
    swipeFromPointToPoint(nearbyChargingLocationsTitle.getLocation(), topPoint);
  }

  public void clickChargeLocation(int index) {
    listChargeLocations.get(index).click();
  }

  public void waitForRendered() {
    waitUntilVisible(btnRecenterIcon, 200);
    waitForPageLoaded(10);
  }

  public List<WebElement> getMapItems(File referenceImage, int cropWidthByPercent, int cropHeightByPercent) {
    //comparing images by cropping it symmetrically, cut cropWidthByPercent portion from the left and right, cut cropWidthByPercent portion from the top and bottom
    int xPercentage = cropWidthByPercent / 2;
    int yPercentage = cropHeightByPercent / 2;
    int widthPercentage = 100 - cropWidthByPercent;
    int heightPercentage = 100 - cropHeightByPercent;
    return getMapItems(referenceImage, xPercentage, yPercentage, widthPercentage, heightPercentage);
  }

  public List<WebElement> getMapItems(File referenceImage, int xPercentage, int yPercentage, int widthPercentage, int heightPercentage) {
    //compares image by extracting it from the start location x,y and by with end height
    waitMilliseconds(4000);
    List<WebElement> likelyItems = new ArrayList<>();
    Dimension refImageDimension = ImageUtils.getDimension(referenceImage);
    double refImageRatio = Math.round(refImageDimension.getWidth() / refImageDimension.getHeight() * 10.0) / 10.0;
    for (WebElement mapItem : listMapItems) {
      //ImageUtils.saveElement(mapItem); //helper function to create reference images
      org.openqa.selenium.Dimension itemSize = mapItem.getSize();
      double elementRatio = Math.round((double) itemSize.getWidth() / itemSize.getHeight() * 10.0) / 10.0;
      if (refImageRatio == elementRatio) {//collecting elements with required width/height only
        likelyItems.add(mapItem);
      }
    }
    return ImageUtils.findMapItems(likelyItems, referenceImage, xPercentage, yPercentage, widthPercentage, heightPercentage);
  }

  public List<WebElement> getChargePoints(String cpType) {
    File chargePointRefImage = new File("src/test/resources/images/" + cpType + "_CP.png");
    List<WebElement> chargePoints = new ArrayList<>();
    chargePoints.addAll(getMapItems(chargePointRefImage, 26, 35));
    return chargePoints;
  }

  public List<WebElement> getFavouritesChargePoints(String cpType) {
    File chargePointFavRefImage = new File("src/test/resources/images/" + cpType + "_CP_favourite.png");
    List<WebElement> chargePoints = new ArrayList<>();
    chargePoints.addAll(getMapItems(chargePointFavRefImage, 35, 40));
    return chargePoints;
  }

  public List<WebElement> getChargePointBubles(String cpType) {
    File refImage = new File("src/test/resources/images/" + cpType + "_bubble.png");
    List<WebElement> chargePoints = new ArrayList<>();
    chargePoints.addAll(getMapItems(refImage, 55, 55));
    return chargePoints;
  }


  public void reRenderMap() {
    // Sometimes  map clusters and CP's cannot be fetched as web elements from the map
    // workaround to get map clusters and CP's is to move the map
    Point start = new Point(0, driver.manage().window().getSize().getHeight() / 2);
    Point end = new Point(0, (driver.manage().window().getSize().getHeight() / 2) + 20);
    this.swipeFromPointToPoint(start, end);
    PageFactory.initElements(new AppiumFieldDecorator(driver), this);
  }
}
