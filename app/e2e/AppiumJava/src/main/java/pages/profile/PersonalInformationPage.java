package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class PersonalInformationPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Personal information']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Personal information']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='EditButton']//*[@text='Edit']")
  @iOSXCUITFindBy(xpath = "")
  private WebElement buttonEdit;

  @AndroidFindBy(xpath = "//*[@resource-id='WidgetContainer']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='WidgetContainer']")
  private WebElement buttonDeleteAccount;

  public PersonalInformationPage(AppiumDriver driver) {
        super(driver);
    }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 3);
    return textPageTitle.getText();
  }

  public void clickDeleteAccount() {
    swipeUp();
    waitMilliseconds(500);
    buttonDeleteAccount.click();
  }

  public void clickEditButton() {
    buttonEdit.click();
  }

  public boolean isEditModeActive() {
    return !isElementPresent(buttonEdit);
  }
}
