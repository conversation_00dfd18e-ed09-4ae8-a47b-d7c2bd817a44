package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class SubscribeAndSavePage extends BasePage {
  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Confirm and authorise']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='ConfirmSubscriptionCTA']")
  private WebElement btnConfirmAndAuthorise;

  @AndroidFindBy(xpath = "//android.view.View[@text='Subscribe and save']")
  @iOSXCUITFindBy(accessibility = "Subscribe and save")
  private WebElement pageSubscribeTitle;

  public SubscribeAndSavePage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitMilliseconds(3000);
    return pageSubscribeTitle.getText();
  }

  public boolean confirmAndAuthoriseIsDisplayed() {
    return btnConfirmAndAuthorise.isDisplayed();
  }

  public void clickConfirmAndAuthorise() {
    btnConfirmAndAuthorise.click();
  }
}
