package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class SettingsPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Header']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Profile']")
  private WebElement textPageTitle;

  @AndroidFindBy(accessibility = "Language - (English)")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[@name=\"Language Language - (English)\"])[3]")
  private WebElement selectedEnglishLanguageButton;

  @AndroidFindBy(accessibility = "Idioma - (Español)")
  @iOSXCUITFindBy(accessibility = "(//XCUIElementTypeOther[@name=\"Idioma Idioma - (Español)\"])[3]")
  private WebElement selectedSpanishLanguageButton;

  @AndroidFindBy(accessibility = "Taal - (Nederlands)")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[@name=\"Taal Taal - (Nederlands)\"])[3]")
  private WebElement selectedDutchLanguageButton;

  @AndroidFindBy(xpath = "//*[@text='Marketing preferences']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther")
  private WebElement buttonMarketingPreferences;

  public SettingsPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitMilliseconds(1000);
    return textPageTitle.getText();
  }

  public void selectedLanguageButtonsIsDisplayed(String country) {
    switch (country.toUpperCase()) {
      case "UK":
        selectedEnglishLanguageButton.isDisplayed();
        break;
      case "NL":
        selectedDutchLanguageButton.isDisplayed();
        break;
      case "ES":
        selectedSpanishLanguageButton.isDisplayed();
        break;
    }
  }

  public void selectedEnglishLanguageButtonIsDisplayed() {
    selectedEnglishLanguageButton.isDisplayed();
  }

  public void clickSelectedEnglishLanguageButton() {
    selectedEnglishLanguageButton.click();
  }

  public void clickMarketingPreferences() {
    buttonMarketingPreferences.click();
  }
}
