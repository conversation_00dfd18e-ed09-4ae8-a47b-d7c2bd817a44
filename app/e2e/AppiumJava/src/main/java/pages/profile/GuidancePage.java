package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class GuidancePage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Guidance']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@label='Guidance']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='next-button']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='next-button']")
  private WebElement buttonNext;

  public GuidancePage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public void clickNext() {
    buttonNext.click();
  }
}
