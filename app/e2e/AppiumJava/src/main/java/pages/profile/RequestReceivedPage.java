package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class RequestReceivedPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Request received']")
  @iOSXCUITFindBy(xpath = "//*[@name='Request received']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='primaryButton']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='primaryButton']")
  private WebElement buttonDone;

  public RequestReceivedPage(AppiumDriver driver) {
        super(driver);
    }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public void clickDone() {
    swipeUp();
    waitMilliseconds(500);
    buttonDone.click();
  }
}
