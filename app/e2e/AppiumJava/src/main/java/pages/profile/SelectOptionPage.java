package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.WebElement;

import java.util.List;

public class SelectOptionPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Select option']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@label='Select option']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='delete-account-options-screen-radio-option-row-radio-button']/parent::*/parent::*")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[@name='radioOptionRow']//parent::*")})
  private List<WebElement> listOptionItems;

  @AndroidFindBy(xpath = "//android.widget.TextView[@index=0]")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@accessible='true']")
  private WebElement textOptionTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='delete-account-options-screen-select-button']")
  @iOSXCUITFindBy(xpath = "//*[@name='delete-account-options-screen-select-button']")
  private WebElement buttonSelect;

  public SelectOptionPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public WebElement selectOption(String value) {
    WebElement foundOption;
    boolean endOfList = false;
    String lastOptionTitle;

    while (!endOfList) {
      foundOption = findOption(value);
      if (foundOption != null) {
        foundOption.click();
        return foundOption;
      }
      lastOptionTitle = getLastOptionTitle();
      swipeFromElementToElement(listOptionItems.get(listOptionItems.size() - 1), listOptionItems.get(0), 300);//swiping up the content
      if (lastOptionTitle.equals(getLastOptionTitle())) { //lastOptionTitle doesn't change that means we are on the bottom of the list
        endOfList = true;
      }
    }
    return null;
  }

  public WebElement findOption(String value) {
    for (WebElement option : listOptionItems) {
      WebElement optionTitle = getChainedElement(option, textOptionTitle);
      if (optionTitle != null && optionTitle.getText().contains(value)) {
        return option;
      }
    }
    return null;
  }

  public String getLastOptionTitle() {
    WebElement lastOptionItem = getChainedElement(listOptionItems.get(listOptionItems.size() - 1), textOptionTitle);
    if (lastOptionItem != null) {
      return lastOptionItem.getText();
    } else {
      return "";
    }
  }

  public void clickSelect() {
    swipeFromElementToElement(listOptionItems.get(listOptionItems.size() - 1), textPageTitle);
    buttonSelect.click();
  }

}
