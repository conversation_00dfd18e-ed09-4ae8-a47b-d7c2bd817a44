package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

public class MarketingPreferencesPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Header']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Profile']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@text='Save preference']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Save preference']")
  private WebElement buttonSave;

  public MarketingPreferencesPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitMilliseconds(2000);
    return textPageTitle.getText();
  }

  public void selectPrefs(String value) {
    driver.findElement(By.xpath("//android.widget.CheckBox[@content-desc='" + value + "']")).click();
  }

  public boolean isPrefsSelected(String value) {
    WebElement checkbox = driver.findElement(By.xpath("//android.widget.TextView[@text='" + value + "']/preceding-sibling::android.widget.CheckBox[@content-desc='" + value + "']"));
    return checkbox.getAttribute("checked").equals("true");
  }

  public void clickSave() {
    buttonSave.click();
    waitUntilInvisible(buttonSave, 10);
  }
}
