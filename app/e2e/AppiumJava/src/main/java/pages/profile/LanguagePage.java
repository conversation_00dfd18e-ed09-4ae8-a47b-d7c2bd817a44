package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class LanguagePage extends BasePage {

  @AndroidFindBy(xpath = "//android.widget.TextView[@text=\"Select app language\"]")
  @iOSXCUITFindBy(accessibility = "Select app language")
  private WebElement selectAppLanguageHeading;

  @AndroidFindBy(accessibility = "Update language")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name=\"Update language\"]")
  private WebElement updateLanguageButton;

  @AndroidFindBy(accessibility = "English (UK)")
  private WebElement englishLanguageButton;

  @AndroidFindBy(accessibility = "Nederlands")
  private WebElement dutchLanguageButton;

  @AndroidFindBy(accessibility = "Español")
  private WebElement spanishLanguageButton;

  public LanguagePage(AppiumDriver driver) {
        super(driver);
    }

  public void clickLanguageButtonIsDisplayedForCountry(String country) {
    switch (country.toUpperCase()) {
      case "NL":
        dutchLanguageButton.click();
        break;
      case "ES":
        spanishLanguageButton.click();
        break;
    }
  }

  public void updateLanguageButtonIsDisplayed() {
    updateLanguageButton.isDisplayed();
  }

  public void clickUpdateLanguageButton() {
    updateLanguageButton.click();
  }

  public void englishLanguageButtonIsDisplayed() {
    englishLanguageButton.isDisplayed();
  }
}
