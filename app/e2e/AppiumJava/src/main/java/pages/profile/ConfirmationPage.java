package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.WebElement;

import java.util.List;

public class ConfirmationPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Confirm deletion']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Confirm deletion']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//*[contains(@resource-id, 'reason')]")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[contains(@name, 'reason')]")})
  private List<WebElement> listReasons;

  @AndroidFindBy(xpath = "//android.widget.TextView")
  private WebElement textReason;

  @AndroidFindBy(xpath = "//android.widget.RadioButton/android.view.ViewGroup")
  private WebElement radioReason;

  @AndroidFindBy(xpath = "//*[@resource-id='confirmCheckbox']")
  @iOSXCUITFindBy(xpath = "//*[@name='confirmCheckbox']")
  private WebElement checkBoxUnderstand;

  @AndroidFindBy(xpath = "//*[@resource-id='deletionButton']")
  @iOSXCUITFindBy(xpath = "//*[@name='deletionButton']")
  private WebElement buttonConfirmDeletion;

  public ConfirmationPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    waitUntilVisible(textPageTitle, 3);
    return textPageTitle.getText();
  }

  public WebElement selectReason(String name) {
    for (WebElement reason : listReasons) {
      String reasonName;
      if (isAndroid) {
        reasonName = getChainedElement(reason, textReason).getText();
      } else {
        reasonName = reason.getText();
      }
      if (reasonName.contains(name)) {
        reason.click();
        return reason;
      }
    }
    return null;
  }

  public boolean isReasonSelected(WebElement reason) {
    return isElementPresent(getChainedElement(reason, radioReason));
  }

  public void checkUnderstandConsequences() {
    checkBoxUnderstand.click();
  }

  public boolean isUnderstandChecked() {
    if (isAndroid) {
      return checkBoxUnderstand.getAttribute("checked").equals("true");
    } else {
      return checkBoxUnderstand.getAttribute("value").equals("checkbox, checked");
    }
  }

  public boolean isConfirmDeletionEnabled() {
    return waitForElementAttributeToBe(buttonConfirmDeletion, "enabled", "true", 15);
  }

  public boolean isConfirmDeletionDisabled() {
    return waitForElementAttributeToBe(buttonConfirmDeletion, "enabled", "false", 10);
  }

  public void clickConfirmDeletion(){
    buttonConfirmDeletion.click();
  }

}
