package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class BeforeYouGoPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Before you go']")
  @iOSXCUITFindBy(xpath = "//*[@name='Before you go']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='delete-my-account-and-data-button']")
  @iOSXCUITFindBy(xpath = "//*[@name='delete-my-account-and-data-button']")
  private WebElement buttonDeleteAccountData;

  public BeforeYouGoPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public void clickDeleteAccount() {
    buttonDeleteAccountData.click();
  }
}
