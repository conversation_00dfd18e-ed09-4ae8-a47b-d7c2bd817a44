package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class DeleteAccountPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Delete account']")
  @iOSXCUITFindBy(xpath = "//*[@name='Delete account']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='delete-account-button']")
  @iOSXCUITFindBy(xpath = "//*[@name='delete-account-button']")
  private WebElement buttonDeleteAccount;

  public DeleteAccountPage(AppiumDriver driver) {
    super(driver);
  }

  public String getPageTitle() {
    return textPageTitle.getText();
  }

  public void clickDeleteAccount() {
    buttonDeleteAccount.click();
  }
}
