package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import static org.testng.Assert.assertFalse;

public class ProfilePage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='Header']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Profile']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//android.widget.TextView[contains(@text, 'Hi')]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeScrollView//XCUIElementTypeOther[contains(@name, 'Hi ')])[2]")
  private WebElement textHiUserFirstName;

  @AndroidFindBy(xpath = "//android.widget.Button//*[starts-with(@text,'Log in')]")
  @iOSXCUITFindBy(xpath = "(//XCUIElementTypeOther[starts-with(@name, 'Log in')])[last()]")
  private WebElement btnLogin;

  @AndroidFindBy(xpath = "//android.widget.Button[@resource-id='register']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='register']")
  private WebElement buttonCreateAccount;

  @AndroidFindBy(accessibility = "Recent transactions")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='ChargeActivity']")
  private WebElement btnRecentTransactions;

  @AndroidFindBy(xpath = "//*[@resource-id='UserDetails']/android.widget.TextView")
  @iOSXCUITFindBy(accessibility = "Personal information")
  private WebElement btnPersonalInformation;

  @AndroidFindBy(accessibility = "Settings")
  @iOSXCUITFindBy(accessibility = "Settings")
  private WebElement btnSettings;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='Payment methods']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='PaymentDetails']")
  private WebElement btnPaymentDetails;

  @AndroidFindBy(accessibility = "Subscribe and save")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Subscription']")
  private WebElement btnSubscribe;

  @AndroidFindBy(accessibility = "My Subscription")
  @iOSXCUITFindBy(accessibility = "My Subscription")
  private WebElement btnMySubscription;

  @AndroidFindBy(accessibility = "Log out")
  @iOSXCUITFindBy(xpath = "(//*[contains(@name,'Log out')])[last()]")
  private WebElement btnLogout;

  @AndroidFindBy(accessibility = "Offers and promotions")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Offers']")
  private WebElement btnOffersAndPromotions;

  @AndroidFindBy(accessibility = "NEW")
  @iOSXCUITFindBy(accessibility = "NEW")
  private WebElement widgetNewOffersAndPromotions;

  public ProfilePage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitMilliseconds(3000);
    return textPageTitle.getText();
  }

  public String profilePageIsDisplayed() {
    return btnPersonalInformation.getText();
  }

  public void clickSettingsButton() {
    btnSettings.click();
  }

  public void profilePageIsLoggedIn() {
    btnLogout.isDisplayed();
  }

  public boolean mySubscriptionIsDisplayed(){
    return btnMySubscription.isDisplayed();
  }

  public boolean mySubscriptionIsNotDisplayed(){
    return isElementPresent(btnMySubscription);
  }

  public void clickMySubscription(){
    btnMySubscription.click();
  }

  public boolean loginToCreateAnAccountButtonIsDisplayed() {
    return isElementPresent(btnLogin, 5);
  }

  public boolean offersAndPromotionsButtonIsDisplayed() {
    return isElementPresent(btnOffersAndPromotions, 5);
  }

  public void clickOffersAndPromotionsButton() {
    btnOffersAndPromotions.click();
  }

  public boolean offersAndPromotionsNewWidgetIsDisplayed() {
    return isElementPresent(widgetNewOffersAndPromotions, 5);
  }

  public void clickLoginButton() {
    if (isAndroid) {
      waitUntilVisible(btnLogin, 20);
      waitMilliseconds(2000);
      btnLogin.click();
    } else {
      // Cannot identify selector for the button. It is located at 60% of 'Log in or create an
      // account' element
      int y = (int) (btnLogin.getLocation().getY() + btnLogin.getSize().getHeight() * 0.6);
      int x = btnLogin.getSize().getWidth() / 2; // middle of the button
      tapCoordinates(x, y);
    }
  }

  public void clickCreateAccount() {
    if (isAndroid) {
      buttonCreateAccount.click();
    } else {
      // Cannot identify selector for the button. It is located at 82% of 'Log in or create an
      // account' element
      int y = (int) (btnLogin.getLocation().getY() + btnLogin.getSize().getHeight() * 0.82);
      int x = btnLogin.getSize().getWidth() / 2; // middle of the button
      tapCoordinates(x, y);
    }
  }

  public void clickLogOut() {
    btnLogout.click();
  }

  public void clickPaymentDetails() {
    btnPaymentDetails.click();
  }

  public void clickRecentTransactions() {
    waitUntilClickable(btnRecentTransactions, 2).click();
  }

  public void clickPersonalInformation() {
    btnPersonalInformation.click();
  }

  public String getTextUserFirstName() {
    return textHiUserFirstName.getText();
  }

  public boolean subscribeAndSaveOptionIsDisplayed() {
    return btnSubscribe.isDisplayed();
  }

  public void subscribeAndSaveOptionIsNotDisplayed() {
    assertFalse(isElementPresent(btnSubscribe), "Subscribe button is not present on the page");
  }

  public void clickSubscribeAndSave() {
    btnSubscribe.click();
  }
}
