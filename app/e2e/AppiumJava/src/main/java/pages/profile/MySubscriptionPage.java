package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import static org.testng.Assert.assertFalse;

public class MySubscriptionPage extends BasePage {

  @AndroidFindBy(xpath = "//android.view.View[@text='My subscription']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='My subscription']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='Cancel gocard account']")
  @iOSXCUITFindBy(accessibility = "Cancel gocard account")
  private WebElement btnGoCardAcc;

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='Finish']")
  @iOSXCUITFindBy(accessibility = "Finish")
  private WebElement btnFinish;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Set up my subscription now']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Set up gocard account']")
  private WebElement btnSetSubNow;

  @AndroidFindBy(xpath = "//android.view.View[@text='Confirm']")
  @iOSXCUITFindBy(accessibility = "//XCUIElementTypeOther[@name='Confirm']")
  private WebElement pageConfirmTitle;

  public MySubscriptionPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitMilliseconds(3000);
    return textPageTitle.getText();
  }

  public boolean btnFinishIsDisplayed() {
    return btnFinish.isDisplayed();
  }

  public void clickFinish() {
    btnFinish.click();
  }

  public boolean setUpSubNowIsDisplayed() {
    return btnSetSubNow.isDisplayed();
  }

  public void clickSetUpSubNow() {
    btnSetSubNow.click();
  }

  public boolean confirmPageIsDispayed() {
    return pageConfirmTitle.isDisplayed();
  }
}
