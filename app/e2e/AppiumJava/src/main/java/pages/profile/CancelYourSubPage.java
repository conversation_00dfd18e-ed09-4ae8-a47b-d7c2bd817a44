package pages.profile;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;

public class CancelYourSubPage  extends BasePage {
  @AndroidFindBy(xpath = "//android.view.View[@text='Cancel your subscription']")
  private WebElement pageCancelTitle;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Cancel my subscription']")
  private WebElement btnCancelMySub;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Lose my discount']")
  private WebElement btnLooseDiscount;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Are you sure you want to cancel?']")
  private WebElement confirmationDialog;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Cancel your subscription']")
  private WebElement btnCancelSubscription;

  @AndroidFindBy(xpath = "//android.widget.CheckBox[@content-desc='Tick this box to confirm and cancel your bp pulse subscription. (Active until {{activeUntil}})']")
  private WebElement cbConfirm;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Are you sure?']")
  private WebElement secConfirmDialog;

  public CancelYourSubPage(AppiumDriver driver) {
    super(driver);
  }

  public boolean btnCancelMySubIsEnabled() {
    return btnCancelMySub.isEnabled();
  }

  public void clickCancelMySub() {
    btnCancelMySub.click();
  }

  public String getCancelPageTitle() {
    return pageCancelTitle.getText();
  }

  public boolean btnCancelMySubIsDisplayed() {
    return btnCancelMySub.isDisplayed();
  }

  public boolean cancelSubscriptionIsDisplayed() {
    swipeUp();
    waitUntilVisible(btnCancelSubscription, 20);
    return btnCancelSubscription.isDisplayed();
  }

  public void clickCancelSubscription() {
    if (isAndroid) {
      btnCancelSubscription.click();
    } else {
      int y = (int) (btnCancelSubscription.getLocation().getY() + btnCancelSubscription.getSize().getHeight() * 0.6);
      int x = btnCancelSubscription.getSize().getWidth() / 2; // middle of the button
      tapCoordinates(x, y);
    }
  }

  public boolean questionDialogIsDisplayed() {
    return confirmationDialog.isDisplayed();
  }

  public boolean btnLooseDiscountIsDisplayed() {
    return btnLooseDiscount.isDisplayed();
  }

  public void clickLooseDiscount() {
    btnLooseDiscount.click();
  }

  public boolean checkBoxConfirmIsDisplayed() {
    return cbConfirm.isDisplayed();
  }

  public void selectCbConfirm() {
    cbConfirm.click();
  }

  public boolean secondConfirmDialogIsDisplayed() {
    return secConfirmDialog.isDisplayed();
  }

}
