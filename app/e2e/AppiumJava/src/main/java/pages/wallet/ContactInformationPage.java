package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class ContactInformationPage extends BasePage {

  @FindBy(xpath = "//div[text()='Contact information']")
  @iOSXCUITFindBy(xpath = "//*[@name='Contact information']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "'//*[@resource-id='phone-prefix-select']/android.widget.Image")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Microsite bppay wallet']/XCUIElementTypeImage")
  private WebElement iconDropDownPhonePrefix;

  @AndroidFindBy(xpath = "//*[@resource-id='phone-prefix-select-value']/android.widget.TextView | //*[@resource-id='phone-prefix-select-value']/android.view.View")
  @iOSXCUITFindBy(xpath = "//*[@name='phone-prefix-select-valu']")
  private WebElement textPhonePrefix;

  @FindBy(xpath = "//*[@data-testid='phone_number']")
  @iOSXCUITFindBy(xpath = "//*[@name='phone_number']/XCUIElementTypeTextField")
  private WebElement inputPhoneNumber;

  @FindBy(xpath = "//*[@data-testid='email']")
  @iOSXCUITFindBy(xpath = "//*[@name='email']//XCUIElementTypeTextField")
  private WebElement inputEmail;

  @FindBy(xpath = "//*[@data-testid='ContactInfo-button']")
  @iOSXCUITFindBy(xpath = "//*[@name='loading']")
  private WebElement buttonSave;

  @FindBy(xpath = "//android.widget.Button[@text='Save']")
  private WebElement buttonSaveToPhone;

  public ContactInformationPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    swipeDown();
    return textPageTitle.getText();
  }

  public void selectPhonePrefix(String value) {
    iconDropDownPhonePrefix.click();
  }

  public String getPhonePrefix() {
    return textPhonePrefix.getText();
  }

  public void enterPhoneNumber(String value) {
    inputPhoneNumber.sendKeys(value);
  }

  public String getPhoneNumber() {
    return inputPhoneNumber.getDomProperty("value");
  }

  public void enterEmail(String value) {
    inputEmail.sendKeys(value);
  }

  public String getEmail() {
    return inputEmail.getDomProperty("value");
  }

  public void clickSave() {
    swipeUp();
    buttonSave.click();
  }

  public void closeSaveCardModal() {
    if (isElementPresent(buttonSaveToPhone, 3000)) {
      buttonSaveToPhone.click();
    }
  }

}
