package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class AddYourPaymentCardPage extends BasePage {

  @AndroidFindBy(xpath = "//android.view.View[@text='Add your payment card']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Add your payment card' and @accessible='true']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='OutstandingAddCard_Button']")
  @iOSXCUITFindBy(xpath = "//*[@name='OutstandingAddCard_Button']")
  private WebElement buttonAddPaymentCard;

  public AddYourPaymentCardPage(AppiumDriver driver) {
        super(driver);
    }

  public String getTextPageTitle() {
    return textPageTitle.getText();
  }

  public void clickAddPaymentCard() {
    buttonAddPaymentCard.click();
  }

}
