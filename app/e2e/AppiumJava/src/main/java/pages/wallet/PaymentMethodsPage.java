package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.Point;
import org.openqa.selenium.WebElement;

import java.util.ArrayList;
import java.util.List;

public class PaymentMethodsPage extends BasePage {

  @AndroidFindBy(xpath = "//android.widget.Button[@content-desc='Previous Screen']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='BackPress']")
  private WebElement iconBack;

  @AndroidFindBy(xpath = "//android.view.View[@text='Payment methods']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Payment methods' and @accessible='true']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='linkedCardItem']")})
  @iOSXCUITFindBys({@iOSXCUITBy(xpath = "//*[@name='linkedCardItem']")})
  private List<WebElement> listLinkedCards;

  @AndroidFindBy(xpath = "(//com.horcrux.svg.PathView) [last()]")
  @iOSXCUITFindBy(xpath = "/XCUIElementTypeOther[1]")
  private WebElement imageCardBrand;

  @AndroidFindBy(xpath = "//android.widget.TextView[contains(@text,'...')]")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[contains(@value,'...')]")
  private WebElement textCardBrandAndLastFour;

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='DEFAULT ']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='DEFAULT ']")
  private WebElement textDefault;

  @AndroidFindBy(xpath = "//*[@resource-id='PaymentWebView button']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//*[@name='PaymentWebView button']")
  private WebElement buttonAddNewCard;

  @AndroidFindBy(xpath = "//*[@resource-id='buttonDefaultCard']")
  @iOSXCUITFindBy(xpath = "//*[@name='buttonDefaultCard']")
  private WebElement buttonDefaultCard;

  @AndroidFindBy(xpath = "//*[@text='Check your internet connection and try again.']")
  private WebElement textCheckInternet;

  public PaymentMethodsPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 60);
    return textPageTitle.getText();
  }

  public void backToPreviousScreen() {
    iconBack.click();
  }

  public void clickAddNewCard() {
    buttonAddNewCard.click();
  }

  public List<WebElement> getListLinkedCards() {
    return listLinkedCards;
  }

  public List<String> getCardNames() {
    List<String> cardNames = new ArrayList<>();
    if (!buttonAddNewCard.getText().equals("Add a new credit or debit card")) {   //the list is empty
      for (WebElement card : listLinkedCards) {
        cardNames.add(getChainedElement(card, textCardBrandAndLastFour).getText());
      }
    }
    return cardNames;
  }

  public String getDefaultCard() {
    if (listLinkedCards.size() > 0) {
      return getCardBrandAndLastFour(listLinkedCards.get(0));
    } else {
      return "";
    }
  }

  public boolean isCardPresent(String textCardBrandAndLastFour) {
    List<String> cardNames = getCardNames();
    for (String card : cardNames) {
      if (card.equals(textCardBrandAndLastFour)) {
        return true;
      }
    }
    return false;
  }

  public boolean isCardFirstAndDefault(String nameAndLastFour) {
    if (getChainedElement(listLinkedCards.get(0), textCardBrandAndLastFour).getText().equals(nameAndLastFour)) {
      return getChainedElement(listLinkedCards.get(0), textDefault).isDisplayed();
    }
    return false;
  }

  public WebElement getLinkedCard(String nameAndLastFour) {
    waitUntilVisible(buttonAddNewCard, 10);
    for (WebElement card : listLinkedCards) {
      if (getChainedElement(card, textCardBrandAndLastFour).getText().contains(nameAndLastFour)) {
        return card;
      }
    }
    return null;
  }

  public String getCardBrandAndLastFour(WebElement card) {
    return getChainedElement(card, textCardBrandAndLastFour).getText();
  }

  public void deleteCard(String brandAndLastFour) {
    for (WebElement card : listLinkedCards) {
      WebElement name = getChainedElement(card, textCardBrandAndLastFour);
      if (name.getText().equals(brandAndLastFour)) {
        Point from = new Point(textDefault.getLocation().getX(), card.getLocation().getY() + (card.getSize().getHeight() / 2));
        Point to = getChainedElement(card, imageCardBrand).getLocation();
        swipeFromPointToPoint(from, to, 500); //swipe from the end of the 'textCardBrandAndLastFour' to the begining of the card item
        break;
      }
    }
  }

  public void clickDefaultCard() {
    buttonDefaultCard.click();
  }

  public String getTextCheckInternet() {
    waitUntilVisible(textCheckInternet, 5);
    return textCheckInternet.getText();
  }

  public boolean isCheckInternetInvisible() {
    return waitUntilInvisible(textCheckInternet, 20);
  }
}
