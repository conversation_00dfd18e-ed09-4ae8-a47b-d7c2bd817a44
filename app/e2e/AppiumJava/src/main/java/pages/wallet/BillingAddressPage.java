package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class BillingAddressPage extends BasePage {

  @FindBy(xpath = "//div[text()='Billing address']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Billing address']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='street_address']")
  @iOSXCUITFindBy(xpath = "//*[@name='street_address']//XCUIElementTypeTextField")
  private WebElement inputStreetName;

  @FindBy(xpath = "//*[@data-testid='house_number']")
  @iOSXCUITFindBy(xpath = "//*[@name='house_number']//XCUIElementTypeTextField")
  private WebElement inputHouseNumber;

  @FindBy(xpath = "//*[@data-testid='postcode']")
  @iOSXCUITFindBy(xpath = "//*[@name='postcode']//XCUIElementTypeTextField")
  private WebElement inputPostalCode;

  @FindBy(xpath = "//*[@data-testid='town']")
  @iOSXCUITFindBy(xpath = "//*[@name='town']//XCUIElementTypeTextField")
  private WebElement inputTown;

  @FindBy(xpath = "//*[@data-testid='country']")
  @iOSXCUITFindBy(xpath = "//*[@name='country']//XCUIElementTypeTextField")
  private WebElement selectCountry;

  @FindBy(xpath = "//*[@data-testid='Billinginfo-button']")
  @iOSXCUITFindBy(xpath = "//*[@name='Save billing address and continue']")
  private WebElement buttonSave;

  public BillingAddressPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    return textPageTitle.getText();
  }

  public void enterStreetName(String value) {
    inputStreetName.sendKeys(value);
  }

  public String getStreetName() {
    return inputStreetName.getDomProperty("value");
  }

  public void enterHouseNumber(String value) {
    inputHouseNumber.sendKeys(value);
  }

  public String getHouseNumber() {
    return inputHouseNumber.getDomProperty("value");
  }

  public void enterPostalCode(String value) {
    inputPostalCode.sendKeys(value);
  }

  public String getPostalCode() {
    return inputPostalCode.getDomProperty("value");
  }

  public void enterTown(String value) {
    inputTown.sendKeys(value);
  }

  public String getTown() {
    waitMilliseconds(100);
    return inputTown.getDomProperty("value");
  }

  public String getCountryCode() {
    return selectCountry.getDomProperty("value");
  }

  public void clickSave() {
    tapCoordinates(1, driver.manage().window().getSize().getHeight() / 2);// unfocus input
    buttonSave.click();
  }

}
