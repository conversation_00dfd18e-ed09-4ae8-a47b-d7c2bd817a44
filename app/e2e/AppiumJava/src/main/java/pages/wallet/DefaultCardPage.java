package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.AndroidFindBys;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import java.util.List;

public class DefaultCardPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Default card']")
  @iOSXCUITFindBy(xpath = "//*[@name='Default card' and @accessible='true']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='defaultCardItem']")})
  @iOSXCUITFindBy(xpath = "//*[@name='defaultCardItem']")
  private List<WebElement> listDefaultCards;

  @AndroidFindBy(xpath = "//android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//*[@name='Default card' and @accessible='true']")
  private WebElement textCardNameNumber;

  @AndroidFindBy(xpath = "//*[@resource-id='SaveDefault_Button']")
  @iOSXCUITFindBy(xpath = "//*[@name='SaveDefault_Button']")
  private WebElement buttonSave;

  @AndroidFindBy(xpath = "//*[@resource-id='SaveDefault_Button']")
  @iOSXCUITFindBy(xpath = "//*[@name='SaveDefault_Button']")
  private WebElement buttonCancel;

  public DefaultCardPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitMilliseconds(1000);
    return textPageTitle.getText();
  }

  public void selectDefaultCard(String nameAndLastFour) {
    for (WebElement cardItem : listDefaultCards) {
      if (getChainedElement(cardItem, textCardNameNumber).getText().equals(nameAndLastFour)) {
        cardItem.click();
        return;
      }
    }
  }

  public void clickCancel() {
    buttonCancel.click();
  }

  public void clickSave() {
    buttonSave.click();
  }

  public boolean isSaveButtonEnabled() {
    return waitForElementAttributeToBe(buttonSave, "enabled", "true", 15);
  }

  public boolean isSaveButtonDisabled() {
    return waitForElementAttributeToBe(buttonSave, "enabled", "false", 5);
  }

}
