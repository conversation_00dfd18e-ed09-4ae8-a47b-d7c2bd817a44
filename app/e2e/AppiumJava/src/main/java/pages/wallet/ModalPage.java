package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

public class ModalPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@content-desc='alert-title']")
  @iOSXCUITFindBy(xpath = "//*[@name='alert-title']")
  private WebElement textPageTitle;

  public ModalPage(AppiumDriver driver) {
    super(driver);
  }

  public WebElement getTextPageTitle() {
    return textPageTitle;
  }

  public ModalPage(AppiumDriver driver, String title, long seconds) {
    super(driver);
    By by = isAndroid ?
      By.xpath("//*[@text='" + title + "']") :
      By.xpath("//*[@label='" + title + "' and @accessible='true']");
    waitUntilVisible(by, seconds);
  }

  public ModalPage(AppiumDriver driver, String title) {
    this(driver, title, 3);
  }

  public void clickButton(String buttonName) {
    By by = isAndroid ?
      By.xpath("//*[@content-desc='" + buttonName + "' or @text='" + buttonName + "']") :
      By.xpath("//*[@label='" + buttonName + "' and @visible='true']");
    driver.findElement(by).click();
  }

  public boolean isDisplayed(String title) {
    return isElementPresent(By.xpath("//*[contains(@text, '" + title + "')]"));
  }

}
