package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class AddYourPaymentMethodPage extends BasePage {

  @FindBy(xpath = "//*[text()='Add your payment method']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[contains(@name, 'spreedly-number-frame')]")
  private WebElement iframeSpreadly;

  @FindBy(xpath = "//*[@id='card_number']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeTextField[@name='Enter your card number'] | //XCUIElementTypeTextField[@name='Card Number']") // Bug 7455311
  private WebElement inputCardNumber;

  @FindBy(xpath = "//*[@data-testid='listbox-button-month']")
  private WebElement inputMonth;

  @FindBy(xpath = "//*[@data-testid='listbox-button-year']")
  private WebElement inputYear;

  @FindBy(xpath = "//*[contains(@name, 'spreedly-cvv-frame')]")
  private WebElement iframeCvv;

  @FindBy(xpath = "//*[@id='cvv']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeTextField[@name='Enter the security code on the back of your card']")
  private WebElement inputCvv;

  @FindBy(xpath = "//*[@name='cardHoldersName']")
  private WebElement inputCardHolderName;

  @FindBy(xpath = "//*[@name='addressLine1']")
  private WebElement inputStreetHouse;

  @FindBy(xpath = "//*[@name='city']")
  private WebElement inputTown;

  @FindBy(xpath = "//*[@name='postalCode']")
  private WebElement inputPostalCode;

  @FindBy(xpath = "//*[@data-testid='listbox-button-country']//span[contains(@class,'truncate')]")
  private WebElement optionCountry;

  @FindBy(xpath = "//*[@data-testid='listbox-button-dialCode']//span[contains(@class,'truncate')]")
  private WebElement optionPhonePrefix;

  @FindBy(xpath = "//*[@type='search']")
  private WebElement inputSearchPhonePrefix;

  @FindBy(xpath = "//*[@name='phoneNumber']")
  private WebElement inputPhoneNumber;

  @FindBy(xpath = "//*[@name='email']")
  private WebElement inputEmail;

  @FindBy(xpath = "//*[@data-testid='Button']")
  private WebElement buttonContinue;

  public AddYourPaymentMethodPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    return textPageTitle.getText();
  }

  public void enterCardNumber(String value) {
    if (isAndroid) {
      driver.switchTo().frame(iframeSpreadly);
      inputCardNumber.sendKeys(value);
    } else {//Not able to switch to an iframe on iOS due to "Original error: Blocked a frame with origin".
      switchContext("NATIVE_APP");
      inputCardNumber.sendKeys(value);
    }
  }

  public String getCardNumber() {
    String card;
    if (isAndroid) {
      card = inputCardNumber.getDomProperty("value");
      driver.switchTo().defaultContent();
    } else {
      switchContext("NATIVE_APP");
      card = inputCardNumber.getText();
      switchContext("WEBVIEW");
    }
    return card;
  }

  public void enterMonth(String value) {
    inputMonth.click();
    if (Integer.parseInt(value) > 6) {
      swipeFromElementToElement(getMonthDropdown("06"), inputMonth, 1000);
    }
    getMonthDropdown(value).click();
  }

  public String getMonth() {
    return inputMonth.getText();
  }

  public WebElement getMonthDropdown(String value) {
    return driver.findElement(By.xpath("//*[@id='list-option-month-" + value + "']"));
  }

  public void enterYear(String value) {
    inputYear.click();
    if (Integer.parseInt(value) > 30) {
      swipeFromElementToElement(getYearDropdown("29"), inputYear, 1000);
    }
    getYearDropdown(value).click();
  }

  public WebElement getYearDropdown(String value) {
    return driver.findElement(By.xpath("//*[@id='list-option-year-20" + value + "']"));
  }

  public String getYear() {
    return inputYear.getText();
  }

  public void enterCvv(String value) {
    if (isAndroid) {
      driver.switchTo().frame(iframeCvv);
    } else { //Not able to switch to an iframe on iOS due to "Original error: Blocked a frame with origin".
      switchContext("NATIVE_APP");
    }
    inputCvv.sendKeys(value);
  }

  public String getCvv() {
    String cvv;
    if (isAndroid) {
      cvv = inputCvv.getDomProperty("value");
      driver.switchTo().defaultContent();
    } else {
      switchContext("NATIVE_APP");
      hideKeyboard();
      cvv = inputCvv.getText();
      switchContext("WEBVIEW");
    }
    return cvv;
  }

  public void enterCardHolderName(String value) {
    swipeUp();
    inputCardHolderName.sendKeys(value);
  }

  public String getCardHolderName() {
    return inputCardHolderName.getAttribute("value");
  }

  public void enterStreetHouse(String value) {
    inputStreetHouse.sendKeys(value);
  }

  public String getStreetHouse() {
    return inputStreetHouse.getAttribute("value");
  }

  public void enterTown(String value) {
    inputTown.sendKeys(value);
  }

  public String getTown() {
    return inputTown.getAttribute("value");
  }

  public void enterPostalOrZipCode(String value) {
    inputPostalCode.sendKeys(value);
  }

  public String getPostalOrZipCode() {
    return inputPostalCode.getAttribute("value");
  }

  public String getCountry() {
    return optionCountry.getText();
  }

  public String getPhonePrefix() {
    return optionPhonePrefix.getText();
  }

  public void selectPhonePrefix(String value) {
    swipeUp();
    unfocusInput(inputPostalCode);
    optionPhonePrefix.click();
    inputSearchPhonePrefix.sendKeys(value);
    driver.findElement(By.xpath("//*[contains(@id,'list-option-')]//span[contains(text() ,'(" + value + ")')]")).click();
  }

  public void enterMobileNumber(String value) {
    swipeUp();
    inputPhoneNumber.sendKeys(value);
  }

  public String getMobileNumber() {
    return inputPhoneNumber.getAttribute("value");
  }

  public void enterEmailAddress(String value) {
    inputEmail.sendKeys(value);
    unfocusInput(inputEmail);
  }

  public String getEmailAddress() {
    return inputEmail.getAttribute("value");
  }

  public void clickContinue() {
    swipeUp();
    waitMilliseconds(500);
    buttonContinue.click();
  }
}

