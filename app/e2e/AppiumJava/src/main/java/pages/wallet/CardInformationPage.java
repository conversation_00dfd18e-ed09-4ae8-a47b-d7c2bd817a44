package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class CardInformationPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Card information']")
  @iOSXCUITFindBy(xpath = "//*[@name='Card information']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@text='full_name']//android.widget.EditText")
  @iOSXCUITFindBy(xpath = "//*[@name='full_name']//XCUIElementTypeTextField")
  private WebElement inputCardHolderName;

  @AndroidFindBy(xpath = "//*[@text='inputCardNumber']//android.widget.EditText")
  @iOSXCUITFindBy(xpath = "//*[@name='inputCardNumber']//XCUIElementTypeTextField")
  private WebElement inputCardNumber;

  @AndroidFindBy(xpath = "//*[@text='inputCvv']//android.widget.EditText")
  @iOSXCUITFindBy(xpath = "//*[@name='inputCvv']//XCUIElementTypeTextField")
  private WebElement inputCvv;

  @AndroidFindBy(xpath = "//*[@text='expire_month']//android.widget.EditText")
  @iOSXCUITFindBy(xpath = "//*[@name='expire_month']//XCUIElementTypeTextField")
  private WebElement inputMonth;

  @AndroidFindBy(xpath = "//*[@text='expire_year']//android.widget.EditText")
  @iOSXCUITFindBy(xpath = "//*[@name='expire_year']//XCUIElementTypeTextField")
  private WebElement inputYear;

  @AndroidFindBy(xpath = "//*[@text='defaultPayment']")
  @iOSXCUITFindBy(xpath = "//*[@name='defaultPayment']")
  private WebElement checkboxDefaultPaymentMethod;

  @AndroidFindBy(xpath = "//*[@text='defaultPayment']//android.widget.Image")
  private WebElement checkboxDefaultChecked;

  @AndroidFindBy(xpath = "//*[@text='Save and add billing address']")
  @iOSXCUITFindBy(xpath = "//*[@name='Save and add billing address']")
  private WebElement buttonSave;

  public CardInformationPage(AppiumDriver driver) {
        super(driver);
    }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 40);
    return textPageTitle.getText();
  }

  public void enterCardHolderName(String value) {
    inputCardHolderName.sendKeys(value);
  }

  public String getCardHolderName() {
    return inputCardHolderName.getText();
  }

  public void enterCardNumber(String value) {
    inputCardNumber.sendKeys(value);
  }

  public String getCardNumber() {
    return inputCardNumber.getText();
  }

  public void enterCvv(String value) {
    inputCvv.sendKeys(value);
  }

  public String getCvv() {
    return inputCvv.getText();
  }

  public void enterMonth(String value) {
    inputMonth.sendKeys(value);
  }

  public String getMonth() {
    return inputMonth.getText();
  }

  public void enterYear(String value) {
    inputYear.sendKeys(value);
  }

  public String getYear() {
    return inputYear.getText();
  }

  public void setDefault() {
    checkboxDefaultPaymentMethod.click();
  }

  public boolean isCheckboxDefaultChecked() {
    return checkboxDefaultChecked.isDisplayed();
  }

  public void clickSave() {
    swipeFromElementToElement(inputYear, textPageTitle);
    buttonSave.click();
    if (isAndroid) {
      buttonSave.click();//to do, investigate whether report a bug
    }
  }
}

