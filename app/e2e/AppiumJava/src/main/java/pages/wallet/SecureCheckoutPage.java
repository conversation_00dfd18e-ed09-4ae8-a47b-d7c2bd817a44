package pages.wallet;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class SecureCheckoutPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='SECURE CHECKOUT']")
  @iOSXCUITFindBy(xpath = "//*[@name='Secure Checkout']/XCUIElementTypeStaticText")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[contains(@resource-id,'challengeInfoTextView')]")
  @iOSXCUITFindBy(xpath = "//*[@name='challengeInfoText']")
  private WebElement textInfo;

  @AndroidFindBy(xpath = "//*[contains(@resource-id, 'codeEditTextField')]")
  @iOSXCUITFindBy(xpath = "//*[@name='challengeDataEntry']")
  private WebElement inputCode;

  @AndroidFindBy(xpath = "//*[contains(@resource-id, 'submitAuthenticationButton')]")
  @iOSXCUITFindBy(xpath = "//*[@name='SUBMIT']")
  private WebElement buttonSubmit;

  public SecureCheckoutPage(AppiumDriver driver) {
        super(driver);
    }

  public String getTextTitle() {
    waitUntilVisible(textPageTitle, 40);
    return textPageTitle.getText();
  }

  public String getTextInfo() {
    waitUntilVisible(textInfo, 40);
    return textInfo.getText();
  }

  public void enterCode(String value) {
    inputCode.click();
    if (isAndroid) { //unable to input by sendKeys()
      pressKeys(value);
    } else {
      inputCode.sendKeys(value);
    }
  }

  public String getCode() {
    return inputCode.getText();
  }

  public void clickSubmit() {
    buttonSubmit.click();
  }

}
