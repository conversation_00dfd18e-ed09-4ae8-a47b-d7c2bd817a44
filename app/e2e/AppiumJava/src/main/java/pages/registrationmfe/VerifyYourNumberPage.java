package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.AndroidFindBys;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

import java.util.List;

public class VerifyYourNumberPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Verify your number']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Verify your number']")
  private WebElement textPageTitle;

  @AndroidFindBys({@AndroidBy(xpath = "//android.widget.EditText")})
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeTextField[contains(@name,'Please enter OTP character')]")
  private List<WebElement> inputVerificationCode;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='chevron-right']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Continue']")
  private WebElement buttonContinue;

  public VerifyYourNumberPage(AppiumDriver driver) {
        super(driver);
    }

  public void enterVerificationCode(String value) {
    for (int i = 0; i < value.length() && i < inputVerificationCode.size(); i++) {
      inputVerificationCode.get(i).click();
      inputVerificationCode.get(i).sendKeys(String.valueOf(value.charAt(i)));
    }
  }

  public String getVerificationCode() {
    StringBuilder sb = new StringBuilder();
    for (WebElement input : inputVerificationCode) {
      sb.append(input.getText());
    }
    return sb.toString();
  }

  public void clickContinue() {
    buttonContinue.click();
  }
}
