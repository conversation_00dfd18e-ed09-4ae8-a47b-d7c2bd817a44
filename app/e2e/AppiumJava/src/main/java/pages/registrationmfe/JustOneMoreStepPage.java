package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class JustOneMoreStepPage extends BrowserPage {

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Just one more step']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Just one more step']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='Continue']")
  @iOSXCUITFindBy(xpath = "//*[@name='Continue']")
  private WebElement buttonContinue;

  public JustOneMoreStepPage(AppiumDriver driver) {
    super(driver);
  }

  public boolean isPageDisplayed() {
    return isElementPresent(textPageTitle, 5000);
  }

  public String getTextPageTitle() {
    return textPageTitle.getText();
  }

  public void clickContinue() {
    waitUntilClickable(buttonContinue, 5).click();
  }

}

