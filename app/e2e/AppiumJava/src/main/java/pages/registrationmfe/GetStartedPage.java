package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class GetStartedPage extends BrowserPage {

  @FindBy(xpath = "//*[@data-testid='registration.mobile-number.registration-header']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='FormSelect-native-select']")
  private WebElement selectPhonePrefix;

  @FindBy(xpath = "//*[@data-testid='FormSelect-custom-select-value']")
  private WebElement textPhonePrefix;

  @FindBy(xpath = "//*[@data-testid='registration.mobile-number.input']")
  private WebElement inputPhone;

  @FindBy(xpath = "//*[@data-testid='continue-button']")
  private WebElement buttonContinue;

  @FindBy(xpath = "//*[@data-testid='GoogleSH-button']")
  private WebElement buttonGoogle;

  @FindBy(xpath = "//*[@resource-id='com.android.chrome:id/account_picker_dismiss_button']")
  private WebElement buttonSkip;

  public GetStartedPage(AppiumDriver driver) {
    super(driver);
  }

  public void clickPhonePrefix() {
    selectPhonePrefix.click();
  }

  public String getTextPhonePrefix() {
    return textPhonePrefix.getText();
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 60);
    return textPageTitle.getText();
  }

  public void enterPhoneNumber(String value) {
    inputPhone.click();
    inputPhone.sendKeys(value);
  }

  public void confirmPhoneNumber() {
    buttonContinue.click();
  }

  public String getPhoneNumber() {
    return inputPhone.getAttribute("value");
  }

  public void clickGoogleIcon(){
    buttonGoogle.click();
  }

  public void clickSkipCurrentUser(){
    if(isElementPresent(buttonSkip)){//some Saucelabs devices are logged in to Google account
      buttonSkip.click();
    }
  }

}

