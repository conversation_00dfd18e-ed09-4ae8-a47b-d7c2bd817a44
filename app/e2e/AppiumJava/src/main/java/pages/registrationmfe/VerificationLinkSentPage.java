package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class VerificationLinkSentPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Verification link sent']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  public WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='com.android.chrome:id/menu_button']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  public WebElement iconThreeDots;

  @AndroidFindBy(xpath = "//*[@text='Open in Chrome']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  public WebElement textOpenInBrowser;

  @AndroidFindBy(xpath = "//android.widget.EditText[@resource-id='com.android.chrome:id/url_bar']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  public WebElement inputUrl;

  public VerificationLinkSentPage(AppiumDriver driver) {
        super(driver);
    }

  public void clickBrowserMenu() {
    iconThreeDots.click();
  }

  public void openInBrowser() {
    textOpenInBrowser.click();
  }

  public void enterUrl(String verificationLink) {
    inputUrl.click();
    inputUrl.sendKeys(verificationLink);
    if (isAndroid) {
      ((AndroidDriver) driver).pressKey(new KeyEvent(AndroidKey.ENTER));
    }
  }
}
