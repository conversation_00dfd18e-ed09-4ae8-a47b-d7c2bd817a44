package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;

public class SignUpPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='input-12']")
  public WebElement inputPhoneNumber;

  @AndroidFindBy(xpath = "//*[@resource-id='First name']")
  public WebElement inputFirstName;

  @AndroidFindBy(xpath = "//*[@resource-id='Last name']")
  public WebElement inputLastName;

  @AndroidFindBy(xpath = "//*[@resource-id='countryButton']")
  public WebElement dropdownPhonePrefix;

  @AndroidFindBy(xpath = "//*[@resource-id='search']")
  public WebElement inputPhonePrefix;

  @AndroidFindBy(xpath = "//*[@resource-id='Mobile number']")
  public WebElement inputPhone;

  @AndroidFindBy(xpath = "//*[@resource-id='Email']")
  public WebElement inputEmail;

  @AndroidFindBy(xpath = "(//android.widget.CheckBox)[2]")
  public WebElement checkBoxTerms;

  @AndroidFindBy(xpath = "(//android.widget.CheckBox)[3]")
  public WebElement checkBoxPrivacy;

  @AndroidFindBy(xpath = "//*[@resource-id='confirm']")
  public WebElement buttonSaveSelection;

  @AndroidFindBy(xpath = "//*[@resource-id='continueButton']")
  public WebElement buttonContinue;

  public SignUpPage(AppiumDriver driver) {
        super(driver);
    }

  public void enterFirstName(String value) {
    inputFirstName.sendKeys((value));
  }

}
