package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class VerificationPage extends BasePage {

  public VerificationPage(AppiumDriver driver) {
        super(driver);
    }

  @AndroidFindBy(xpath = "//*[@text='Verify your identity']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Verify your identity\"]")
  private WebElement verifyYourIdentityTxt;

  @AndroidFindBy(xpath = "//*[@text='Next']")
  @iOSXCUITFindBy(xpath = "\t\n" + "//XCUIElementTypeButton[@name=\"Next\"]")
  private WebElement nextButton;

  @AndroidFindBy(xpath = "//*[@resource-id='input-4']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name=\"article\"]/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField")
  private WebElement inputVerificationCode;

  public void verifyYourIdentityTextIsDisplayed() {
    verifyYourIdentityTxt.isDisplayed();
  }

  public void clickEnterVerificationCodeBox() {
    inputVerificationCode.click();
  }

  public void clickNextButton() {
    nextButton.click();
  }
}
