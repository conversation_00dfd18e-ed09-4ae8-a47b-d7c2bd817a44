package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class SignInPage extends BrowserPage {

  @FindBy(xpath = "//*[@data-testid='login-landing-page.login-header']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='create-account-button']")
  private WebElement buttonRegister;

  public SignInPage(AppiumDriver driver) {
        super(driver);
    }

  public void clickRegisterNow() {
    buttonRegister.click();
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 60);
    return textPageTitle.getText();
  }
}

