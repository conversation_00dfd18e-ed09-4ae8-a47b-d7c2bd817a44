package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class CheckYourEmailPage extends BrowserPage {

  @FindBy(xpath = "//*[@data-testid='login-magic-link.subheading']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='login-magic-link.label']")
  private WebElement textEmailAt;

  @FindBy(xpath = "//*[@data-testid='login-magic-link.verification-label']")
  private WebElement inputVerificationCode;

  @FindBy(xpath = "//*[@data-testid='magic-link-sent.submit-passcode']")
  private WebElement buttonContinue;

  public CheckYourEmailPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 20);
    return textPageTitle.getText();
  }

  public String getTextEmailAt() {
    return textEmailAt.getText();
  }

  public void enterVerificationCode(String value) {
    inputVerificationCode.sendKeys(value);
    hideKeyboard();
  }

  public String getVerificationCode() {
    if (isAndroid) {
      return inputVerificationCode.getDomProperty("value");
    } else {
      return inputVerificationCode.getAttribute("value");
    }
  }

  public void clickContinue() {
    buttonContinue.click();
  }

}
