package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

public class CustomiseAppPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='exitHeaderButton']/parent::*/parent::*//android.view.View")
  @iOSXCUITFindBy(xpath = "(//*[@name='exitHeaderButton']/parent::*//parent::XCUIElementTypeOther)[1]")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//*[@resource-id='countrySelect']/android.widget.TextView")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='countrySelect']")
  private WebElement selectCoutry;

  @AndroidFindBy(xpath = "//*[@resource-id='Enter your first name']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeTextField[@name='Enter your first name']")
  private WebElement inputFirstName;

  @AndroidFindBy(xpath = "//*[@resource-id='Enter your last name']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeTextField[@name='Enter your last name']")
  private WebElement inputLastName;

  @AndroidFindBy(xpath = "//*[@content-desc='terms and conditions']")
  @iOSXCUITFindBy(xpath = "//*[@name='terms and conditions']")
  private WebElement checkboxTermsAndCond;

  @AndroidFindBy(xpath = "//com.horcrux.svg.SvgView")
  private WebElement imageCheckBox;

  @AndroidFindBy(xpath = "//*[@resource-id='continueButton']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='continueButton']")
  private WebElement buttonJump;

  @AndroidFindBy(xpath = "//android.widget.Button[@resource-id='Try again-button']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Try again-button']")
  private WebElement buttonTryAgain;

  public CustomiseAppPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 60);
    return textPageTitle.getText();
  }

  public void selectCountry(String value) {
    selectCoutry.click();
    if (isAndroid) {
      // cannot find selector for countries in Android. Workaround to select the country by pressing arrow down
      String[] countries = {"Netherlands", "Spain", "United Kingdom"};
      ((AndroidDriver) driver).pressKey(new KeyEvent(AndroidKey.DPAD_DOWN));

      for (String country : countries) {
        ((AndroidDriver) driver).pressKey(new KeyEvent(AndroidKey.DPAD_DOWN));
        if (country.equals(value)) {
          ((AndroidDriver) driver).pressKey(new KeyEvent(AndroidKey.ENTER));
          return;
        }
      }
    } else {
      WebElement country = driver.findElement(By.xpath("(//*[contains(@name, '" + value + "')])[last()]"));
      country.click();
    }
  }

  public String getSelectedCoutry() {
    return selectCoutry.getText();
  }

  public void enterFirstName(String value) {
    inputFirstName.sendKeys(value);
  }

  public String getFirstName() {
    return inputFirstName.getText();
  }

  public void enterlastName(String value) {
    inputLastName.sendKeys(value);
  }

  public String getLastName() {
    return inputLastName.getText();
  }

  public void clickTermsAndCond() {
    checkboxTermsAndCond.click();
  }

  public boolean isTermsAndCondChecked() {
    By by = By.xpath(getXpathSelector(checkboxTermsAndCond) + getXpathSelector(imageCheckBox));
    return isElementPresent(by);
  }

  public void clickJump() {
    buttonJump.click();
  }
}
