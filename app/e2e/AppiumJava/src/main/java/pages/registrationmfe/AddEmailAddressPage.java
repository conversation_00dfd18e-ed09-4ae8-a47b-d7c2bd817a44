package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class AddEmailAddressPage extends BasePage {

  @FindBy(xpath = "//*[@data-testid='registration.email-capture.subheading']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='registration.email-capture.email-field']")
  private WebElement inputEmail;

  @FindBy(xpath = "//*[@data-testid='registration.email-capture.continue-button']")
  private WebElement buttonContinue;

  public AddEmailAddressPage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 30);
    return textPageTitle.getText();
  }

  public void enterEmail(String email) {
    inputEmail.clear();
    inputEmail.sendKeys(email);
  }

  public String getEmail() {
    return inputEmail.getAttribute("value");
  }

  public void clickContinue() {
    buttonContinue.click();
  }

}
