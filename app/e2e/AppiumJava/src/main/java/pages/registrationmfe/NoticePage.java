package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;

public class NoticePage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='dataPrivacyContinueButton']")
  public WebElement buttonContinue;

  public NoticePage(AppiumDriver driver) {
    super(driver);
  }

  public void clickContinue() {
    waitUntilVisible(buttonContinue, 60);
    buttonContinue.click();
  }
}
