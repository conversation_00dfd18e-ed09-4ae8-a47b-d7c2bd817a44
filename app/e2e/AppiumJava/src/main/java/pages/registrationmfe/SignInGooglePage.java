package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class SignInGooglePage extends BrowserPage {

  @FindBy(xpath = "//h1[@id='headingText']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//input[@name='identifier']")
  private WebElement inputEmail;

  @FindBy(xpath = "//*[@id='identifierNext']")
  private WebElement buttonNextEmail;

  @FindBy(xpath = "//input[@name='Passwd']")
  private WebElement inputPassword;

  @FindBy(xpath = "//div[@id='passwordNext']")
  private WebElement buttonNextPassword;

  public SignInGooglePage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 20);
    return textPageTitle.getText();
  }

  public void enterEmail(String value) {
    inputEmail.sendKeys(value);
  }

  public String getTextEmail() {
    return inputEmail.getDomProperty("value");
  }

  public void clickNextEmail() {
    buttonNextEmail.click();
  }

  public void enterPassword(String password) {
    waitUntilVisible(inputPassword, 40);
    inputPassword.sendKeys(password);
  }

  public void clickNextPassword() {
    buttonNextPassword.click();
  }
}
