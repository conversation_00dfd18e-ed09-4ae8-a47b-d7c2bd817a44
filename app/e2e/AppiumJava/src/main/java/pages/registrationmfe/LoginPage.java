package pages.registrationmfe;

import core.BasePage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class LoginPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@text='Email']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name=\"Login\"]/XCUIElementTypeOther[4]/XCUIElementTypeOther[1]")
  private WebElement emailBtn;

  @AndroidFindBy(xpath = "//*[contains(@text,'+')]")
  @iOSXCUITFindBy(xpath = "//*[contains(@value,'+')]")
  private WebElement inputPhonePrefix;

  @AndroidFindBy(xpath = "//*[@hint='Phone']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Login']//XCUIElementTypeTextField")
  private WebElement inputPhoneNumber;

  @AndroidFindBy(xpath = "//*[@resource-id='frmEmailC-8']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[@name='Login']/XCUIElementTypeOther[5]/XCUIElementTypeTextField")
  private WebElement emailTxtField;

  @AndroidFindBy(xpath = "//*[@hint='Verification code']")
  @iOSXCUITFindBy(xpath = "//*[@value='VERIFICATION CODE']/parent::*/following-sibling::*//XCUIElementTypeTextField")
  private WebElement inputVerificationCode;

  @AndroidFindBy(xpath = "//*[@text='Next']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Next']")
  private WebElement nextBtn;

  @AndroidFindBy(xpath = "//*[@resource-id='input-17']")
  @iOSXCUITFindBy(xpath = "XCUIElementTypeOther[@name='Login']/XCUIElementTypeOther[6]/XCUIElementTypeOther[2]/XCUIElementTypeSecureTextField")
  private WebElement passwordField;

  @AndroidFindBy(xpath = "//*[@text='Log in']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Log in']")
  private WebElement loginBtn;

  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name=\"Done\"]")
  private WebElement doneButton;

  public LoginPage(AppiumDriver driver) {
        super(driver);
    }

  public void selectPhonePrefix(String value) {
    if (!inputPhonePrefix.getText().equals(value)) {
      inputPhonePrefix.click();
      selectPicklistValue(value);
    }
  }

  public void clickDone() {
    doneButton.click();
  }

  public String getPhoneNumber() {
    return inputPhoneNumber.getText();
  }

  public String getPhonePrefix() {
    return inputPhonePrefix.getText();
  }

  public void clickTheEmailButton() {
    emailBtn.click();
  }

  public void emailButtonIsDisplayed() {
    emailBtn.isDisplayed();
  }

  public void emailTextFieldIsDisplayed() {
    emailTxtField.isDisplayed();
  }

  public void enterValidEmail(String email) {
    emailTxtField.sendKeys(email);
  }

  public void selectTheNextButton() {
    nextBtn.click();
  }

  public void passwordFieldIsDisplayed() {
    passwordField.isDisplayed();
  }

  public void enterValidPassword(String password) {
    passwordField.sendKeys(password);
  }

  public void enterPhoneNumber(String value) {
    inputPhoneNumber.click();
    inputPhoneNumber.sendKeys(value);
    if (!isAndroid) {//the Login page gets displayed in Android for some reason
      hideKeyboard();
    }
  }

  public void enterVerificationCode(String verificationCode) {
    inputVerificationCode.sendKeys(verificationCode);
  }

  public String getVerificationCode() {
    return inputVerificationCode.getText();
  }

  public void clickTheLoginButton() {
    loginBtn.click();
  }

}
