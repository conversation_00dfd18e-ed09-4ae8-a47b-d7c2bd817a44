package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class VerifyYourEmailPage extends BrowserPage {

  @FindBy(xpath = "//*[@data-testid='registration-magic-link.subheading']")
  private WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='registration-magic-link.label']")
  private WebElement textEmailTo;

  public VerifyYourEmailPage(AppiumDriver driver) {
        super(driver);
    }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle,60);
    return textPageTitle.getText();
  }

}
