package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.FindBys;

import java.util.List;

public class EnterYourCodePage extends BrowserPage {

  @FindBy(xpath = "//*[@data-testid='login-otp-confirmation-subheader']")
  private WebElement textPageTitle;

  @FindBys(@FindBy(xpath = "//*[contains(@data-testid, 'form-input-otp-input-')]"))
  private List<WebElement> inputVerificationCode;

  @FindBy(xpath = "//*[@data-testid='login-otp-confirmation-submit']")
  private WebElement buttonContinue;

  public EnterYourCodePage(AppiumDriver driver) {
    super(driver);
  }

  public String getTextPageTitle() {
    waitUntilVisible(textPageTitle, 20);
    return textPageTitle.getText();
  }

  public void enterVerificationCode(String value) {
    for (int i = 0; i < value.length() && i < inputVerificationCode.size(); i++) {
      inputVerificationCode.get(i).click();
      waitMilliseconds(500);
      inputVerificationCode.get(i).sendKeys(String.valueOf(value.charAt(i)));
      waitMilliseconds(500);
    }
  }

  public String getVerificationCode() {
    StringBuilder sb = new StringBuilder();
    for (WebElement input : inputVerificationCode) {
      sb.append(input.getAttribute("value"));
    }
    return sb.toString();
  }

  public void clickContinue() {
    buttonContinue.click();
  }
}

