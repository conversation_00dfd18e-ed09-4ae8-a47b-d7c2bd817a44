package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;
import org.openqa.selenium.WebElement;

public class PrivacyStatementUpdatePage extends BrowserPage {

  @AndroidFindBy(xpath = "//android.widget.TextView[@text='Privacy statement update']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Privacy statement update']")
  private WebElement textPageTitle;

  @AndroidFindBy(xpath = "//android.widget.CheckBox[@text='Agree to bp’s privacy policy.']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeSwitch[@name='Agree to bp’s privacy policy.']")
  private WebElement switchAgree;

  @AndroidFindBy(xpath = "//android.widget.Button[@text='Continue']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeButton[@name='Continue']")
  private WebElement buttonContinue;

  public PrivacyStatementUpdatePage(AppiumDriver driver) {
        super(driver);
    }

  public boolean isPageDisplayed() {
    return isElementPresent(textPageTitle, 3000);
  }

  public String getTextPageTitle() {
    return textPageTitle.getText();
  }

  public void clickContinue() {
    buttonContinue.click();
  }

  public void clickAgree() {
    switchAgree.click();
  }

}

