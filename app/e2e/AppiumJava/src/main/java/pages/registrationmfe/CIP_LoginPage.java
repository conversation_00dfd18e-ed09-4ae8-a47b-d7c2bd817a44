package pages.registrationmfe;

import core.BrowserPage;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class CIP_LoginPage extends BrowserPage {

  @FindBy(css = "[data-testid='FacebookSH-button']")
  public WebElement faceBookIcon;

  @FindBy(xpath = "//*[@data-testid='login-landing-page.login-header']")
  public WebElement textPageTitle;

  @FindBy(xpath = "//*[@data-testid='login-landing-page.login-input']")
  public WebElement inputMobileOrEmail;

  @FindBy(xpath = "//*[@data-testid='login-landing-page.continue-button']")
  public WebElement buttonContinue;

  @FindBy(xpath = "//*[@data-testid='create-account-button']//*[text()='Create an account']")
  public WebElement buttonCreateAnAccount;

  public CIP_LoginPage(AppiumDriver driver) {
        super(driver);
    }

  public void clickFaceBookIcon() {
    faceBookIcon.click();
  }

  public void enterMobileOrEmail(String value) {
    waitUntilVisible(inputMobileOrEmail, 60);
    inputMobileOrEmail.click();
    inputMobileOrEmail.sendKeys(value);
  }

  public String getMobileOrEmail() {
    return inputMobileOrEmail.getText();
  }

  public void clickContinue() {
    this.hideKeyboard();
    buttonContinue.click();
  }

  public void clickCreateAnAccount() {
    buttonCreateAnAccount.click();
  }

}

