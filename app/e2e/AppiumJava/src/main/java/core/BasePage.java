package core;

import com.google.common.collect.ImmutableMap;
import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.pagefactory.AppiumFieldDecorator;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.interactions.PointerInput;
import org.openqa.selenium.interactions.Sequence;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Wait;
import org.openqa.selenium.support.ui.WebDriverWait;
import utils.StringUtilities;
import java.lang.reflect.InvocationTargetException;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import static utils.ConstantClass.*;

public class BasePage {

  public AppiumDriver driver;
  public boolean isAndroid;
  private static final Logger logger = Logger.getLogger(BasePage.class.getName());

  public BasePage(AppiumDriver driver) {
    PageFactory.initElements(new AppiumFieldDecorator(driver), this);
    this.driver = driver;
    this.isAndroid = String.valueOf(driver.getCapabilities().getPlatformName()).equals(ANDROID);
  }

  public boolean isElementPresent(WebElement element) {
    Duration globalWait = driver.manage().timeouts().getImplicitWaitTimeout();
    driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(1));
    try {
      return element.isDisplayed();
    } catch (NoSuchElementException | StaleElementReferenceException e) {
      return false;
    } finally {
      //back to previous value
      driver.manage().timeouts().implicitlyWait(globalWait);
    }
  }

  public boolean isElementPresent(By element) {
    Duration globalWait = driver.manage().timeouts().getImplicitWaitTimeout();
    driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(1));
    boolean present = !driver.findElements(element).isEmpty();
    driver.manage().timeouts().implicitlyWait(globalWait); //back to previous value
    return present;
  }

  public boolean isElementPresent(WebElement element, long miliseconds) {
    Instant startTime = Instant.now();
    while (Duration.between(startTime, Instant.now()).toMillis() < miliseconds) {
      if (isElementPresent(element)) {
        return true;
      }
      waitMilliseconds(1000);
    }
    return false;
  }

  public WebElement waitForEitherElement(WebElement we1, WebElement we2) {
    Instant startTime = Instant.now();
    while (Duration.between(startTime, Instant.now()).toMillis() < 40000) {
      if (isElementPresent(we1)) {
        return we1;
      } else if (isElementPresent(we2)) {
        return we2;
      } else {
        waitMilliseconds(500);
      }
    }
    return null;
  }

  public WebElement selectPicklistValue(String value) {
    List<WebElement> listItems;
    By byItemText;
    boolean endOfList = false;
    String lastItem = "";
    if (isAndroid) {
      byItemText = By.xpath("//android.widget.TextView | //android.widget.CheckedTextView");
    } else {
      byItemText = By.xpath("//XCUIElementTypeCell/XCUIElementTypeButton[@visible='true']");
    }
    while (!endOfList) {
      listItems = driver.findElements(byItemText);
      if (lastItem.equals(listItems.get(listItems.size() - 1).getText())) {
        endOfList = true;
      }
      for (WebElement item : listItems) {
        if (item.getText().contains(value)) {
          item.click();
          return item;
        }
      }
      lastItem = listItems.get(listItems.size() - 1).getText();
      swipeFromElementToElement(listItems.get(listItems.size() - 1), listItems.get(0));
    }
    return null;
  }

  public void swipeFromElementToElement(WebElement from, WebElement to) {
    Actions actions = new Actions(driver);
    actions.clickAndHold(from)
      .moveToElement(to).pause(Duration.ofMillis(1000))
      .release()
      .build()
      .perform();
  }

  public Point getElementCenter(WebElement we) {
    int centerX = we.getLocation().getX() + (we.getSize().getWidth() / 2);
    int centerY = we.getLocation().getY() + (we.getSize().getHeight() / 2);
    return new Point(centerX, centerY);
  }

  public void swipeFromElementToElement(WebElement from, WebElement to, long speedMilis) {
    swipeFromPointToPoint(getElementCenter(from), getElementCenter(to), speedMilis);
  }

  public void swipeFromPointToPoint(Point from, Point to, long speedMilis) {
    PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");
    Sequence swipe = new Sequence(finger, 1);
    swipe.addAction(finger.createPointerMove(Duration.ofMillis(0),
      PointerInput.Origin.viewport(), from.getX(), from.getY()));
    swipe.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()));
    swipe.addAction(finger.createPointerMove(Duration.ofMillis(speedMilis),
      PointerInput.Origin.viewport(), to.getX(), to.getY()));
    swipe.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));
    driver.perform(Arrays.asList(swipe));
  }

  public void swipeFromPointToPoint(Point from, Point to) {
    swipeFromPointToPoint(from, to, 1000);
  }

  public void doubleTap(WebElement we) {
    Actions actions = new Actions(driver);
    actions.click(we).pause(Duration.ofMillis(100)).click()
      .build()
      .perform();
  }

  public void tapCoordinates(int x, int y) {
    Actions actions = new Actions(driver);
    actions.moveToLocation(x, y).click()
      .build()
      .perform();
  }

  public void swipeUp() {
    int x = 0;
    int middleY = driver.manage().window().getSize().getHeight() / 2;
    swipeFromPointToPoint(new Point(x, middleY), new Point(x, 0));
  }

  public void swipeDown() {
    int x = 0;
    int middleY = driver.manage().window().getSize().getHeight() / 2;
    swipeFromPointToPoint(new Point(x, middleY), new Point(x, driver.manage().window().getSize().getHeight()));
  }

  public void pressKeys(String value) {
    KeyEvent enterKeyEvent;
    for (char character : value.toCharArray()) {
      if (Character.isDigit(character)) {
        enterKeyEvent = new KeyEvent(AndroidKey.valueOf("DIGIT_" + character));
      } else {
        throw new IllegalArgumentException("Invalid value: " + value);
      }
      ((AndroidDriver) driver).pressKey(enterKeyEvent);
    }
  }

  public void hideKeyboard() {
    if (isAndroid) {
      ((AndroidDriver) driver).hideKeyboard();
    } else {
      By keyReturn = By.xpath("//XCUIElementTypeButton[@name='Done']");
      By keyboardLayout = By.xpath("//XCUIElementTypeKeyboard");
      if (isElementPresent(keyReturn)) { //to awoid error: "Call to 'hideKeyboard' failed"
        driver.findElement(keyReturn).click();
      } else if (isElementPresent(keyboardLayout)) {
        WebElement we = driver.findElement(keyboardLayout);
        int x = driver.manage().window().getSize().getWidth() - 20;
        int y = we.getLocation().getY() - 10;
        tapCoordinates(x, y); // Done on top righ corner
      } else {
        ((IOSDriver) driver).isKeyboardShown();
      }
    }
    waitMilliseconds(500);
  }

  public void waitMilliseconds(long milliseconds) {
    try {
      Thread.sleep(milliseconds);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
  }

  public WebElement waitUntilVisible(WebElement we, long seconds) {
    Wait<WebDriver> wait = new WebDriverWait(driver, Duration.ofSeconds(seconds))
      .ignoring(StaleElementReferenceException.class);
    return wait.until(ExpectedConditions.visibilityOf(we));
  }

  public WebElement waitUntilVisible(By by, long seconds) {
    WebDriverWait webDriverWait = new WebDriverWait(driver, Duration.ofSeconds(seconds));
    return webDriverWait.until(ExpectedConditions.visibilityOfElementLocated(by));
  }

  public boolean waitUntilInvisible(WebElement we, long seconds) {
    Wait<WebDriver> wait = new WebDriverWait(driver, Duration.ofSeconds(seconds))
      .ignoring(StaleElementReferenceException.class);
    return wait.until(ExpectedConditions.invisibilityOf(we));
  }

  public Object handleStaleElementException(WebElement we, String methodName) {
    try {
      return WebElement.class.getMethod(methodName).invoke(we);
    } catch (StaleElementReferenceException | InvocationTargetException
             | NoSuchMethodException | IllegalAccessException e) {
      logger.log(Level.INFO, "Exception ", e);
      return handleStaleElementException(we, methodName);
    }
  }

  public WebElement waitUntilClickable(WebElement we, long seconds) {
    WebDriverWait webDriverWait = new WebDriverWait(driver, Duration.ofSeconds(seconds));
    return webDriverWait.until(ExpectedConditions.elementToBeClickable(we));
  }

  public void tapElementCoordinates(WebElement we) {
    tapCoordinates(we.getLocation().getX(), we.getLocation().getY());
  }

  public boolean switchContext(String contextToSwitch) {
    Instant startTime = Instant.now();
    while (Duration.between(startTime, Instant.now()).toMillis() < 80000) {
      Set<String> availableContextNames = getAllContexts();
      Optional<String> matchingContext = availableContextNames.stream()
        .filter(name -> name.startsWith(contextToSwitch))
        .findFirst();
      if (matchingContext.isPresent()) {
        setContext(matchingContext.get());
        if (getCurrentContext().startsWith(contextToSwitch)) {
          return true;
        }
      }
      waitMilliseconds(1000);
    }
    return false;
  }

  public String getCurrentContext() {
    if (isAndroid) {
      return ((AndroidDriver) driver).getContext();
    } else {
      return ((IOSDriver) driver).getContext();
    }
  }

  public Set<String> getAllContexts() {
    if (isAndroid) {
      return ((AndroidDriver) driver).getContextHandles();
    } else {
      return ((IOSDriver) driver).getContextHandles();
    }
  }

  public void setContext(String contextName) {
    waitMilliseconds(1000);
    if (isAndroid) {
      ((AndroidDriver) driver).context(contextName);
    } else {
      ((IOSDriver) driver).context(contextName);
    }
  }

  public void closeApp(String packageName) {
    if (isAndroid) {
      driver.executeScript("mobile: terminateApp", ImmutableMap.of("appId", packageName));
    }
  }

  public void closeChrome() {
    closeApp(COM_ANDROID_CHROME);
  }

  public void startApp(String packageName) {
    ((AndroidDriver) driver).activateApp(packageName);
  }

  public String getPackageName() {
    return ((AndroidDriver) driver).getCurrentPackage();
  }


  public String getComponentName() {
    String currentPackage = ((AndroidDriver) driver).getCurrentPackage();
    String currentActivity = ((AndroidDriver) driver).currentActivity();
    return currentPackage + "/" + currentActivity;
  }

  public String startApplication(String app) {
    driver.executeScript("mobile: startActivity", ImmutableMap.of("intent", app));
    return getComponentName();
  }

  public static String getXpathSelector(WebElement we) {
    return StringUtilities.substringBetween(we.toString(), "By.chained({By.xpath: ", "})");
  }

  public WebElement getChainedElement(WebElement parentItem, WebElement chainingElement) {
    String xpathSelector = getXpathSelector(chainingElement);
    return getChainedElement(parentItem, xpathSelector);
  }

  public WebElement getChainedElement(WebElement parentItem, String xpathSelector) {
    try {
      return parentItem.findElement(By.xpath(xpathSelector));
    } catch (NoSuchElementException e) {
      return null;
    }
  }

  public void waitForPageLoaded(int seconds) {
    Instant startTime = Instant.now();
    while (Duration.between(startTime, Instant.now()).toSeconds() < seconds) {
      int startLength = driver.getPageSource().length();
      waitMilliseconds(2000);
      int endLength = driver.getPageSource().length();
      if (startLength == endLength) {// the content doesn't change, should be loaded
        break;
      }
    }
  }

  public boolean waitForElementAttributeToBe(WebElement we, String attribute, String toBe, int seconds) {
    Instant startTime = Instant.now();
    while (Duration.between(startTime, Instant.now()).toSeconds() < seconds) {
      if (we.getAttribute(attribute).equals(toBe)) {
        return true;
      }
    }
    return false;
  }

  public void unfocusInput(WebElement we) {
    ((JavascriptExecutor) driver).executeScript("arguments[0].blur();", we);
  }

}
