package core;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import io.appium.java_client.pagefactory.*;
import org.openqa.selenium.WebElement;

import java.util.List;

public class BrowserPage extends BasePage {

  @AndroidFindBy(xpath = "//*[@resource-id='com.android.chrome:id/close_button'] | //android.widget.ImageButton[@content-desc=\"Return to previous app\"]")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement iconClose;

  @AndroidFindBy(xpath = "//*[@resource-id='com.android.chrome:id/url_bar']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement textUrl;

  @AndroidFindBy(xpath = "//android.widget.ImageButton[@content-desc='Share']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement iconShare;

  @AndroidFindBy(xpath = "//*[@resource-id='com.android.chrome:id/menu_button']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement iconThreeDots;

  @AndroidFindBy(xpath = "//*[contains(@text, ' in Chrome')]")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement textOpenInBrowser;

  @AndroidFindBy(xpath = "//android.widget.EditText[@resource-id='com.android.chrome:id/url_bar']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement inputUrl;

  @AndroidFindBy(xpath = "//android.widget.EditText[@resource-id='com.android.chrome:id/search_box_text']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement inputSearch;

  @AndroidFindBy(xpath = "//android.widget.Button[@resource-id='com.android.chrome:id/message_primary_button']")
  @iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name=")
  private WebElement buttonContinue;

  @AndroidFindBy(xpath = "(//android.widget.FrameLayout[@package='com.android.chrome'])[1]")
  private WebElement chromeLayout;

  @iOSXCUITFindBy(xpath = "//XCUIElementTypeOther[contains(@name,'BrowserView')]")
  private WebElement safariLayout;

  @AndroidFindBy(xpath = "(//android.widget.FrameLayout[@package='org.mozilla.firefox'])[1]")
  private WebElement firefoxLayout;

  @AndroidFindBy(xpath = "//*[@text='Chrome']")
  private WebElement imageChromeLogoShare;

  @AndroidFindBys({@AndroidBy(xpath = "//*[@resource-id='com.android.chrome:id/share_sheet_other_apps']/android.widget.RelativeLayout")})
  private List<WebElement> listShareByApp;

  @AndroidFindBy(xpath = "//android.widget.Button[@resource-id='com.android.chrome:id/negative_button']")
  private WebElement buttonNoThanks;

  public BrowserPage(AppiumDriver driver) {
    super(driver);
  }

  public boolean isChromeOpened() {
    return chromeLayout.isDisplayed();
  }

  public boolean isSafariOpened() {
    return safariLayout.isDisplayed();
  }

  public String getDefaultBrowser() {
    WebElement layout = waitForEitherElement(chromeLayout, firefoxLayout);
    if (layout.getAttribute("package").contains("chrome")) {
      return "chrome";
    } else if (layout.getAttribute("package").contains("firefox")) {
      return "firefox";
    } else {
      return "unknown browser";
    }
  }

  public void closeBrowser() {
    iconClose.click();
  }


  public void clickShare() {
    iconShare.click();
  }

  public void selectShereWithChrome() {
    if (!isElementPresent(imageChromeLogoShare)) {
      swipeFromElementToElement(listShareByApp.get(listShareByApp.size() - 1), listShareByApp.get(0));
    }
    imageChromeLogoShare.click();
  }

  public void clickBrowserMenu() {
    iconThreeDots.click();
  }

  public void openInBrowser() {
    textOpenInBrowser.click();
  }

  public void clickContinue() {
    buttonContinue.click();
  }

  public void enterUrl(String link) {
    WebElement input = waitForEitherElement(inputUrl, inputSearch); //Chrome browser may open with some of these inputs
    input.click();
    inputUrl.sendKeys(link);
    if (isAndroid) {
      ((AndroidDriver) driver).pressKey(new KeyEvent(AndroidKey.ENTER));
    }
  }

  public void clickNoThanksChromeNotifications() {
    if (isElementPresent(buttonNoThanks)) {
      buttonNoThanks.click();
    }
  }


}
