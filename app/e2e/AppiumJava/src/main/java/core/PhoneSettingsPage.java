package core;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.pagefactory.AndroidBy;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.AndroidFindBys;
import org.openqa.selenium.WebElement;

import java.util.List;

public class PhoneSettingsPage extends BasePage {

  @AndroidFindBy(xpath = "//*[contains(@resource-id, 'search_action_bar_title') or contains(@resource-id, 'search_bar_title')]")
  private WebElement buttonSearchSettings;

  @AndroidFindBy(xpath = "//*[contains(@resource-id, 'open_search_view_edit_text')]")
  private WebElement inputSearchSettings;

  @AndroidFindBy(xpath = "//android.widget.ImageButton[@content-desc='Navigate up']")
  private WebElement iconBack;

  @AndroidFindBys({@AndroidBy(xpath = "//*[contains(@resource-id, 'list_results')]/android.widget.LinearLayout")})
  private List<WebElement> listResults;

  @AndroidFindBy(xpath = "//*[contains(@resource-id, 'title')]")
  private WebElement title;

  public PhoneSettingsPage(AppiumDriver driver) {
        super(driver);
    }

  public void searchSettings(String defaultBrowser) {
    buttonSearchSettings.click();
    inputSearchSettings.sendKeys(defaultBrowser);
  }

  public WebElement openSearchResult(String value) {
    waitMilliseconds(3000);
    waitUntilVisible(listResults.get(0), 20);
    for (WebElement result : listResults) {
      String settingsName = getChainedElement(result, title).getText();
      if (settingsName.equals(value)) {
        return result;
      }
    }
    return null;
  }

  public void clickBack() {
    iconBack.click();
  }
}
