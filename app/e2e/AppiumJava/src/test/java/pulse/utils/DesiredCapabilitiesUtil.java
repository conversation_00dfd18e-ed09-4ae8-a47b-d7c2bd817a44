package pulse.utils;

import io.appium.java_client.android.options.UiAutomator2Options;
import io.appium.java_client.ios.options.XCUITestOptions;
import io.appium.java_client.remote.AutomationName;
import io.cucumber.java.Scenario;
import org.openqa.selenium.MutableCapabilities;

import java.time.Duration;
import java.util.Collection;

public class DesiredCapabilitiesUtil {
  private final Scenario scenario;
  private final boolean localMode;

  public DesiredCapabilitiesUtil(Scenario scenario, boolean localMode) {
    this.scenario = scenario;
    this.localMode = localMode;
  }

  private MutableCapabilities getSauceLabsCapabilities() {
    MutableCapabilities sauceOptions = new MutableCapabilities();
    sauceOptions.setCapability("username", System.getenv("SAUCE_USERNAME"));
    sauceOptions.setCapability("accessKey", System.getenv("SAUCE_ACCESS_KEY"));
    sauceOptions.setCapability("build", System.getenv("parameters.buildNumber"));
    sauceOptions.setCapability("commandTimeout", 300); //to avoid closing the session while debugging
    sauceOptions.setCapability("appiumVersion", "latest");  //https://docs.saucelabs.com/mobile-apps/automated-testing/appium/appium-versions/
    sauceOptions.setCapability("enableAnimations", true);
    sauceOptions.setCapability("name", scenario.getName());
    return sauceOptions;
  }

  public UiAutomator2Options setDesiredAndroidCapabilities() {
    UiAutomator2Options uiAutomator2Options = new UiAutomator2Options();
    uiAutomator2Options.setAutomationName(AutomationName.ANDROID_UIAUTOMATOR2);
    uiAutomator2Options.autoGrantPermissions();
    uiAutomator2Options.setCapability("locationServicesAuthorized", true);
    uiAutomator2Options.setCapability("locationServicesEnabled", true);
    uiAutomator2Options.setCapability("sauce:options", getSauceLabsCapabilities());
    if (localMode) {
      uiAutomator2Options.setDeviceName(System.getenv("DEVICE_NAME"));
      uiAutomator2Options.setPlatformVersion(System.getenv("PLATFORM_VERSION"));
      uiAutomator2Options.setApp(System.getenv("BUILD_FILE_NAME"));
      uiAutomator2Options.setCapability("appium:newCommandTimeout", 1000);
    } else {
      uiAutomator2Options.setApp("storage:filename=" + System.getenv("AAB_FILE_NAME"));
//      uiAutomator2Options.setApp("storage:f2992c8b-b85b-4d28-8a93-f7447dbea174");
        uiAutomator2Options.setDeviceName("^(?:Google|Nokia|HTC|Oppo|Sony|Xiaomi)(?!.*(?:Tab|Pad|Fold)).*$");
      uiAutomator2Options.setPlatformVersion("11.*|12.*|13.*|14.*|15.*");
    }
    return uiAutomator2Options;
  }

  public XCUITestOptions setDesiredIosCapabilities() {
    XCUITestOptions xcuiTestOptions = new XCUITestOptions();
    xcuiTestOptions.setPlatformName("ios");
    xcuiTestOptions.autoAcceptAlerts();
    xcuiTestOptions.autoDismissAlerts();
    xcuiTestOptions.setCommandTimeouts(Duration.ofMinutes(1));
    xcuiTestOptions.setCapability("sauce:options", getSauceLabsCapabilities());
    if (localMode) {
      xcuiTestOptions.setDeviceName(System.getenv("DEVICE_NAME"));
      xcuiTestOptions.setPlatformVersion(System.getenv("PLATFORM_VERSION"));
      xcuiTestOptions.setApp(System.getenv("BUILD_FILE_NAME"));
      xcuiTestOptions.setCapability("appium:newCommandTimeout", 1000);
      xcuiTestOptions.setCapability("appium:udid", System.getenv("DEVICE_UDID"));
    } else {
      xcuiTestOptions.setDeviceName("iPhone.*");
      xcuiTestOptions.setPlatformVersion("^(17.*|18.*)");
      xcuiTestOptions.setApp("storage:filename=" + System.getenv("IOS_SCHEME") + System.getenv("RELEASE_VARIANT") + ".ipa");
//      xcuiTestOptions.setApp("storage:dbaf31ea-0482-4e77-9135-6771a5fc465b");
    }
    return xcuiTestOptions;
  }

  public boolean isTagPresent(String cucumberTag, Scenario scenario) {
    Collection<String> tags = scenario.getSourceTagNames();
    for (String tag : tags) {
      if (tag.equals(cucumberTag)) {
        return true;
      }
    }
    return false;
  }
}
