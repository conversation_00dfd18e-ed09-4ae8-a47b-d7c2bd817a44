package pulse.utils;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.Scenario;
import utils.StringUtilities;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.Duration;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Hooks extends BaseUtils {
  BaseUtils base;
  private final boolean localMode = System.getenv("LOCAL_MODE") != null && System.getenv("LOCAL_MODE").equals("true");
  private static final Logger logger = Logger.getLogger(Hooks.class.getName());

  public Hooks(BaseUtils base) {
    this.base = base;
  }

  @Before
  public void init(Scenario scenario) throws MalformedURLException {
    DesiredCapabilitiesUtil desiredCapabilitiesUtil = new DesiredCapabilitiesUtil(scenario, localMode);
    URL url;

    if (localMode) {
      url = new URL("http://localhost:4723");
    } else {
      url = new URL("https://ondemand.eu-central-1.saucelabs.com:443/wd/hub");
    }
    if (System.getenv("MOBILE_PLATFORM").equalsIgnoreCase("Android")) {
      driver = new AndroidDriver(url, desiredCapabilitiesUtil.setDesiredAndroidCapabilities());
    } else {
      driver = new IOSDriver(url, desiredCapabilitiesUtil.setDesiredIosCapabilities());
    }

    driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
    base.setDriver(driver);
  }

  @After
  public void cleanUp(Scenario scenario) {
    String status = scenario.getStatus().toString();
    if (!localMode) {
      base.getDriver().executeScript("sauce:job-result=" + status.toLowerCase());
    }

    String device = (String) driver.getCapabilities().getCapability("appium:testobject_device_name");
    String ver = (String) driver.getCapabilities().getCapability("appium:platformVersion");
    String udid = (String) driver.getCapabilities().getCapability("appium:deviceUDID");
    //String feature = StringUtilities.getStringAfter("/", String.valueOf(scenario.getUri()));
    logger.log(Level.INFO, "---------------- " + scenario.getStatus() + " on device: " + device + " v." + ver + " " + udid + " ----------------");

    if (status.equals("FAILED")) {
      if (System.getenv("USER").equals("vsts")) { // Printing the xml page source to the pipeline log
        logger.log(Level.INFO, "---------------- Page source for scenario: '" + scenario.getName() + "' ----------------");
        logger.log(Level.INFO, driver.getPageSource());
      } else {
        StringUtilities.savePageSourceToFile(driver); //saving the page source to app/e2e/AppiumJava/source.xml
      }
    }

    try {
      if (base.isAndroid() && !((AndroidDriver) base.getDriver()).getConnection().isWiFiEnabled()) {
        ((AndroidDriver) base.getDriver()).toggleWifi(); // Some tests are turning WiFi off, making sure it is back on again
      }
      base.getDriver().quit();
    } catch (Exception e) {
      throw new AssertionError("Drive quit giving Exception", e);
    }

  }
}
