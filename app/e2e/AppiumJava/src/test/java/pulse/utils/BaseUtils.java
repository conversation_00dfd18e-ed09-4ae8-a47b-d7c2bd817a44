package pulse.utils;

import io.appium.java_client.AppiumDriver;
import utils.Pojo.User;
import utils.Translator;

import static utils.ConstantClass.ANDROID;

public class BaseUtils {
  public AppiumDriver driver;
  private User user;
  private Translator translator;

  public AppiumDriver getDriver() {
    return driver;
  }

  public void setDriver(AppiumDriver driver) {
    this.driver = driver;
  }

  public User getUser() {
    return user;
  }

  public void setUser(User user) {
    this.user = user;
    this.translator = new Translator(user);
  }

  public boolean isAndroid() {
    return String.valueOf(driver.getCapabilities().getPlatformName()).equals(ANDROID);
  }

  public static String getBrand() {
    return System.getenv("APP_BRAND");
  }

  public String getTranslation(String value) {
    return this.translator.getTranslatedString(value);
  }

}
