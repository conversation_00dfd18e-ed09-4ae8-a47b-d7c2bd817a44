package pulse.runners;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;
import org.testng.annotations.DataProvider;

@CucumberOptions(
  features = "src/test/resources/features/",
  glue = {"pulse.stepDefinitions", "pulse.utils"},
  publish = true,
  plugin = {
    "pretty",
    "junit:target/site/junit.xml",
    "html:target/site/cucumber-html-report.html",
    "json:target/site/cucumber.json",
    "html:target/site/cucumber-pretty.html",
    "rerun:rerun/failed_scenarios.txt"
  })

public class CucumberRunTests extends AbstractTestNGCucumberTests {
  @Override
  @DataProvider(parallel = true)
  public Object[][] scenarios() {
    return super.scenarios();
  }
}

