package pulse.stepDefinitions.notifications;

import io.cucumber.java.en.When;
import pages.registrationmfe.NoticePage;
import pulse.utils.BaseUtils;

public class NotificationSteps extends BaseUtils {
    BaseUtils base;

  private final NoticePage noticePage;

  public NotificationSteps(BaseUtils base) {
    super();
    this.base = base;
    this.noticePage = new NoticePage(base.getDriver());
  }

  @When("Users taps on Continue on Notify for guest users page")
  public void tapOnContinueOnNotifyForGuestUsersPage() {
    if (noticePage.isAndroid) {
      noticePage.clickContinue();
    }
  }
}
