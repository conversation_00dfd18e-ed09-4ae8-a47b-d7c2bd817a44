package pulse.stepDefinitions.wallet;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.openqa.selenium.WebElement;
import pages.wallet.AddYourPaymentCardPage;
import pages.wallet.ModalPage;
import pages.wallet.PaymentMethodsPage;
import pulse.stepDefinitions.CommonSteps;
import pulse.utils.BaseUtils;
import utils.StringUtilities;

import java.util.ArrayList;
import java.util.List;

import static org.testng.Assert.*;

public class PaymentMethodsSteps extends BaseUtils {
  BaseUtils base;

  private final PaymentMethodsPage paymentMethodsPage;
  private final AddYourPaymentCardPage addYourPaymentCardPage;
  private String deletedCard;

  public PaymentMethodsSteps(BaseUtils base) {
    super();
    this.base = base;
    paymentMethodsPage = new PaymentMethodsPage(base.getDriver());
    addYourPaymentCardPage = new AddYourPaymentCardPage(base.getDriver());
  }

  @Then("the Payment methods page is displayed")
  public void iAmOnPaymentDetailsPage() {
    assertEquals(paymentMethodsPage.getTextPageTitle(), "Payment methods");
    List<String> cards = paymentMethodsPage.getCardNames();
    base.getUser().setCards(cards);
  }

  @When("user taps Add a new credit or debit card")
  public void userClicsAddNewCard() {
    paymentMethodsPage.clickAddNewCard();
  }

  @When("user goes back from Payment methods page")
  public void backFromPaymentDetailsPage() {
    paymentMethodsPage.backToPreviousScreen();
  }

  @When("user removes all the cards")
  public void iRemoveAllCards() {
    List<WebElement> linkedCards = paymentMethodsPage.getListLinkedCards();
    while (!linkedCards.isEmpty()) {
      String cardNameAndLastFour = paymentMethodsPage.getCardBrandAndLastFour(linkedCards.get(0));
      boolean isFinalCard = linkedCards.size() == 1;
      paymentMethodsPage.deleteCard(cardNameAndLastFour);
      if (isFinalCard) {
        new ModalPage(base.getDriver(), "Remove final payment card?").clickButton("Remove card");
      } else {
        new ModalPage(base.getDriver(), "Remove payment card?").clickButton("Remove card");
      }
      paymentMethodsPage.waitMilliseconds(1000);
      assertNull(paymentMethodsPage.getLinkedCard(cardNameAndLastFour)); //make sure the card has been removed
      linkedCards = paymentMethodsPage.getListLinkedCards();
    }
    assertEquals(paymentMethodsPage.getCardNames().size(), 0);
    base.getUser().setCards(new ArrayList<>());
  }

  @Then("the linked cards list is an empty")
  public void theLinkedCardsListIsAnEmpty() {
    assertEquals(paymentMethodsPage.getCardNames().size(), 0);
  }

  @When("user removes non default card")
  public void userRemovesNonDefaultCard() {
    List<WebElement> linkedCards = paymentMethodsPage.getListLinkedCards();
    assertTrue(linkedCards.size() > 1);
    deletedCard = paymentMethodsPage.getCardBrandAndLastFour(linkedCards.get(1));
    paymentMethodsPage.deleteCard(deletedCard);
    new ModalPage(base.getDriver(), "Remove payment card?").clickButton("Remove card");
  }

  @Then("the card can be removed from the wallet")
  public void cardCanBeRemoved() {
    assertNull(paymentMethodsPage.getLinkedCard(deletedCard));
  }

  @When("user removes the default card")
  public void userRemovesTheDefaultCard() {
    List<WebElement> linkedCards = paymentMethodsPage.getListLinkedCards();
    assertTrue(linkedCards.size() > 1);
    deletedCard = paymentMethodsPage.getCardBrandAndLastFour(linkedCards.get(0));
    paymentMethodsPage.deleteCard(deletedCard);
    new ModalPage(base.getDriver(), "Remove payment card?").clickButton("Remove card");
    linkedCards = paymentMethodsPage.getListLinkedCards();
    base.getUser().setDefaultCard(paymentMethodsPage.getCardBrandAndLastFour(linkedCards.get(0)));
  }

  @When("user removes the card from the wallet")
  public void userRemovesCardFromTheWallet() {
    List<WebElement> linkedCards = paymentMethodsPage.getListLinkedCards();
    if (linkedCards.size() > 0) {
      String cardNameAndLastFour = paymentMethodsPage.getCardBrandAndLastFour(linkedCards.get(0));
      boolean isFinalCard = linkedCards.size() == 1;
      paymentMethodsPage.deleteCard(cardNameAndLastFour);
      if (isFinalCard) {
        new ModalPage(base.getDriver(), "Remove final payment card?").clickButton("Remove card");
      } else {
        new ModalPage(base.getDriver(), "Remove payment card?").clickButton("Remove card");
      }
      assertNull(paymentMethodsPage.getLinkedCard(cardNameAndLastFour)); //make sure the card has been removed
    }
    List<String> cards = paymentMethodsPage.getCardNames();
    base.getUser().setCards(cards);
  }

  @Then("the second card will automatically become the default")
  public void removeDefault() {
    assertNull(paymentMethodsPage.getLinkedCard(deletedCard)); //make sure the card has been removed
    assertTrue(paymentMethodsPage.isCardFirstAndDefault(base.getUser().getDefaultCard()));
  }

  @And("the added card is in the Linked cards list")
  public void theAddedCardIsInTheLinkedCardsList() {
    String expectedCard = StringUtilities.getNameAndLastFour(base.getUser().getNewCard());
    assertTrue(paymentMethodsPage.isCardPresent(expectedCard), "The card " + expectedCard + " cannot be found");
  }

  @Then("Add your payment card page is displayed")
  public void addYourPaymentCardIsDisplayed() {
    assertEquals(addYourPaymentCardPage.getTextPageTitle(), "Add your payment card");
  }

  @When("user clicks Add card detail")
  public void userClicksAddPaymentCard() {
    addYourPaymentCardPage.clickAddPaymentCard();
  }

  @Given("there are at least {int} credit cards in a wallet")
  public void thereIsMoreThenOneCreditCard(int count) {
    while (paymentMethodsPage.getListLinkedCards().size() < count) {
      userClicsAddNewCard();
      CommonSteps commonSteps = new CommonSteps(base);
      commonSteps.userCanAddNewCardToTheWallet();
      PaymentMethodsSteps paymentMethodsSteps = new PaymentMethodsSteps(base);
      paymentMethodsSteps.iAmOnPaymentDetailsPage();
      paymentMethodsSteps.theAddedCardIsInTheLinkedCardsList();
    }
  }

  @When("clicking Default card button")
  public void clickingDefaultCardButton() {
    paymentMethodsPage.clickDefaultCard();
  }

  @And("the new default card is set and listed as first")
  public void theNewDefaultCardIsSetAndListedAsFirst() {
    String defaultCard = base.getUser().getDefaultCard();
    assertTrue(paymentMethodsPage.isCardFirstAndDefault(defaultCard));
  }

  @Then("Check your internet connection is displayed on the Payment methods page")
  public void checkYourInternetConnectionIsDisplayedOnThePaymentDetailsPage() {
    assertEquals(paymentMethodsPage.getTextCheckInternet(), "Check your internet connection and try again.");
  }

  @Then("Check your internet connection is not displayed on the Payment methods page")
  public void checkYourInternetConnectionIsNotDisplayedOnThePaymentDetailsPage() {
    assertTrue(paymentMethodsPage.isCheckInternetInvisible());
  }
}
