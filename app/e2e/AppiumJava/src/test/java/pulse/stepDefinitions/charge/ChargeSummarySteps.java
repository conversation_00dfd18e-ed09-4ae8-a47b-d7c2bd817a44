package pulse.stepDefinitions.charge;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.chargemfe.ChargeSummaryPage;
import pages.mapmfe.MapPage;
import pulse.utils.BaseUtils;
import utils.StringUtilities;
import java.util.Date;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class ChargeSummarySteps extends BaseUtils {
  BaseUtils base;
  private final ChargeSummaryPage chargeSummaryPage;

  public ChargeSummarySteps(BaseUtils base) {
    super();
    this.base = base;
    this.chargeSummaryPage = new ChargeSummaryPage(base.getDriver());
  }

  @Then("Charge summary page is displayed")
  public void chargeSummaryPageIsDisplayed() {
    assertEquals(chargeSummaryPage.getPageTitle(), "Charge summary");
    assertTrue(base.getUser().getChargeSession().getAddress().contains(chargeSummaryPage.getAddress()));
    String chargingEndDateTimeString = chargeSummaryPage.getEndChargingDateTime();
    Date chargingEndDateTime = StringUtilities.formatDateString(chargingEndDateTimeString);
    base.getUser().getChargeSession().setChargingDateTime(chargingEndDateTime);
    int status = Integer.parseInt(chargeSummaryPage.getStatus().replace(" %", ""));
    assertTrue(status >= base.getUser().getChargeSession().getStatus());

    float averageSpeed;
    float energyDelivered;
    float totalCost;
    if (base.isAndroid()) {
      averageSpeed = Float.parseFloat(StringUtilities.getStringBefore(chargeSummaryPage.getAverageSpeed(), " kW"));
      energyDelivered = Float.parseFloat(StringUtilities.getStringBefore(chargeSummaryPage.getEnergyDelivered(), " kWh"));
    } else {
      averageSpeed = Float.parseFloat(StringUtilities.getStringBefore(chargeSummaryPage.getAverageSpeed(), " kW "));
      energyDelivered = Float.parseFloat(StringUtilities.substringBetween(chargeSummaryPage.getEnergyDelivered(), " kW ", " kWh"));
    }
    totalCost = Float.parseFloat(StringUtilities.getStringAfter(chargeSummaryPage.getTotalCost(), "€ ").replace(",", "."));

    assertTrue(averageSpeed > 0);
    assertTrue(energyDelivered > 0);
    assertTrue(totalCost > 0);
    base.getUser().getChargeSession().setEnergyDelivered(energyDelivered);
    base.getUser().getChargeSession().setTotalCost(totalCost);
  }

  @When("user clicks Done on Charge summary page")
  public void iClickDoneOnChargeSummaryPage() {
    chargeSummaryPage.clickDone();
    new MapPage(base.getDriver()).waitForRendered();
  }
}
