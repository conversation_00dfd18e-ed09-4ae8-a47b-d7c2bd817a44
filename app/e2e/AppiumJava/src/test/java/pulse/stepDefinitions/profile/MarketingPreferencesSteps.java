package pulse.stepDefinitions.profile;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.apache.commons.lang3.tuple.Pair;
import pages.profile.MarketingPreferencesPage;
import pulse.utils.BaseUtils;

import java.util.List;

import static org.testng.Assert.*;

public class MarketingPreferencesSteps extends BaseUtils {
  BaseUtils base;

  private final MarketingPreferencesPage marketingPreferencesPage;
  private final List<Pair<String, Boolean>> marketingPrefsItems;

  public MarketingPreferencesSteps(BaseUtils base) {
    super();
    this.base = base;
    this.marketingPreferencesPage = new MarketingPreferencesPage(base.getDriver());
    this.marketingPrefsItems = base.getUser().getMarketingPreferences();
  }

  @Then("the Marketing preferences page is displayed")
  public void theMarketingPreferencesPageIsDisplayed() {
    assertEquals(marketingPreferencesPage.getTextPageTitle(), "Marketing preferences");
  }

  @When("preference {string} is selected")
  public void preferenceIsClicked(String value) {
    boolean isPrefChecked = marketingPreferencesPage.isPrefsSelected(value);
    marketingPreferencesPage.selectPrefs(value);
    assertEquals(marketingPreferencesPage.isPrefsSelected(value), !isPrefChecked);  //checkbox gets oposite value
    marketingPrefsItems.add(Pair.of("Email", !isPrefChecked));
  }

  @When("Save preference is clicked")
  public void savePreferenceIsClicked() {
    marketingPreferencesPage.clickSave();
  }

  @Then("Marketing preferences {string} gets saved")
  public void marketingPreferencesGetsSaved(String value) {
    for (Pair<String, Boolean> pref : marketingPrefsItems) {
      if (pref.getKey().equals(value)) {
        assertEquals(pref.getValue(), marketingPreferencesPage.isPrefsSelected(value), "Preference '" + pref.getKey() + "' has not changed");
        break;
      }
    }
  }

}
