package pulse.stepDefinitions.profile;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.profile.LanguagePage;
import pulse.utils.BaseUtils;

import java.util.List;

public class LanguageSteps extends BaseUtils {

  BaseUtils base;

  private final LanguagePage languagePage;

  public LanguageSteps(BaseUtils base) {
    super();
    this.base = base;
    languagePage = new LanguagePage(base.getDriver());
  }

  @Then("the language screen is displayed")
  public void clickTheDefaultEnglishLanguageOption() {
    languagePage.englishLanguageButtonIsDisplayed();
  }

  @Then("the update language button is displayed")
  public void theUpdateLanguageButtonIsDisplayed() {
    languagePage.updateLanguageButtonIsDisplayed();
  }

  @And("selecting the chosen language")
  public void selectTheChosenLanguageButton(List<String> countries) {
    countries.forEach(languagePage::clickLanguageButtonIsDisplayedForCountry);
  }

  @When("clicking the update language button")
  public void clickUpdateLanguageButton() {
    languagePage.clickUpdateLanguageButton();
  }
}
