package pulse.stepDefinitions.logins;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.DefaultCardPage;
import pulse.utils.BaseUtils;

import static org.testng.Assert.*;

public class DefaultCardSteps extends BaseUtils {
    BaseUtils base;

  private final DefaultCardPage defaultCardPage;
  String nonDefaultCard;

  public DefaultCardSteps(BaseUtils base) {
    super();
    this.base = base;
    defaultCardPage = new DefaultCardPage(base.getDriver());
  }

  @Then("Default card page is displayed")
  public void defaultCardPageIsDisplayedd() {
    assertEquals(defaultCardPage.getTextPageTitle(), "Default card");
  }

  @When("user select none default card")
  public void userChangesTheDefaultCard() {
    nonDefaultCard = base.getUser().getCards().get(1);
    defaultCardPage.selectDefaultCard(nonDefaultCard);
  }

  @When("user clicks Cancel on Default card page")
  public void userClicksCancelOnDefaultCardPage() {
    defaultCardPage.clickCancel();
  }

  @When("user clicks Save on Default card page")
  public void saveDefaultCard() {
    defaultCardPage.clickSave();
    base.getUser().setDefaultCard(nonDefaultCard);
  }

  @Then("the Save button on Default card page is disabled")
  public void theSaveButtonOnDefaultCardPageGetsDisabled() {
    assertTrue(defaultCardPage.isSaveButtonDisabled());
  }

  @Then("the Save button on Default card page is enabled")
  public void theSaveButtonOnDefaultCardPageIsEnabled() {
    assertTrue(defaultCardPage.isSaveButtonEnabled());
  }


}
