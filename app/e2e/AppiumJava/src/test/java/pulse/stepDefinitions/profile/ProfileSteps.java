package pulse.stepDefinitions.profile;

import io.cucumber.java.en.*;
import pages.offersmfe.OffersAndPromotionsPage;
import pages.profile.ProfilePage;
import pages.wallet.ModalPage;
import pulse.utils.BaseUtils;

import static org.testng.Assert.*;

public class ProfileSteps extends BaseUtils {
  BaseUtils base;
  private final ProfilePage profilePage;
  private final OffersAndPromotionsPage offersAndPromotionsPage;

  public ProfileSteps(BaseUtils base) {
    super();
    this.base = base;
    this.profilePage = new ProfilePage(base.getDriver());
    this.offersAndPromotionsPage = new OffersAndPromotionsPage(base.getDriver());
  }

  @When("the Settings button is clicked")
  public void clickTheSettingsButton() {
    profilePage.clickSettingsButton();
  }

  @When("user taps the Payment methods")
  public void iTapOnPaymentDetails() {
    profilePage.clickPaymentDetails();
  }

  @When("Recent transactions are selected")
  public void iTapOnRecentTransactions() {
    profilePage.clickRecentTransactions();
  }

  @When("user logs out")
  public void userLogsOut() {
    profilePage.swipeUp();
    profilePage.clickLogOut();
    ModalPage modalPage;
    modalPage = new ModalPage(base.getDriver(), "Logout");
    modalPage.clickButton("Yes, log out");
  }

  @When("clicking the Personal information")
  public void openingThePersonalInformation() {
    profilePage.clickPersonalInformation();
  }

  @When("clicking the Create an account button")
  public void clickingTheCreateAnAccountButton() {
    profilePage.clickCreateAccount();
  }

  @When("clicking the Log in button")
  public void clickingTheLoginButton() {
    profilePage.clickLoginButton();
  }

  @When("clicking on the offers and promotions button")
  public void clickingTheOffersAndPromotionsButton() {
    profilePage.clickOffersAndPromotionsButton();
  }

  @When("going back to the profile screen")
  public void goingBackToTheProfileScreen() {
    offersAndPromotionsPage.clickBackButton();
    assertTrue(profilePage.loginToCreateAnAccountButtonIsDisplayed());
  }

  @And("is shown as logged in")
  public void shownAsLoggedIn() {
    profilePage.profilePageIsLoggedIn();
  }

  @And("the user name is displayed on the Profile page")
  public void theUserNameIsDisplayedOnOnProfilePage() {
    String hiUser = "Hi " + base.getUser().getFirstName();
    if (hiUser.length() > 17) { //Long string is ellipsis
      hiUser = hiUser.substring(0, 17) + "...";
    }
    assertEquals(profilePage.getTextUserFirstName(), hiUser);
  }

  @And("the log in to create an account button is displayed")
  public void theLoginToCreateAnAccountButtonIsDisplayed() {
    assertTrue(profilePage.loginToCreateAnAccountButtonIsDisplayed());
  }

  @Then("the want to add a offer login screen is displayed")
  public void theWantToAddAOfferLoginScreenIsDisplayed() {
    assertTrue(offersAndPromotionsPage.wantToAddAnOfferTitleIsDisplayed());
  }

  @Then("a Guest user is on the Profile page")
  public void aGuestIsOnTheProfilePage() {
    assertEquals(profilePage.getTextPageTitle(), "Profile");
    assertTrue(profilePage.loginToCreateAnAccountButtonIsDisplayed());
  }

  @Then("the offers and promotions button is visible")
  public void theOffersAndPromotionsWidgetIsVisible() {
    assertTrue(profilePage.offersAndPromotionsButtonIsDisplayed());
  }

  @Then("the Profile page is displayed")
  public void iAmOnTheProfilePage() {
    assertEquals(profilePage.getTextPageTitle(), "Profile");
  }

  @Then("the Profile page with Log in button is displayed")
  public void theProfilePageWithLogInButtonIsDisplayed() {
    assertEquals(profilePage.getTextPageTitle(), "Profile");
    assertTrue(profilePage.loginToCreateAnAccountButtonIsDisplayed());
  }

  @Then("the subscribe and save option is displayed")
  public void theSubscribeAndSaveOptionIsDisplayed() {
    assertTrue(profilePage.subscribeAndSaveOptionIsDisplayed());
  }

  @Then("the subscribe and save option is not displayed")
  public void theSubscribeAndSaveOptionIsNotDisplayed() {
    profilePage.subscribeAndSaveOptionIsNotDisplayed();
  }

  @Then("the offers and promotions new widget is not displayed")
  public void theOffersAndPromotionsNewWidgetIsNotDisplayed() {
    assertFalse(profilePage.offersAndPromotionsNewWidgetIsDisplayed());
  }

  @When("My subscription option is displayed")
  public void mySubscriptionOptionIsDisplayed() {
    assertTrue(profilePage.mySubscriptionIsDisplayed());
  }

  @When("My subscription option is not displayed")
  public void mySubscriptionOptionIsNotDisplayed() {
    assertFalse(profilePage.mySubscriptionIsNotDisplayed());
  }

  @Then("the user opens My subscription")
  public void theUserOpensMySubscription() {
    profilePage.clickMySubscription();
  }

  @Then("the user clicks on subscribe and save")
  public void theUserClicksOnSubscribeAndSave() {
    profilePage.clickSubscribeAndSave();
  }
}
