package pulse.stepDefinitions.wallet;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.CardInformationPage;
import pulse.utils.BaseUtils;
import utils.Pojo.CreditCard;
import utils.JsonManager;
import utils.StringUtilities;

import java.util.List;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class CardInformationSteps extends BaseUtils {
  BaseUtils base;
  public CardInformationPage cardInformationPage;

  public CardInformationSteps(BaseUtils base) {
    super();
    this.base = base;
    this.cardInformationPage = new CardInformationPage(base.getDriver());
  }

  @Then("the Card information page is displayed")
  public void iAmOnTheCardInformationPage() {
    assertEquals(cardInformationPage.getTextPageTitle(), "Card information");
  }

  @When("user fills in valid card information add save")
  public void iFillInSomeCardInformationAddSave() {
    List<CreditCard> testCreditCards = JsonManager.getTestCards();
    List<String> existingCards = base.getUser().getCards();

    assertTrue(existingCards.size() < testCreditCards.size(),
      "Cannot add another card, all available test cards has been added");

    CreditCard cardToBeAded = JsonManager.getSomeCreditCard(testCreditCards);

    boolean tryAgain = false;
    if (!base.isAndroid() && cardToBeAded.isSecured()) {
      //For an ios we can add only cards that are not secured.
      // The 'Secure checkout' page cannot be displayed for iOS in SauceLabs
      // It is not possible to bypass 'Screen recording detected' banner for ios

      int unsecuredCardsCount = 0;
      List<CreditCard> existingCardsObjects = StringUtilities.cardsToObjects(existingCards, testCreditCards);
      for (CreditCard card : existingCardsObjects) {
        if (!card.isSecured()) {
          unsecuredCardsCount++;
        }
      }
      assertTrue(unsecuredCardsCount < StringUtilities.getUnsecuredCardsCount(testCreditCards),
        "Cannot add another card, there are no more unsecured test cards available for an ios");
      tryAgain = true;
    }

    //make sure we don't add existing card or secure card for an ios
    while (existingCards.contains(StringUtilities.getNameAndLastFour(cardToBeAded)) || tryAgain) {
      cardToBeAded = JsonManager.getSomeCreditCard(testCreditCards);
      if (!base.isAndroid() && cardToBeAded.isSecured()) {
        tryAgain = true;
      } else {
        tryAgain = false;
      }
    }

    cardInformationPage.enterCardHolderName(cardToBeAded.getHolderName());
    assertEquals(cardInformationPage.getCardHolderName(), cardToBeAded.getHolderName());

    cardInformationPage.enterCardNumber(cardToBeAded.getCardNumber());
    assertEquals(cardInformationPage.getCardNumber(), cardToBeAded.getCardNumber());

    cardInformationPage.enterCvv(cardToBeAded.getSecurityCode());
    assertEquals(cardInformationPage.getCvv(), cardToBeAded.getSecurityCode());

    cardInformationPage.enterMonth(cardToBeAded.getExpiryMonth());
    assertEquals(cardInformationPage.getMonth(), cardToBeAded.getExpiryMonth());

    cardInformationPage.enterYear(cardToBeAded.getExpiryYear());
    assertEquals(cardInformationPage.getYear(), cardToBeAded.getExpiryYear());
    cardInformationPage.hideKeyboard();
    if (existingCards.size() > 0) {
      cardInformationPage.swipeUp();
      cardInformationPage.setDefault();
      if (base.isAndroid()) {// not possible to check on ios
        assertTrue(cardInformationPage.isCheckboxDefaultChecked());
      }
    }

    cardInformationPage.clickSave();
    base.getUser().setNewCard(cardToBeAded);
  }
}
