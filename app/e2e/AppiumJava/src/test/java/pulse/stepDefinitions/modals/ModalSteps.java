package pulse.stepDefinitions.modals;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.ModalPage;
import pulse.utils.BaseUtils;

public class ModalSteps extends BaseUtils {
    BaseUtils base;

  private ModalPage modalPage;

  public ModalSteps(BaseUtils base) {
    super();
    this.base = base;
  }

  @Then("the Processing card detail modal is displayed")
  public void theProcessingCardDetailModalIsDisplayed() {
    modalPage = new ModalPage(base.getDriver(),"Processing card details");
  }

  @When("user clicks Continue on Processing modal")
  public void iClickContinueOnProcessingModal() {
    modalPage.clickButton("Continue");
  }
}
