package pulse.stepDefinitions.wallet;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.SecureCheckoutPage;
import pulse.utils.BaseUtils;
import utils.StringUtilities;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class SecureCheckoutSteps extends BaseUtils {
    BaseUtils base;
  SecureCheckoutPage secureCheckoutPage;

  public SecureCheckoutSteps(BaseUtils base) {
    super();
    this.base = base;
    this.secureCheckoutPage = new SecureCheckoutPage(base.getDriver());
  }

  @Then("the Secure Checkout page is displayed")
  public void iAmOnTheSecureCheckoutPage() {
    if (base.getUser().getNewCard().isSecured()) {
      assertEquals(secureCheckoutPage.getTextTitle().toLowerCase(), "SECURE CHECKOUT".toLowerCase());
      String cardNumber = base.getUser().getNewCard().getCardNumber();
      String lastFourExpected = StringUtilities.getLastChars(cardNumber, 4);
      assertTrue(secureCheckoutPage.getTextInfo().contains("********"+lastFourExpected));
    }
  }

  @When("user enters the SMS code for 3DS card and submits")
  public void iEnterTheSMSCodeAndSubmit() {
    if (base.getUser().getNewCard().isSecured()) {
      String code = "1234";
      secureCheckoutPage.enterCode(code);
      assertEquals(secureCheckoutPage.getCode(), code);
      secureCheckoutPage.hideKeyboard();
      secureCheckoutPage.clickSubmit();
    }
  }

}
