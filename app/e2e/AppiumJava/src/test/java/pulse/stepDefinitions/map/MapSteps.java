package pulse.stepDefinitions.map;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.openqa.selenium.WebElement;
import pages.mapmfe.FavouritesPromptPage;
import pages.mapmfe.MapPage;
import pages.mapmfe.MapSearchPage;
import pulse.stepDefinitions.AppSteps;
import pulse.utils.BaseUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.testng.Assert.*;

public class MapSteps extends BaseUtils {
  BaseUtils base;

  MapPage mapPage;
  FavouritesPromptPage favouritesPromptPage;
  MapSearchPage mapSearchPage;
  List<WebElement> clusters;
  List<WebElement> chargePoints = new ArrayList<>();

  public MapSteps(BaseUtils base) {
    super();
    this.base = base;
    this.mapPage = new MapPage(base.getDriver());
    this.favouritesPromptPage = new FavouritesPromptPage(base.getDriver());
    this.mapSearchPage = new MapSearchPage(base.getDriver());
  }

  @When("entering the search {string}")
  public void whenEnteringTheSearchField(String arg0) {
    new AppSteps(base).userTapsMapIcon();
    mapSearchPage.enterSearchTerm(arg0);
  }

  @When("selecting the Favourites button")
  public void selectingTheFavouritesButtonAsAGuest() {
    mapPage.clickFavouritesButton();
  }

  @Then("the save favourites charging locations prompt is displayed")
  public void theSaveFavouritesChargingLocationsPromptIsDisplayed() {
    assertEquals(favouritesPromptPage.favouritesPromptIsDisplayed(), "Log in", "favouritesPromptLoginButton Element is not present on the page");
  }

  @Then("the save favourites charging locations prompt can be closed")
  public void theFavouritesPromptCanBeClosed() {
    favouritesPromptPage.clickFavouritesCloseButton();
  }

  @Then("the Favourites Login button is not displayed")
  public void theFavouritesButtonIsNotDisplayed() {
    favouritesPromptPage.loginButtonIsNotDisplayed();
  }

  @When("the nearby charging locations menu is displayed")
  public void theNearbyChargingLocationMenuIsDisplayed() {
    mapPage.theChargingLocationsMenuIsDisplayed();
  }

  @Then("the user can open the nearby charging locations menu")
  public void theUserCanOpenTheNearbyChargingLocationMenu() {
    mapPage.swipeUpTheChargingLocationMenu();
  }

  @When("selecting the first option")
  public void theFirstOptionCanBeSelectedInTheChargingLocationMenu() {
    mapPage.clickChargeLocation(0);
  }

  @When("selecting the ‘Log in or Create’ an account button")
  public void theLoginButtonCanBeSelected() {
  }

  @Then("the map screen is displayed")
  public void theMapIsDisplayed() {
    mapPage.waitForRendered();
  }

  @Then("the list of results is returned in the dropdown menu")
  public void theListOfResultsIsReturnedInTheDropdownMenu() {
    assertFalse(mapSearchPage.getSearchResults().isEmpty());
  }

  @And("{string} from the area {string} is selected")
  public void fromTheAreaCanBeSelected(String location, String areaCode) {
    WebElement result = mapSearchPage.getSearchItem(location, areaCode);
    assertNotNull(result);
    result.click();
  }

  @Then("clusters on the map are displayed")
  public void clustersAreDisplayed() {
    File clusterRefImage = new File("src/test/resources/images/cluster.png");
    clusters = mapPage.getMapItems(clusterRefImage, 55, 55);
    int tries = 0;
    while (clusters.size() < 1 && tries < 3) {
      mapPage.reRenderMap();
      clusters = mapPage.getMapItems(clusterRefImage, 55, 55);
      tries++;
    }
    assertTrue(clusters.size() > 0, "No clusters has been found on the map");
  }

  @When("selecting the cluster")
  public void selectingCluster() {
    if (mapPage.isAndroid) {
      mapPage.doubleTap(clusters.get(0));
    } else {
      clusters.get(0).click();
    }
  }

  @Then("charge points {string} are displayed on the map")
  public void chargePointsAreDisplayedOnTheMap(String value) {
    chargePoints = new ArrayList<>();
    chargePoints.addAll(mapPage.getChargePoints(value));
    chargePoints.addAll(mapPage.getFavouritesChargePoints(value));
    chargePoints.addAll(mapPage.getChargePointBubles(value));
    int tries = 0;
    while (chargePoints.size() < 1 && tries < 3) {
      chargePoints.addAll(mapPage.getChargePoints(value));
      chargePoints.addAll(mapPage.getFavouritesChargePoints(value));
      mapPage.reRenderMap();
      tries++;
    }
    assertFalse(chargePoints.isEmpty());
  }

  @When("selecting charge point on the map")
  public void selectingChargePointOnTheMap() {
    //Sometimes CP doesn't open. The workaround is to click on more places of that CP until it opens
    int step = 0;
    WebElement chargePointToSelect = chargePoints.get(0);
    int x = chargePointToSelect.getLocation().getX();
    int y = chargePointToSelect.getLocation().getY();
    while (mapPage.isElementPresent(chargePointToSelect) && step < 50) {
      mapPage.tapCoordinates(x + step, y + step);
      step = step + 10;
      mapPage.waitMilliseconds(3000);
    }
  }

  @When("clicking on the current location icon")
  public void cickingOnCurrentLocationIcon() {
    mapPage.clickCurrentLocation();
  }

  @When("clicking the quick filter button on the Map page")
  public void clickingTheQuickFilterButtonOnTheMapPage() {
    mapPage.clickFilterButton();
  }

  @Then("the All connector buttons starts with {string}")
  public void theAllConnectorButtonsStartsWith(String value) {
    assertTrue(mapPage.getAllConnectorButtonName().startsWith(value));
  }

  @Then("only {string} favourite charge points are displayed on the map")
  public void onlyFavouriteChargePointsAreDisplayedOnTheMap(String value) {
    assertTrue(mapPage.getChargePoints(value).size() == 0);
    assertTrue(mapPage.getFavouritesChargePoints(value).size() > 0);
  }
}
