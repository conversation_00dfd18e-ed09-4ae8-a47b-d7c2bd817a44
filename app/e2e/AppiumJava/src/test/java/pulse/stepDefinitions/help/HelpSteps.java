package pulse.stepDefinitions.help;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import pages.help.HelpPage;
import pages.help.WhereAreYouChargingHelpPage;
import pulse.utils.BaseUtils;

import java.util.List;

import static org.testng.AssertJUnit.assertTrue;

public class HelpSteps extends BaseUtils {
    BaseUtils base;

  private final WhereAreYouChargingHelpPage whereAreYouChargingHelpPage;
  private final HelpPage helpPage;

  public HelpSteps(BaseUtils base) {
    super();
    this.base = base;
    this.whereAreYouChargingHelpPage = new WhereAreYouChargingHelpPage(base.getDriver());
    this.helpPage = new HelpPage(base.getDriver());
  }

  @Then("the where are you charging screen is displayed")
  public void theWhereAreYouChargingPageIsDisplayed() {
    whereAreYouChargingHelpPage.verifyWhereAreYouChargingHelpPage();
  }

  @And("clicking the show help page button")
  public void clickingTheShowHelpPageButton() {
    whereAreYouChargingHelpPage.clickShowHelpPageButton();
  }

  @Then("the countries help screen is displayed")
  public void theCountriesHelpScreenIsDisplayed(String country) {
    assertTrue(helpPage.telNumberIsDisplayedForCountry(country));
  }

  @And("they select I'm charging in")
  public void selectImChargingIn(List<String> countries) {
    countries.forEach(whereAreYouChargingHelpPage::selectGuestChargingHelpForCountry);
  }
}
