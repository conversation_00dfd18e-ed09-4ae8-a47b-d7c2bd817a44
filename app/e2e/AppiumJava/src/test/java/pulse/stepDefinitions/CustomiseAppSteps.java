package pulse.stepDefinitions;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.registrationmfe.AddEmailAddressPage;
import pages.registrationmfe.CustomiseAppPage;
import pages.registrationmfe.VerifyYourEmailPage;
import pulse.utils.BaseUtils;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class CustomiseAppSteps extends BaseUtils {
  BaseUtils base;

  private final CustomiseAppPage customiseAppPage;
  private final AddEmailAddressPage addEmailAddressPage;
  private final VerifyYourEmailPage verifyYourEmailPage;

  public CustomiseAppSteps(BaseUtils base) {
    super();
    this.base = base;
    this.customiseAppPage = new CustomiseAppPage(base.getDriver());
    this.addEmailAddressPage = new AddEmailAddressPage(base.getDriver());
    this.verifyYourEmailPage = new VerifyYourEmailPage(base.getDriver());
  }

  @Then("customise the bp pulse app page opens")
  public void customiseTheBpPulseAppPageOpens() {
    assertTrue(customiseAppPage.switchContext("NATIVE_APP"));
    assertEquals(customiseAppPage.getTextPageTitle(), base.getTranslation("customizeApp"));
  }

  @When("user selects the country on Customise page")
  public void userSelectsTheCountryOnCustomisePage() {
    String brand = getBrand();
    if (!brand.equals("bpUS")) {
      if (base.isAndroid()) {
        if (!customiseAppPage.getSelectedCoutry().contains(base.getUser().getCountry())) {
          customiseAppPage.selectCountry(base.getUser().getCountry());
        }
        assertTrue(customiseAppPage.getSelectedCoutry().contains(base.getUser().getCountry()), "The selected country " + customiseAppPage.getSelectedCoutry() + " not found");
      } else {//Unable to verify selected country on iOS
        customiseAppPage.selectCountry(base.getUser().getCountry());
      }
    }
  }

  @When("user enters the first name on Customise page")
  public void iEnterTheFirstNameOnCustomisePage() {
    customiseAppPage.enterFirstName(base.getUser().getFirstName());
    assertEquals(customiseAppPage.getFirstName(), base.getUser().getFirstName());
  }

  @And("user enters the last name on Customise page")
  public void iEnterTheLastNameOnCustomisePage() {
    customiseAppPage.enterlastName(base.getUser().getLastName());
    assertEquals(customiseAppPage.getLastName(), base.getUser().getLastName());
    customiseAppPage.hideKeyboard();
  }

  @And("user checks I have read terms and conditions")
  public void iCheckIHaveReadTermsAndConditions() {
    customiseAppPage.clickTermsAndCond();
    if (base.isAndroid()) {
      assertTrue(customiseAppPage.isTermsAndCondChecked());
    }
  }

  @And("user clicks Jump right in")
  public void iClickJumpRightIn() {
    customiseAppPage.clickJump();
//    ModalPage modalPage = new ModalPage(base.getDriver());
//    if (modalPage.isElementPresent(modalPage.getTextPageTitle(), 40000)) {
//      modalPage.waitMilliseconds(1000);
//      new ModalPage(base.getDriver(), "Unable to complete set-up").clickButton("Try again");
//    }
  }

  @Then("Add email address page opens")
  public void addEmailAddressPageOpens() {
    assertTrue(addEmailAddressPage.switchContext("WEBVIEW"));
    assertEquals(addEmailAddressPage.getTextPageTitle(), "Add email address");
  }

  @When("user enters email on Add email address page")
  public void iEnterUsersEmailOnAddEmailAddressPage() {
    String email = base.getUser().getEmail();
    base.getUser().setEmail(email);
    addEmailAddressPage.enterEmail(email);
    addEmailAddressPage.clickContinue();
  }

  @And("I click Send verification link")
  public void iClickSendVerificationLink() {
    addEmailAddressPage.clickContinue();
  }

  @Then("verify your email page opens")
  public void verifyYourEmailPageOpens() {
    assertTrue(addEmailAddressPage.switchContext("WEBVIEW"));
    assertEquals(verifyYourEmailPage.getTextPageTitle(), "Verify your email");
    assertTrue(verifyYourEmailPage.switchContext("NATIVE_APP"));
  }

}
