package pulse.stepDefinitions.wallet;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.AddYourPaymentMethodPage;
import pulse.utils.BaseUtils;
import utils.JsonManager;
import utils.Pojo.CreditCard;
import utils.Pojo.User;
import utils.StringUtilities;

import java.util.List;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class AddYourPaymentMethodSteps extends BaseUtils {
  private BaseUtils base;
  private AddYourPaymentMethodPage addYourPaymentMethodPage;
  private CreditCard cardToBeAded;
  private List<CreditCard> testCreditCards;

  private User user;

  public AddYourPaymentMethodSteps(BaseUtils base) {
    super();
    this.base = base;
    this.addYourPaymentMethodPage = new AddYourPaymentMethodPage(base.getDriver());
    this.user = base.getUser();
    this.testCreditCards = JsonManager.getTestCards();
    this.cardToBeAded = JsonManager.getSomeCreditCard(testCreditCards);
  }

  @Then("Add your payment method page is displayed")
  public void AddYourPaymentMethodIsDisplayed() {
    assertTrue(addYourPaymentMethodPage.switchContext("WEBVIEW"));
    assertEquals(addYourPaymentMethodPage.getTextPageTitle(), "Add your payment method");
  }

  @When("entering valid credit card number")
  public void userEntersValidCreditCardNumber() {
    List<String> existingCards = base.getUser().getCards();
    assertTrue(existingCards.size() < testCreditCards.size(), "Cannot add another card, all cards from testData/cards.json are added");

    boolean tryAgain = false;  //
    if (!base.isAndroid() && cardToBeAded.isSecured()) {
      //For an ios we can add only cards that are not secured.
      // The 'Secure checkout' page cannot be displayed for iOS in SauceLabs, it is not possible to bypass 'Screen recording detected' banner for ios

      int unsecuredCardsCount = 0;
      List<CreditCard> existingCardsObjects = StringUtilities.cardsToObjects(existingCards, testCreditCards);
      for (CreditCard card : existingCardsObjects) {
        if (!card.isSecured()) {
          unsecuredCardsCount++;
        }
      }
      assertTrue(unsecuredCardsCount < StringUtilities.getUnsecuredCardsCount(testCreditCards), "Cannot add another card, there are no more unsecured test cards available for an ios");
      tryAgain = true;
    }

    //make sure we don't add existing card or secure card for an ios
    while (existingCards.contains(StringUtilities.getNameAndLastFour(cardToBeAded)) || tryAgain) {
      cardToBeAded = JsonManager.getSomeCreditCard(testCreditCards);
      if (!base.isAndroid() && cardToBeAded.isSecured()) {
        tryAgain = true;
      } else {
        tryAgain = false;
      }
    }

    addYourPaymentMethodPage.enterCardNumber(cardToBeAded.getCardNumber());
    // Bug 7455311
    assertEquals(addYourPaymentMethodPage.getCardNumber().replace(" ", ""), cardToBeAded.getCardNumber().replace(" ", ""));
  }

  @And("entering the Expiry month")
  public void enteringExpiryMonth() {
    addYourPaymentMethodPage.enterMonth(cardToBeAded.getExpiryMonth());
    assertEquals(addYourPaymentMethodPage.getMonth(), cardToBeAded.getExpiryMonth());
  }

  @And("entering the Expiry year")
  public void enteringTheExpiryYear() {
    addYourPaymentMethodPage.enterYear(cardToBeAded.getExpiryYear());
    assertEquals(addYourPaymentMethodPage.getYear(), cardToBeAded.getExpiryYear());
  }

  @And("entering the CVV")
  public void enteringTheCVV() {
    addYourPaymentMethodPage.enterCvv(cardToBeAded.getSecurityCode());
    assertEquals(addYourPaymentMethodPage.getCvv(), cardToBeAded.getSecurityCode());
  }

  @And("entering the Card holders name")
  public void enteringTheCardHoldersName() {
    addYourPaymentMethodPage.enterCardHolderName(cardToBeAded.getHolderName());
    assertEquals(addYourPaymentMethodPage.getCardHolderName(), cardToBeAded.getHolderName());
  }

  @And("entering the Street and house name")
  public void enteringTheStreetAndHouseName() {
    addYourPaymentMethodPage.enterStreetHouse(user.getStreet());
    assertEquals(addYourPaymentMethodPage.getStreetHouse(), user.getStreet());
  }

  @And("entering the Town or city name")
  public void enteringTheTown() {
    addYourPaymentMethodPage.enterTown(user.getTown());
    assertEquals(addYourPaymentMethodPage.getTown(), user.getTown());
  }

  @And("entering the Postal or zip code")
  public void enteringThePostalOrZipCode() {
    addYourPaymentMethodPage.enterPostalOrZipCode(user.getPostalCode());
    assertEquals(addYourPaymentMethodPage.getPostalOrZipCode(), user.getPostalCode());
  }

  @And("entering the Mobile number")
  public void enteringTheMobileNumber() {
    String phoneNumber = user.getPhoneNumber();
    addYourPaymentMethodPage.enterMobileNumber(phoneNumber);
    assertEquals(addYourPaymentMethodPage.getMobileNumber(), phoneNumber);
  }

  @And("entering the Email address")
  public void enteringTheEmailAddress() {
    addYourPaymentMethodPage.enterEmailAddress(user.getEmail());
    assertEquals(addYourPaymentMethodPage.getEmailAddress(), user.getEmail());
  }

  @And("clicking Continue to card authorisation")
  public void clickingContinueToCardAuthorisation() {
    addYourPaymentMethodPage.clickContinue();
    assertTrue(addYourPaymentMethodPage.switchContext("NATIVE_APP"));
    base.getUser().setNewCard(cardToBeAded);
  }

  @And("selecting the Counry")
  public void selectingTheCounry() {
    assertEquals(StringUtilities.getStringAfter(addYourPaymentMethodPage.getCountry(), ", "), user.getCountryCode());
  }

  @And("selecting the phone prefix")
  public void selectingThePhonePrefix() {
    String phonePrefix = StringUtilities.substringBetween(addYourPaymentMethodPage.getPhonePrefix(), "(", ")");
    if (!phonePrefix.equals(user.getPhonePrefix())) {
      addYourPaymentMethodPage.selectPhonePrefix(user.getPhonePrefix());
      assertEquals(StringUtilities.substringBetween(addYourPaymentMethodPage.getPhonePrefix(), "(", ")"), user.getPhonePrefix());
    }
  }
}
