package pulse.stepDefinitions.wallet;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.ContactInformationPage;
import pulse.utils.BaseUtils;
import utils.Pojo.User;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class ContactInformationSteps extends BaseUtils {
  BaseUtils base;

  private final ContactInformationPage contactInformationPage;

  public ContactInformationSteps(BaseUtils base) {
    super();
    this.base = base;
    this.contactInformationPage = new ContactInformationPage(base.getDriver());
  }

  @Then("the Contact information page is displayed")
  public void iAmOnTheContactInformationPage() {
    assertEquals(contactInformationPage.getTextPageTitle(), "Contact information");
  }

  @When("user enters contact information and save")
  public void enterContactInformationAndSave() {
    User user = base.getUser();
    String phoneNumber = user.getPhoneNumber();
    if (phoneNumber == null || phoneNumber.isEmpty()) {//user has no phone number set in the user's json file
      phoneNumber = "01234567";
    }
    contactInformationPage.enterPhoneNumber(phoneNumber);
    assertEquals(contactInformationPage.getPhoneNumber(), phoneNumber);

    contactInformationPage.enterEmail(user.getEmail());
    contactInformationPage.hideKeyboard();
    assertEquals(contactInformationPage.getEmail(), user.getEmail());

    contactInformationPage.clickSave();
    assertTrue(contactInformationPage.switchContext("NATIVE_APP"));
    contactInformationPage.closeSaveCardModal();
  }
}
