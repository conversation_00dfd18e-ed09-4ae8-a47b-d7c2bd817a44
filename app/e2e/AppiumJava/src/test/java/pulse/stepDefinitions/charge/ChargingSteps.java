package pulse.stepDefinitions.charge;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.chargemfe.ChargingPage;
import pages.navigation.BottomTabNavigatorPage;
import pages.wallet.ModalPage;
import pulse.utils.BaseUtils;
import utils.StringUtilities;

import java.time.Duration;
import java.time.Instant;

import static org.testng.Assert.*;

public class ChargingSteps extends BaseUtils {
  BaseUtils base;

  private final ChargingPage chargingPage;
  private int chargedPercent = 0;
  private final ModalPage modalPage;

  public ChargingSteps(BaseUtils base) {
    super();
    this.base = base;
    chargingPage = new ChargingPage(base.getDriver());
    this.modalPage = new ModalPage(base.getDriver());
  }

  @Then("Charging page is displayed with the actual status")
  public void chargingPageIsDisplayed() {
    if (modalPage.isDisplayed("use Location Accuracy")) {
      modalPage.clickButton("No, thanks");
    }
    assertEquals(chargingPage.getPageTitle(), "Charging");
    if (base.isAndroid()) { //cannot get selector for ios
      assertTrue(new BottomTabNavigatorPage(base.getDriver()).chargeButtonIsCharging());
    }

    Instant startTime = Instant.now();
    while (Duration.between(startTime, Instant.now()).toSeconds() < 120) {
      chargedPercent = chargingPage.getChargingPercentValue();
      if (chargedPercent >= 23) {//charged more than this number%
        chargedPercent = chargingPage.getChargingPercentValue();
        break;
      }
    }

    int charged_kW;
    float energyConsumed;
    float approximatePrice;
    if (base.isAndroid()) {
      charged_kW = Integer.parseInt(chargingPage.getChargerSpeed().replace("kW", "").trim());
      energyConsumed = Float.parseFloat(chargingPage.getEnergyConsumed().replace("kWh", "").trim());
      approximatePrice = Float.parseFloat(chargingPage.getApproximatePrice().replace("€", "").replace(",", ".").replace(" ", "").trim());
    } else {
      charged_kW = Integer.parseInt(StringUtilities.getStringBefore(chargingPage.getChargerSpeed(), " kW"));
      energyConsumed = Float.parseFloat(StringUtilities.getStringBefore(chargingPage.getEnergyConsumed(), "  kWh"));
      approximatePrice = Float.parseFloat(StringUtilities.getStringAfter(chargingPage.getApproximatePrice(), " ").replace(",", "."));
    }
    assertTrue(charged_kW > 0, "charged_kW: " + charged_kW);
    assertTrue(energyConsumed > 0, "energyConsumed: " + energyConsumed);
    assertTrue(approximatePrice > 0, "approximatePrice: " + approximatePrice);
  }

  @When("button Stop charging is clicked")
  public void stopCharging() {
    chargingPage.clickStopCharge();
  }

  @When("Stop charging is confirmed in the modal")
  public void stopChargingModal() {
    new ModalPage(base.getDriver(), "Are you sure you want to stop charging?").clickButton("Stop charging");
    base.getUser().getChargeSession().setStatus(chargedPercent);
  }

  @Then("No charging information is displayed")
  public void noInternet() {
    chargingPage.waitMilliseconds(3000);
    assertEquals(chargingPage.getChargerSpeed(), "--");
  }

  @Then("charging status is displayed")
  public void chargingStatusIsDisplayed() {
    chargingPage.waitMilliseconds(3000);
    assertTrue(chargingPage.getChargingPercentValue() > 0);
  }

}
