package pulse.stepDefinitions.webview;

import core.BrowserPage;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pulse.utils.BaseUtils;
import utils.Gmail_API;
import utils.StringUtilities;
import static org.junit.Assert.assertTrue;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;
import static utils.ConstantClass.COM_ANDROID_CHROME;
import static utils.ConstantClass.COM_ANDROID_MAIN;
import static utils.ConstantClass.CONTEXT.NATIVE_APP;

public class BrowserSteps extends BaseUtils {
  BaseUtils base;
  private final BrowserPage browserPage;
  private String verificationLink;

  public BrowserSteps(BaseUtils base) {
    super();
    this.base = base;
    browserPage = new BrowserPage(base.getDriver());
  }

  @Then("the verification link is sent to the users email")
  public void verificationLinkToMyEmail() {
    String email = StringUtilities.removeEmailPlusAddressing(base.getUser().getEmail(), "+", "@");
    Gmail_API gmail_API = new Gmail_API(email);
    String query = "from:BP Accounts Team <<EMAIL>> to:" + base.getUser().getEmail();
    String emailBody = gmail_API.readMail(query);
    String start = "<a href=\"";
    String end = "\" class=";

    assertNotNull(emailBody, "The email cannot be found: " + query);
    verificationLink = StringUtilities.substringBetween(emailBody, start, end);
    assertNotNull(verificationLink);
    verificationLink = StringUtilities.decodedUrl(verificationLink);
  }

  @When("I open webview menu")
  public void iOpenWebviewMenu() {
    assertTrue(browserPage.switchContext(NATIVE_APP.toString()));
    browserPage.clickBrowserMenu();
  }

  @And("I select open in a browser")
  public void iSelectOpenInABrowser() {
    browserPage.openInBrowser();
  }

  @And("user enters the verification link to the url input")
  public void iEnterVerificationLinkToTheUrlInput() {
    if (base.isAndroid()) {
      assertTrue(browserPage.switchContext(NATIVE_APP.toString()));
      String app = COM_ANDROID_CHROME + "/" + COM_ANDROID_MAIN;
      assertEquals(browserPage.startApplication(app), app);
      browserPage.clickNoThanksChromeNotifications();
      browserPage.enterUrl(verificationLink);
      String packageNm = browserPage.getPackageName();
      browserPage.clickContinue();
      browserPage.closeApp(packageNm);
    } else {
      base.getDriver().get(verificationLink);
    }
  }

}
