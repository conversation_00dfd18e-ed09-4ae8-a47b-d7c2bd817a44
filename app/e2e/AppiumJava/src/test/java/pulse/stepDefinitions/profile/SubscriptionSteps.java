package pulse.stepDefinitions.profile;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.chargemfe.SelectConnectorPage;
import pages.profile.CancelYourSubPage;
import pages.profile.MySubscriptionPage;
import pages.profile.SubscribeAndSavePage;
import pages.wallet.ModalPage;
import pulse.stepDefinitions.CommonSteps;
import pulse.utils.BaseUtils;

import static org.testng.Assert.*;

public class SubscriptionSteps extends BaseUtils {
  BaseUtils base;
  private final MySubscriptionPage mySubPage;
  private final CancelYourSubPage cancelMySubPage;
  private final SubscribeAndSavePage subscribeAndSave;
  public SelectConnectorPage selectConnectorPage;


  public SubscriptionSteps(BaseUtils base) {
    super();
    this.base = base;
    this.mySubPage = new MySubscriptionPage(base.getDriver());
    this.cancelMySubPage = new CancelYourSubPage(base.getDriver());
    this.subscribeAndSave = new SubscribeAndSavePage(base.getDriver());
    this.selectConnectorPage = new SelectConnectorPage(base.getDriver());
  }

  @When("My subscription page is displayed")
  public void mySubscriptionPageIsDisplayed() {
    assertEquals(mySubPage.getTextPageTitle(), "My subscription");
  }

  @Then("Cancel subscription is displayed")
  public void cancelSubscriptionIsDisplayed() {
    assertTrue(cancelMySubPage.cancelSubscriptionIsDisplayed());
  }

  @Then("the user cancels subscription")
  public void theUserCancelsSubscription() {
    cancelMySubPage.clickCancelSubscription();
  }

  @Then("the user selects loose my discount option")
  public void theUserSelectsLooseMyDiscountOption() {
    cancelMySubPage.clickLooseDiscount();
  }

  @Then("dialog with confirmation question is displayed")
  public void dialogWithConfirmationQuestionIsDisplayed() {
    new ModalPage(base.getDriver(), "Are you sure you want to cancel?").clickButton("Lose my discount");
  }

  @When("loose option is displayed")
  public void looseOptionIsDisplayed() {
    assertTrue(cancelMySubPage.btnLooseDiscountIsDisplayed());
  }

  @When("cancel subscription page is displayed")
  public void cancelSubscriptionPageIsDisplayed() {
    assertEquals(cancelMySubPage.getCancelPageTitle(), "Cancel your subscription");
  }

  @Then("confirmation checkbox is displayed")
  public void confirmationCheckboxIsDisplayed() {
    assertTrue(cancelMySubPage.checkBoxConfirmIsDisplayed());
  }

  @Then("Cancel my subscription is displayed")
  public void cancelMySubscriptionIsDisplayed() {
    assertTrue(cancelMySubPage.btnCancelMySubIsDisplayed());
  }

  @When("the user check the checkbox")
  public void theUserCheckTheCheckbox() {
    cancelMySubPage.selectCbConfirm();
  }

//  @Then("the checkbox is selected")
//  public void theCheckboxIsSelected() {
//    assertTrue(mySubPage.checkBoxConfirmIsSelected());
//  }

  @Then("Cancel my subscription is enabled")
  public void cancelMySubscriptionIsEnabled() {
    assertTrue(cancelMySubPage.btnCancelMySubIsEnabled());
  }

  @When("the user click Cancel my subscription")
  public void userClickCancelMySubscription() {
    cancelMySubPage.clickCancelMySub();
  }

  @Then("second confirmation dialog is displayed")
  public void secondConfirmationDialogIsDisplayed() {
    assertTrue(cancelMySubPage.secondConfirmDialogIsDisplayed());
  }

  @When("Finish button is displayed")
  public void finishButtonIsDisplayed() {
    assertTrue(mySubPage.btnFinishIsDisplayed());
  }

  @Then("the user clicks Finish button")
  public void theUserClicksFinishButton() {
    mySubPage.clickFinish();
  }

  @When("Subscribe and save page is displayed")
  public void subscribeAndSavePageIsDisplayed() {
    assertEquals(subscribeAndSave.getTextPageTitle(), "Subscribe and save");
  }

  @Then("the button Set up my subscription now is displayed")
  public void theButtonSetUpMySubscriptionNowIsDisplayed() {
    assertTrue(mySubPage.setUpSubNowIsDisplayed());
  }

  @When("the user clicks on button Set up my subscription now")
  public void theUserClicksOnButtonSetUpMySubscriptionNow() {
    mySubPage.clickSetUpSubNow();
    if (selectConnectorPage.isElementPresent(selectConnectorPage.getButtonAddCardDetail())) { //if there is no card then we add some
      selectConnectorPage.clickAddCardDetail();
      CommonSteps commonSteps = new CommonSteps(base);
      commonSteps.userCanAddNewCardToTheWallet();
      assertEquals(selectConnectorPage.getPageTitle(), "Select a connector");
    }
  }

  @Then("confirm page is displayed")
  public void confirmPageIsDisplayed() {
    assertTrue(mySubPage.confirmPageIsDispayed());
  }

  @When("Confirm and authorise button is displayed")
  public void confirmAndAuthoriseButtonIsDisplayed() {
    assertTrue(subscribeAndSave.confirmAndAuthoriseIsDisplayed());
  }

  @Then("the user click on button Confirm and authorise")
  public void theUserClickOnButtonConfirmAndAuthorise() {
    subscribeAndSave.clickConfirmAndAuthorise();
  }
}
