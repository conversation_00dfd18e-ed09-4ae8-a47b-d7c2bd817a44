package pulse.stepDefinitions.logins;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.testng.Assert;
import pages.facebook.AllowCookiesPage;
import pages.facebook.LoginToYourFacebookAccountPage;
import pages.registrationmfe.CIP_LoginPage;
import pulse.utils.BaseUtils;
import utils.JsonManager;
import utils.Pojo.User;

import static org.junit.Assert.*;

public class FacebookSteps extends BaseUtils {
  BaseUtils base;
  private final LoginToYourFacebookAccountPage loginToYourFacebookAccountPage;

  public FacebookSteps(BaseUtils base) {
    super();
    this.base = base;
    this.loginToYourFacebookAccountPage = new LoginToYourFacebookAccountPage(base.getDriver());
  }

  @When("tapping the Facebook icon")
  public void clickingTheFacebookIcon() {
    new CIP_LoginPage(base.getDriver()).clickFaceBookIcon();
  }

  @Then("the log in with Facebook page is displayed")
  public void theLogInWithFacebookPageIsDisplayed() {
    new AllowCookiesPage(base.getDriver()).clickAllowCookiesButton();
  }

  @When("the Facebook log in to your account page is displayed")
  public void theFacebookLoginToYourAccountPageIsDisplayed() {
    loginToYourFacebookAccountPage.switchContext("NATIVE_APP");
    assertTrue(loginToYourFacebookAccountPage.isLoginDisplayed());
  }

  @When("user {string} enters Facebook credentials")
  public void userEntersFacebookCredentials(String value) {
    User user = JsonManager.getUser(value, base.isAndroid());
    assertNotNull(user);
    base.setUser(user);
    loginToYourFacebookAccountPage.enterFaceBookEmailAddressField(user.getEmail());
    assertEquals(loginToYourFacebookAccountPage.getEmail(), user.getEmail());
    String brand = getBrand();
    String facebookPassword;
    if (brand.equals("bpUS")) {
      facebookPassword = System.getenv("FACEBOOK_PASSWORD_US_1");
    } else {
      facebookPassword = System.getenv("FACEBOOK_PASSWOR1");
    }
    loginToYourFacebookAccountPage.enterPasswordField(facebookPassword);
    assertEquals(loginToYourFacebookAccountPage.getPassword(), facebookPassword);

    loginToYourFacebookAccountPage.clickFaceBookLoginButton();
    loginToYourFacebookAccountPage.clickContinue();
  }
}
