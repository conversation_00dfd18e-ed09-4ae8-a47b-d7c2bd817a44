package pulse.stepDefinitions.profile;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.profile.SettingsPage;
import pulse.utils.BaseUtils;
import java.util.List;
import static org.testng.Assert.assertEquals;

public class SettingSteps extends BaseUtils {
  BaseUtils base;

  private final SettingsPage settingsPage;

  public SettingSteps(BaseUtils base) {
    super();
    this.base = base;
    this.settingsPage = new SettingsPage(base.getDriver());
  }

  @When("clicking the selected default English language option")
  public void clickingTheDefaultEnglishLanguageOption() {
    settingsPage.clickSelectedEnglishLanguageButton();
  }

  @Then("the default english language settings screen is displayed")
  public void theSettingsScreenIsDisplayed() {
    settingsPage.selectedEnglishLanguageButtonIsDisplayed();
  }

  @Then("the language is updated on the settings screen to:")
  public void theLanguageIsUpdatedOnTheSettingsScreen(List<String> countries) {
    countries.forEach(settingsPage::selectedLanguageButtonsIsDisplayed);
  }

  @Then("the Settings page is displayed")
  public void theUserIsOnSettingsPage() {
    assertEquals(settingsPage.getTextPageTitle(), "Settings");
  }

  @When("the Marketing preferences are clicked")
  public void theUserClicksMarketingPreferences() {
    settingsPage.clickMarketingPreferences();
  }

}
