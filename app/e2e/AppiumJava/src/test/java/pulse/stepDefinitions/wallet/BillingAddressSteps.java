package pulse.stepDefinitions.wallet;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.wallet.BillingAddressPage;
import pulse.utils.BaseUtils;
import utils.ConstantClass;
import utils.Pojo.User;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class BillingAddressSteps extends BaseUtils {
    BaseUtils base;
  public BillingAddressPage billingAddressPage;

  public BillingAddressSteps(BaseUtils base) {
    super();
    this.base = base;
    this.billingAddressPage = new BillingAddressPage(base.getDriver());
  }

  @Then("the Billing Address page is displayed")
  public void iAmOnTheBillingAddressPage() {
    assertTrue(billingAddressPage.switchContext(ConstantClass.CONTEXT.WEBVIEW.toString()));
    assertEquals(billingAddressPage.getTextPageTitle(), "Billing address");
  }

  @When("user enters billing address information and save")
  public void iEnterBillingAddressInformationAndSave() {
    User user = base.getUser();
    billingAddressPage.enterStreetName(user.getStreet());
    assertEquals(billingAddressPage.getStreetName(), user.getStreet());

    billingAddressPage.enterHouseNumber(user.getHouseNumber());
    assertEquals(billingAddressPage.getHouseNumber(), user.getHouseNumber());

    billingAddressPage.enterPostalCode(user.getPostalCode());
    assertEquals(billingAddressPage.getPostalCode(), user.getPostalCode());

    billingAddressPage.enterTown(user.getTown());
    assertEquals(billingAddressPage.getTown(), user.getTown());

    assertEquals(billingAddressPage.getCountryCode(), user.getCountryCode());

    billingAddressPage.hideKeyboard();
    billingAddressPage.clickSave();
  }
}
