package pulse.stepDefinitions.payments;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.chargemfe.FreeFormPage;
import pages.chargemfe.InvoicePage;
import pages.chargemfe.RecentTransactionsPage;
import pages.chargemfe.TransactionDetailPage;
import pulse.utils.BaseUtils;
import utils.StringUtilities;

import java.util.Date;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;
import static org.testng.AssertJUnit.assertNotNull;

public class RecentTransactionsSteps extends BaseUtils {
  BaseUtils base;
  private final RecentTransactionsPage recentTransactionsPage;
  private final TransactionDetailPage transactionDetailPage;
  private final InvoicePage invoicePage;
  private String chargingDateTime;

  public RecentTransactionsSteps(BaseUtils base) {
    super();
    this.base = base;
    this.recentTransactionsPage = new RecentTransactionsPage(base.getDriver());
    this.transactionDetailPage = new TransactionDetailPage(base.getDriver());
    this.invoicePage = new InvoicePage(base.getDriver());
  }

  @Then("the Recent transactions page is displayed")
  public void iAmOnRecentTransactionsPage() {
    assertEquals(recentTransactionsPage.getPageTitle(), "Recent transactions");
  }

  @When("the latest transaction is selected")
  public void iSelectTheLatestTransaction() {
    int index = 0;
    chargingDateTime = recentTransactionsPage.getTransactionDateTime(index).replace(" ", " ");
    Date actualChargingDate = StringUtilities.formatDateString(chargingDateTime);
    Date expectedChargingDate = base.getUser().getChargeSession().getChargingDateTime();
    assertEquals(actualChargingDate.compareTo(expectedChargingDate), 0, "The actualChargingDate " + actualChargingDate + " differs from " + expectedChargingDate);
    float totalCost = Float.parseFloat(StringUtilities.getStringAfter(recentTransactionsPage.getTextTotalCost(index), "€"));
    if(base.isAndroid()){ //cannot get energyDelivered on this page from ios
      float energyDelivered = Float.parseFloat(StringUtilities.getStringBefore(recentTransactionsPage.getEnergyAndDuration(index), " kWh"));
      assertEquals(energyDelivered, base.getUser().getChargeSession().getEnergyDelivered());
    }
    assertEquals(totalCost, base.getUser().getChargeSession().getTotalCost());
    recentTransactionsPage.getListTransactions().get(index).click();
    recentTransactionsPage.waitMilliseconds(2000);
  }

  @Then("Transaction details are displayed")
  public void transactionDetailsAreDisplayed() {
    assertEquals(transactionDetailPage.getPageTitle().replace(" ", " "), chargingDateTime);
  }

  @When("Download VAT invoice button is clicked")
  public void iClickDownloadVATInvoice() {
    transactionDetailPage.clickDownloadInvoice();
  }

  @Then("the invoice can be downloaded and verified")
  public void theInvoiceIsDownloadedAndViewed() {
    String deviceTime;
    String invoiceStartChargeDate;
    String chargePointTimeZone = base.getUser().getTimeZone();

    if (invoicePage.isAndroid) {
      deviceTime = ((AndroidDriver) base.getDriver()).getDeviceTime();
      invoiceStartChargeDate = StringUtilities.calculateStartChargeDate(deviceTime, chargingDateTime, chargePointTimeZone);
    } else {
      deviceTime = ((IOSDriver) base.getDriver()).getDeviceTime();
      invoiceStartChargeDate = StringUtilities.calculateStartChargeDate(deviceTime, chargingDateTime, chargePointTimeZone);
      new FreeFormPage(base.getDriver()).openWithFreeform();
    }

    invoicePage.settDefaultPdfViewer();
    String invoiceContent = invoicePage.getInvoiceContent();

    assertNotNull(invoicePage.getInvoiceTitle());
    assertTrue(invoiceContent.contains("B2Mobility"));
    assertTrue(invoiceContent.contains(invoiceStartChargeDate), "The invoice doeasn't contain " + invoiceStartChargeDate + ".\n\nInvoice:\n" + invoiceContent);
  }

  @When("user taps Done on pdf viewer page")
  public void userTapsDoneOnPdfViewerPage() {
    invoicePage.clickDone();
  }

  @When("user goes back from Transaction details page")
  public void userGoesBackFromTransactionDetailsPage() {
    transactionDetailPage.goBack();
  }

  @When("user goes back from the Recent transaction details page")
  public void userGoesBackFromTheRecentTransactionDetailsPage() {
    recentTransactionsPage.goBack();
  }

  @Then("Check your internet connection is displayed on Recent transactions page")
  public void checkYourInternetConnectionIsDisplayedOnRecentTransactionsPage() {
    assertEquals(recentTransactionsPage.getTextCheckInternet(), "Check your internet connection and try again.");
  }

  @Then("Check your internet connection is not displayed on Recent transactions page")
  public void checkYourInternetConnectionIsNotDisplayedOnRecentTransactionsPage() {
    assertTrue(recentTransactionsPage.isCheckInternetInvisible());
  }
}
