package pulse.stepDefinitions.map;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.openqa.selenium.WebElement;
import pages.mapmfe.MapPage;
import pages.mapmfe.SiteDetailsPage;
import pulse.utils.BaseUtils;

import java.io.File;
import java.util.List;

import static org.testng.Assert.assertTrue;

public class SiteDetailSteps extends BaseUtils {
  BaseUtils base;

  SiteDetailsPage siteDetailsPage;
  MapPage mapPage;
  List<WebElement> chargePoints;

  public SiteDetailSteps(BaseUtils base) {
    super();
    this.base = base;
    this.siteDetailsPage = new SiteDetailsPage(base.getDriver());
    this.mapPage = new MapPage(base.getDriver());
  }

  @Then("the charge point details screen is displayed")
  public void theChargePointDetailScreenIsDisplayed() {
    if (base.isAndroid()) {
      assertTrue(siteDetailsPage.getTextAddress().length() > 0);
    } else {
      assertTrue(siteDetailsPage.favoriteButonIsDisplayed()); // cannot get address in iOS
    }
  }

  @When("selecting the Log in or Create an account button")
  public void selectingLoginOrCreateAnAccountButton() {
    siteDetailsPage.loginOrCreateAnAccountButtonIsClicked();
  }

  @When("clicking Charge now button")
  public void clickingChargeNowButton() {
    siteDetailsPage.clickChargeNow();
  }

  @When("marking the charge point {string} as a favourite")
  public void markingTheChargePointAsAFavourite(String value) {
    File favouriteRefImage = new File("src/test/resources/images/" + value + "_buble_Favourite.png");
    chargePoints = mapPage.getMapItems(favouriteRefImage, 84, 8, 13, 22);
    if (chargePoints.size() > 0) {
      siteDetailsPage.markAsFavourite(); //unmark favourite
      chargePoints = mapPage.getMapItems(favouriteRefImage, 84, 8, 13, 22);
      assertTrue(chargePoints.size() == 0);
    }
    siteDetailsPage.markAsFavourite();
  }

  @Then("the favourite symbol is assigned to the charge point {string}")
  public void theFavouriteSymbolIsAssignedToTheChargePoint(String value) {
    File bubbleFavouriteRefImage = new File("src/test/resources/images/" + value + "_buble_Favourite.png");
    chargePoints = mapPage.getMapItems(bubbleFavouriteRefImage, 84, 8, 13, 22);
    assertTrue(chargePoints.size() == 1);
  }

  @When("closing the charge point details page")
  public void iCloseTheChargePointDetails() {
    siteDetailsPage.closeDetails();
  }

  @When("closing the Details tab on charge point details screen")
  public void iCloseTheChargePointDetailsSwipeDown() {
    siteDetailsPage.swipeDown();
  }

  @And("the charge point should have {string} listed")
  public void theChargePointShouldHaveListed(String name) {
    siteDetailsPage.swipeUp();
    assertTrue(siteDetailsPage.getConnector(name) != null);
  }

  @When("opening Details tab on charge point details screen")
  public void openingDetailsTabOnChargePointDetailsScreen() {
    siteDetailsPage.openDetailsTab();
  }

  @Then("the {string} number should be from that country")
  public void theCustomerCarePhoneNumberShouldBeFromThatCountry(String phonePrefix) {
    String phone = siteDetailsPage.getCustomerCarePhone();
    assertTrue(phone.startsWith("Tel:  " + phonePrefix), "The phone doestn't start with " + phonePrefix);
  }
}
