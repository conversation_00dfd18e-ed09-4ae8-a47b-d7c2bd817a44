package pulse.stepDefinitions.charge;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.openqa.selenium.WebElement;
import pages.chargemfe.SelectConnectorPage;
import pulse.stepDefinitions.CommonSteps;
import pulse.stepDefinitions.wallet.PaymentMethodsSteps;
import pulse.utils.BaseUtils;
import utils.Pojo.ChargeSession;
import utils.StringUtilities;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class SelectConnectorSteps extends BaseUtils {
  BaseUtils base;
  public SelectConnectorPage selectConnectorPage;

  public SelectConnectorSteps(BaseUtils base) {
    super();
    this.base = base;
    this.selectConnectorPage = new SelectConnectorPage(base.getDriver());
  }

  @Then("Select a connector page is displayed")
  public void selectAConnectorPageIsDisplayed() {
    if (base.getUser().getChargeSession() == null) {  //'Select a connector' page is opened by selecting some CP from the map
      base.getUser().setChargeSession(new ChargeSession());
    } else { //'Select a connector' page is opened by entering and searching CP ID
      assertTrue(selectConnectorPage.getChargeId().contains(base.getUser().getChargeSession().getId()));
    }
    assertEquals(selectConnectorPage.getPageTitle(), "Select a connector");
  }

  @Then("Select a connector page is displayed whith added card")
  public void selectAConnectorPageIsDisplayedWhithAddedCard() {
    assertEquals(selectConnectorPage.getPageTitle(), "Select a connector");
    assertEquals(selectConnectorPage.getTextCreditCard(), StringUtilities.getNameAndLastFour(base.getUser().getNewCard()));
  }

  @When("selecting a connector {string}")
  public void iSelectConnector(String connector) {
    selectConnectorPage.selectConnector(connector);
    if (selectConnectorPage.isAndroid) {  //unable to get checkbox state in iOS
      assertTrue(selectConnectorPage.isChecked(connector));
    }
    base.getUser().getChargeSession().setConnectorName(connector);
  }

  @When("clicking the button Charge using Connector...")
  public void startCharging() {
    selectConnectorPage.clickCharge();
  }

  @When("user selects Add payment card")
  public void userSelectsAddPaymentCard() {
    selectConnectorPage.clickAddCard();
  }

  @When("selecting available connector and starting the charge")
  public void selectingAvailableConnectorAndStartingTheCharge() {
    base.getUser().getChargeSession().setAddress(selectConnectorPage.getAddress());
    WebElement selectedConnector = selectConnectorPage.selectAvailableConnector();
    assertTrue(selectConnectorPage.isConnectorSelected(selectedConnector));
    base.getUser().getChargeSession().setConnectorName(selectedConnector.getText());
    selectConnectorPage.clickCharge();
  }

  @When("selecting available connector")
  public void selectingAvailableConnector() {
    base.getUser().getChargeSession().setAddress(selectConnectorPage.getAddress());
    WebElement selectedConnector = selectConnectorPage.selectAvailableConnector();
    if (base.isAndroid()) {
      assertTrue(selectConnectorPage.isConnectorSelected(selectedConnector));
    }
  }

  @Then("the Charge using Connector... button will be disabled")
  public void theChargeButtonWillBeDisabled() {
    assertTrue(selectConnectorPage.isChargeButtonDisabled());
  }

  @Then("the Charge using Connector... button will be enabled")
  public void theChargeButtonWillBeEnabled() {
    assertTrue(selectConnectorPage.isChargeButtonEnabled());
  }

  @When("clicking Add payment method link")
  public void clickingAddPaymentMethod() {
    selectConnectorPage.swipeUp();
    selectConnectorPage.waitMilliseconds(500);
    selectConnectorPage.clickAddCardLink();
  }

  @When("there is no payment card add some")
  public void thereIsNoPaymentCardAddSome() {
    if (selectConnectorPage.isElementPresent(selectConnectorPage.getButtonAddCard())) { //if there is no card then we add some
      userSelectsAddPaymentCard();
      PaymentMethodsSteps paymentMethodsSteps = new PaymentMethodsSteps(base);
      paymentMethodsSteps.addYourPaymentCardIsDisplayed();
      paymentMethodsSteps.userClicksAddPaymentCard();
      CommonSteps commonSteps = new CommonSteps(base);
      commonSteps.userCanAddNewCardToTheWallet();
      assertEquals(selectConnectorPage.getPageTitle(), "Select a connector");
    }
  }

}
