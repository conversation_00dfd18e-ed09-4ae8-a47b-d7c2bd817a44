package pulse.stepDefinitions.map;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.apache.commons.lang3.tuple.Pair;
import pages.mapmfe.FilterPage;
import pulse.utils.BaseUtils;

import java.util.List;

import static org.testng.Assert.*;

public class FilterSteps extends BaseUtils {
  BaseUtils base;

  FilterPage filterPage;

  public FilterSteps(BaseUtils base) {
    super();
    this.base = base;
    this.filterPage = new FilterPage(base.getDriver());
  }

  @When("on the Filter Screen")
  public void theFilterScreenIsDisplayed() {
    filterPage.filterPageIsDisplayed();
  }

  @Then("The Uber selection should not be displayed")
  public void theUberSelectionShouldNotBeDisplayed() {
    filterPage.uberChargeSwitchIsNotDisplayed();
  }

  @Then("the Filter page opens")
  public void theFilterPageOpens() {
    if (base.isAndroid()) {
      assertEquals(filterPage.getPageTitle(), "Filters");
    } else {
      assertEquals(filterPage.getPageTitle(), "Filter page title");
    }
  }

  @When("selecting connector type {string}")
  public void selectingConnectorType(String name) {
    filterPage.selectItem(name);
    assertTrue(filterPage.isItemSelected(name));
  }

  @When("clearing all the filters")
  public void clearingAll() {
    filterPage.clickClear();
  }

  @Then("all the connectors are deselected")
  public void allTheConnectorsAreDeselected() {
    List<Pair<String, Boolean>> filterItems = filterPage.getFilterItems();
    assertEquals(filterItems.size(), 20);
    for (Pair<String, Boolean> pair : filterItems) {
      assertFalse(pair.getValue(), "Item '" + pair.getKey() + "' is not deselected"); //all items are deselected
    }
  }

  @And("saving the connector type filter")
  public void savingTheConnectorTypeFilter() {
    filterPage.clickSave();
  }
}
