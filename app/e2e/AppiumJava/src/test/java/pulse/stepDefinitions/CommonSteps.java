package pulse.stepDefinitions;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import pulse.stepDefinitions.logins.LoginSteps;
import pulse.stepDefinitions.map.MapSteps;
import pulse.stepDefinitions.profile.ProfileSteps;
import pulse.stepDefinitions.wallet.AddYourPaymentMethodSteps;
import pulse.stepDefinitions.wallet.SecureCheckoutSteps;
import pulse.utils.BaseUtils;
import utils.JsonManager;
import utils.Pojo.User;

import static org.testng.Assert.assertNotNull;

public class CommonSteps extends BaseUtils {
    BaseUtils base;

  public CommonSteps(BaseUtils base) {
    super();
    this.base = base;
  }

  @Given("a user {string} logs in using email address")
  public void loginEmail(String id) {
    User user = JsonManager.getUser(id, base.isAndroid());
    base.setUser(user);
    assertNotNull(user, "User " + id + " cannot be found");

    AppSteps appSteps = new AppSteps(base);
    appSteps.theApplicationStartsUp();
    appSteps.iClickTheProfileIcon();

    ProfileSteps profileSteps = new ProfileSteps(base);
    profileSteps.iAmOnTheProfilePage();
    profileSteps.clickingTheLoginButton();

    LoginSteps loginSteps = new LoginSteps(base);
    loginSteps.iAmOnCIPLoginPage();
    loginSteps.enterEmail(user.getEmail());
    loginSteps.iCanClickContinueOnLogInPage();
    loginSteps.checkYourEmailPageOpens();

    loginSteps.theVerificationCodeIsSentToTheUsersEmail();
    loginSteps.userEntersTheVerificationCode();
    loginSteps.userClicksContinueOnCheckYourEmailPage();

    new MapSteps(base).theMapIsDisplayed();
  }

  @Given("a user {string} logs in using mobile number")
  public void loginMobile(String id) {
    User user = JsonManager.getUser(id, base.isAndroid());
    base.setUser(user);
    assertNotNull(user, "User " + id + " cannot be found");

    AppSteps appSteps = new AppSteps(base);
    appSteps.theApplicationStartsUp();
    appSteps.iClickTheProfileIcon();

    ProfileSteps profileSteps = new ProfileSteps(base);
    profileSteps.iAmOnTheProfilePage();
    profileSteps.clickingTheLoginButton();

    LoginSteps loginSteps = new LoginSteps(base);
    loginSteps.iAmOnCIPLoginPage();
    loginSteps.iEnterMobileNumber(user.getPhonePrefix() + user.getPhoneNumber());
    loginSteps.iCanClickContinueOnLogInPage();
    loginSteps.enterYourCodeIsDisplayed();
    loginSteps.iReceiveCIPVerificationCode();
    loginSteps.userEntersSMSCode();
    loginSteps.selectingAgreePrivacyPolicyAndContinue();

    new MapSteps(base).theMapIsDisplayed();
  }


  @Then("user can add a new card to the wallet")
  public void userCanAddNewCardToTheWallet() {
    AddYourPaymentMethodSteps addYourPaymentMethodSteps = new AddYourPaymentMethodSteps(base);
    addYourPaymentMethodSteps.AddYourPaymentMethodIsDisplayed();
    addYourPaymentMethodSteps.userEntersValidCreditCardNumber();
    addYourPaymentMethodSteps.enteringExpiryMonth();
    addYourPaymentMethodSteps.enteringTheExpiryYear();
    addYourPaymentMethodSteps.enteringTheCVV();
    addYourPaymentMethodSteps.enteringTheCardHoldersName();
    addYourPaymentMethodSteps.enteringTheStreetAndHouseName();
    addYourPaymentMethodSteps.enteringTheTown();
    addYourPaymentMethodSteps.enteringThePostalOrZipCode();
    addYourPaymentMethodSteps.selectingTheCounry();
    addYourPaymentMethodSteps.selectingThePhonePrefix();
    addYourPaymentMethodSteps.enteringTheMobileNumber();
    addYourPaymentMethodSteps.enteringTheEmailAddress();
    addYourPaymentMethodSteps.clickingContinueToCardAuthorisation();

    SecureCheckoutSteps secureCheckoutSteps = new SecureCheckoutSteps(base);
    secureCheckoutSteps.iAmOnTheSecureCheckoutPage();
    secureCheckoutSteps.iEnterTheSMSCodeAndSubmit();
  }

  @And("the {string} is deleted and re-registered using their email")
  public void theRegisersToBpPulseAppWithEmail(String id) {
    AppSteps appSteps = new AppSteps(base);
    appSteps.theIsDeletedFromForgeRockDatabaseBasedOnEmail(id);
    appSteps.newIsGeneratedUsingCIPAPI(id);
    appSteps.iClickTheProfileIcon();
    ProfileSteps profileSteps = new ProfileSteps(base);
    profileSteps.iAmOnTheProfilePage();
    profileSteps.clickingTheLoginButton();

    LoginSteps loginSteps = new LoginSteps(base);
    loginSteps.iAmOnCIPLoginPage();
    loginSteps.iEnterEmailById(id);
    loginSteps.iCanClickContinueOnLogInPage();
    loginSteps.checkYourEmailPageOpens();
    loginSteps.theVerificationCodeIsSentToTheUsersEmail();
    loginSteps.userEntersTheVerificationCode();
    loginSteps.userClicksContinueOnCheckYourEmailPage();
    loginSteps.selectingAgreePrivacyPolicyAndContinue();

    CustomiseAppSteps customiseAppSteps = new CustomiseAppSteps(base);
    customiseAppSteps.customiseTheBpPulseAppPageOpens();
    customiseAppSteps.userSelectsTheCountryOnCustomisePage();
    customiseAppSteps.iEnterTheFirstNameOnCustomisePage();
    customiseAppSteps.iEnterTheLastNameOnCustomisePage();
    customiseAppSteps.iCheckIHaveReadTermsAndConditions();
    customiseAppSteps.iClickJumpRightIn();

    new MapSteps(base).theMapIsDisplayed();
  }
}
