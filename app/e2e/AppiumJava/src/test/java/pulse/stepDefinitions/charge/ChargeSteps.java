package pulse.stepDefinitions.charge;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.chargemfe.ChargeSummaryPage;
import pages.chargemfe.ChargingPage;
import pages.chargemfe.SerialSearchPage;
import pages.wallet.ModalPage;
import pulse.stepDefinitions.AppSteps;
import pulse.utils.BaseUtils;
import utils.Pojo.ChargeSession;

import static org.testng.Assert.*;

public class ChargeSteps extends BaseUtils {
  BaseUtils base;
  private final SerialSearchPage serialSearchPage;
  private final ChargeSummarySteps chargeSummarySteps;
  private final ModalPage modalPage;

  public ChargeSteps(BaseUtils base) {
    super();
    this.base = base;
    this.serialSearchPage = new SerialSearchPage(base.getDriver());
    this.chargeSummarySteps = new ChargeSummarySteps(base);
    this.modalPage = new ModalPage(base.getDriver());
  }

  @When("entering the charger ID {string} and clicking the Find charger")
  public void iEnterTheChargerIDAndFindCharger(String value) {
    serialSearchPage.enterChargerId(value);
    if (base.isAndroid()) {
      assertEquals(serialSearchPage.getChargerId(), value);  //cannot get value in iOS
    }
    serialSearchPage.selectFindChargerButton();
    ChargeSession chargeSession = new ChargeSession();
    chargeSession.setId(value);
    base.getUser().setChargeSession(chargeSession);
  }

  @When("entering the charger ID")
  public void iEnterTheChargerID() {
    String chrgerId = base.getUser().getChargerId();
    assertNotNull(chrgerId, "User " + base.getUser().getId() + " has no 'chargerId' set in users" + System.getenv("RELEASE_VARIANT") + ".json");
    serialSearchPage.enterChargerId(chrgerId);
    if (base.isAndroid()) {
      assertEquals(serialSearchPage.getChargerId(), chrgerId);  //cannot get value in iOS
    }
  }

  @When("selecting Find charger")
  public void findCharger() {
    serialSearchPage.selectFindChargerButton();
    ChargeSession chargeSession = new ChargeSession();
    chargeSession.setId(base.getUser().getChargerId());
    base.getUser().setChargeSession(chargeSession);
  }

  @When("there is a charge in progress stop it")
  public void thereIsAChargeInProgressStopIt() {
    //in the case the Charging session is active or Charge summary is displayed from the previous test let's close it
    new AppSteps(base).iTapOnChargeIcon();
    serialSearchPage.waitMilliseconds(2000);
    if (!serialSearchPage.isElementPresent(serialSearchPage.getTextPageTitle())) {
      if (serialSearchPage.isElementPresent(new ChargeSummaryPage(base.getDriver()).getTextPageTitle())) {//closing  Charge summary
        chargeSummarySteps.iClickDoneOnChargeSummaryPage();
      } else if (serialSearchPage.isElementPresent(new ChargingPage(base.getDriver()).getTextPageTitle())) {
        new ChargingPage(base.getDriver()).clickStopCharge();
        modalPage.clickButton("Stop charging");
        chargeSummarySteps.iClickDoneOnChargeSummaryPage();
      } else if (modalPage.isDisplayed("use Location Accuracy")) {
        modalPage.clickButton("No, thanks");
      } else {
        fail("None of the pages 'Charge', 'Charge summary', 'Location Accuracy' modal or 'Charging' are present");
      }
    }
  }

  @Then("the Charge page is displayed")
  public void iAmOnChargePage() {
    if (modalPage.isDisplayed("use Location Accuracy")) {
      modalPage.clickButton("No, thanks");
    }
    assertEquals(serialSearchPage.getPageTitle(), "Charge");
    assertEquals(serialSearchPage.getTextEnterId(), "Enter the charger ID to start");
  }

  @Then("the Serial Search page is displayed")
  public void theUserIsOnTheSerialSearchPage() {
    assertEquals(serialSearchPage.getPageTitle(), "Charge");
  }

  @When("entering an invalid CP number {string} into the input field")
  public void enteringAnInvalidCpNumberIntoTheInputField(String value) {
    serialSearchPage.enterChargerId(value);
  }

  @And("selecting the Find Charger button")
  public void selectingFindCharger() {
    serialSearchPage.selectFindChargerButton();
  }

  @Then("a no matching charge error is displayed")
  public void aNoMatchingChargeErrorIsDisplayed() {
    assertEquals(serialSearchPage.getErrorChargerId(), "No chargers match this ID, please check and try again");
  }

}
