package pulse.stepDefinitions;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.openqa.selenium.WebElement;
import pages.profile.*;
import pulse.utils.BaseUtils;
import utils.Gmail_API;
import utils.Pojo.User;
import utils.StringUtilities;

import static org.testng.Assert.*;

public class DeleteAccountSteps extends BaseUtils {
  BaseUtils base;

  private final DeleteAccountPage deleteAccountPage;
  private final ConfirmationPage confirmationPage;
  private final GuidancePage guidancePage;
  private final SelectOptionPage selectOptionPage;
  private final RequestReceivedPage requestReceivedPage;
  private final BeforeYouGoPage beforeYouGoPage;

  public DeleteAccountSteps(BaseUtils base) {
    super();
    this.base = base;
    this.deleteAccountPage = new DeleteAccountPage(base.getDriver());
    this.confirmationPage = new ConfirmationPage(base.getDriver());
    this.guidancePage = new GuidancePage(base.getDriver());
    this.selectOptionPage = new SelectOptionPage(base.getDriver());
    this.requestReceivedPage = new RequestReceivedPage(base.getDriver());
    this.beforeYouGoPage = new BeforeYouGoPage(base.getDriver());
  }

  @Then("Delete account page is displayed")
  public void deleteAccountPageIsDisplayed() {
    assertEquals(deleteAccountPage.getPageTitle(), "Delete account");
  }

  @When("user selects Delete my account")
  public void userSelectsDeleteMyAccount() {
    deleteAccountPage.clickDeleteAccount();
  }

  @Then("Confirm deletion page is displayed")
  public void confirmDeletionPageIsDisplayed() {
    assertEquals(confirmationPage.getPageTitle(), "Confirm deletion");
  }

  @When("selecting the reason of deletion {string}")
  public void selectingTheReasonOfDeletion(String value) {
    confirmationPage.swipeUp();
    WebElement selectedReason = confirmationPage.selectReason(value);
    assertNotNull(selectedReason);
    if (base.isAndroid()) {
      assertTrue(confirmationPage.isReasonSelected(selectedReason));
    }
  }

  @And("selecting I understand the consequences")
  public void selectingIUnderstandTheConsequences() {
    confirmationPage.checkUnderstandConsequences();
    assertTrue(confirmationPage.isUnderstandChecked());
  }

  @Then("the Confirm account deletion button will be disabled")
  public void theConfirmAccountDeletionButtonWillBeDisabled() {
    assertTrue(confirmationPage.isConfirmDeletionDisabled());
  }

  @Then("the Confirm account deletion button will be enabled")
  public void theConfirmAccountDeletionButtonWillBeEnabled() {
    assertTrue(confirmationPage.isConfirmDeletionEnabled());
  }

  @When("the Confirm account deletion button is clicked")
  public void theConfirmAccountDeletionButtonIsClicked() {
    confirmationPage.clickConfirmDeletion();
  }

  @And("Request received page is displayed")
  public void requestReceivedTextIsDisplayed() {
    assertEquals(requestReceivedPage.getPageTitle(), "Request received");
  }

  @Then("Guidence page is displayed")
  public void guidencePageIsDisplayed() {
    assertEquals(guidancePage.getPageTitle(), "Guidance");
  }

  @When("clicking Next on Guidence page")
  public void clickingNextOnGuidencePage() {
    guidancePage.clickNext();
  }

  @Then("Select optionPage page is displayed")
  public void selectOptionPagePageIsDisplayed() {
    assertEquals(selectOptionPage.getPageTitle(), "Select option");
  }

  @When("clicking Select on Select optionPage page")
  public void clickingNextOnSelectOptionPagePage() {
    selectOptionPage.clickSelect();
  }

  @When("selecting option {string} on Select option page")
  public void selectingOptionOnSelectOptionPage(String value) {
    assertNotNull(selectOptionPage.selectOption(value));
  }

  @When("clicking Done on Request received page")
  public void clickingDoneOnRequestReceivedPage() {
    requestReceivedPage.clickDone();
  }

  @And("the Account Deletion Requested email is received")
  public void theConfirmationEmailIsReceivedInTheUserSMailbox() {
    User user = base.getUser();
    String email = StringUtilities.removeEmailPlusAddressing(base.getUser().getEmail(), "+", "@");

    Gmail_API gmail_API = new Gmail_API(email);
    String query = "from: bp Data Privacy Team to: " + user.getEmail();
    String emailBody = gmail_API.readMail(query);
    if (!user.getCountryCode().equals("EN")) {
      user.setLanguage(user.getCountryCode().toLowerCase());//Email is received in user's country language
      base.setUser(user);
    }
    String emailGreeting = base.getTranslation("emailGreeting");
    base.setUser(user);
    String firstLastName = StringUtilities.substringBetween(emailBody, emailGreeting + " ", ",");//getting firstLastName from the email
    assertEquals(firstLastName, user.getFirstName() + " " + user.getLastName());
  }

  @Then("Before you go page is displayed")
  public void beforeYouGoPageIsDisplayed() {
    assertEquals(beforeYouGoPage.getPageTitle(), "Before you go");
  }

  @When("clicking Delete my account and date")
  public void clickingDeleteMyAccountAndDate() {
    beforeYouGoPage.clickDeleteAccount();
  }
}

