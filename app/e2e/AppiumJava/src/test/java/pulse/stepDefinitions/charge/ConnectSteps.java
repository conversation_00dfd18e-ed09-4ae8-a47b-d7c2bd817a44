package pulse.stepDefinitions.charge;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.chargemfe.ConnectPage;
import pulse.utils.BaseUtils;

import static org.testng.Assert.assertEquals;

public class ConnectSteps extends BaseUtils {
    BaseUtils base;

  private final ConnectPage connectPage;

  public ConnectSteps(BaseUtils base) {
    super();
    this.base = base;
    connectPage = new ConnectPage(base.getDriver());
  }

  @Then("Connect your vehicle page is displayed")
  public void connectPageIsDisplayed() {
    assertEquals(connectPage.getTextPageTitle(), "Connect your vehicle");
  }

  @When("confirming the vehicle is connected")
  public void iConfirmTheVehicleIsConnected() {
    connectPage.clickConfirm();
  }
}
