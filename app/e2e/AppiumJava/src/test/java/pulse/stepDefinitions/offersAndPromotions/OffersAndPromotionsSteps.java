package pulse.stepDefinitions.offersAndPromotions;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.offersmfe.OffersAndPromotionsPage;
import pages.offersmfe.UsedAndExpiredOffersPage;
import pulse.utils.BaseUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class OffersAndPromotionsSteps {

  BaseUtils base;
  private final OffersAndPromotionsPage offersAndPromotionsPage;
  private final UsedAndExpiredOffersPage usedAndExpiredOffersPage;

  public OffersAndPromotionsSteps(BaseUtils base) {
    this.base = base;
    offersAndPromotionsPage = new OffersAndPromotionsPage(base.getDriver());
    usedAndExpiredOffersPage = new UsedAndExpiredOffersPage(base.getDriver());
  }

  @Then("the Nothing to show yet offers and promotion screen is displayed")
  public void theNothingToShowScreenIsDisplayed() {
    assertTrue(offersAndPromotionsPage.nothingToShowScreenIsDisplayed());
  }

  @Then("the Coming Soon screen is displayed")
  public void theComingSoonScreenIsDisplayed() {
    assertTrue(offersAndPromotionsPage.comingSoonScreenIsDisplayed());
  }

  @Then("Offers and promotions screen is displayed")
  public void offersAndPromotionsScreenIsDisplayed() {
    assertEquals(offersAndPromotionsPage.getPageTitle(), "Offers and promotions");
  }

  @When("going back to the Offers and promotions screen")
  public void goingBackToTheProfileScreen() {
    offersAndPromotionsPage.clickBackButton();
  }

  @When("the user clicks on Used and expired offers")
  public void theUserClicksOnUsedAndExpiredOffers() {
    offersAndPromotionsPage.clickUsedAndExpiredOffersButton();
  }

  @Then("Used and expired offers screen is displayed")
  public void usedAndExpiredOffersAppears() {
    assertEquals(usedAndExpiredOffersPage.getPageTitle(), "Used and Expired offers");
  }

  @When("screen contains Active Offer list")
  public void screenContainsActiveOfferList() {
    assertTrue(offersAndPromotionsPage.activeListIsDisplayed());
  }

  @Then("offer with {string} contains {string} left and expiry date with {string}")
  public void offerWithContainsLeft(String activeCode, String credit, String expireDate) {
    LocalDate date = LocalDate.parse(expireDate, DateTimeFormatter.ofPattern("d/M/yyyy"));
    assertTrue(offersAndPromotionsPage.offerCreditAndDateIsDisplayed(activeCode,credit,date));
  }

  @Then("there is expired offer containing offer with code {string}")
  public void thereIsExpiredOfferContainingOfferWithCode(String expiredCode) {
    assertTrue(usedAndExpiredOffersPage.expiredOfferIsDisplayed(expiredCode));
  }
}
