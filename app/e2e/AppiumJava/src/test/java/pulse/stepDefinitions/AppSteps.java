package pulse.stepDefinitions;

import core.BasePage;
import core.PhoneSettingsPage;
import io.appium.java_client.android.AndroidDriver;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import org.openqa.selenium.WebElement;
import pages.mapmfe.MapPage;
import pages.navigation.BottomTabNavigatorPage;
import pages.registrationmfe.NoticePage;
import pulse.utils.BaseUtils;
import utils.CIP_API;
import utils.ForgeRock_API;
import utils.JsonManager;
import utils.Pojo.User;

import java.net.HttpURLConnection;

import static org.testng.Assert.*;

public class AppSteps extends BaseUtils {
  BaseUtils base;
  BottomTabNavigatorPage bottomTabNavigatorPage;
  PhoneSettingsPage phoneSettingsPage;

  MapPage mapPage;
  NoticePage noticePage;

  public AppSteps(BaseUtils base) {
    super();
    this.base = base;
    this.bottomTabNavigatorPage = new BottomTabNavigatorPage(base.getDriver());
    this.mapPage = new MapPage(base.getDriver());
    this.noticePage = new NoticePage(base.getDriver());
    this.phoneSettingsPage = new PhoneSettingsPage(base.getDriver());
  }

  @Given("the application starts up")
  public void theApplicationStartsUp() {
    if (base.isAndroid()) {
      noticePage.clickContinue();
    } else {
      mapPage.clickAllowWhileUsingPrompt();
    }
    mapPage.waitForRendered();
  }

  @When("user taps on the Profile icon")
  public void iClickTheProfileIcon() {
    bottomTabNavigatorPage.clickProfileButton();
  }

  @When("a Guest taps on the Profile icon")
  public void theGuestTapsTheProfileIcon() {
    bottomTabNavigatorPage.clickProfileButton();
  }

  @When("user clicks on Charge icon")
  public void iTapOnChargeIcon() {
    bottomTabNavigatorPage.clickChargeButton();
  }

  @When("user taps on the Map icon")
  public void userTapsMapIcon() {
    bottomTabNavigatorPage.clickMapButton();
  }

  @When("a Guest taps the Help icon")
  public void guestUserTapsOnTheHelpIcon() {
    bottomTabNavigatorPage.clickHelpButton();
  }

  @When("user restarts the app")
  public void iResetTheApp() {
    BasePage app = new BasePage(base.getDriver());
    String packageName = app.getPackageName();
    app.closeApp(packageName);
    assertNotEquals(app.getPackageName(), packageName);
    app.startApp(packageName);
    mapPage.waitForRendered();
  }

  @When("user sets default browser to Chrome")
  public void setChromeAsDefaultBrowser() {
    phoneSettingsPage.searchSettings("Default browser");
    WebElement result = phoneSettingsPage.openSearchResult("Default browser app");
    assertNotNull(result);
    result.click();
    phoneSettingsPage.waitMilliseconds(1000);
    phoneSettingsPage.selectPicklistValue("Chrome");
    phoneSettingsPage.clickBack();

    String packageName = phoneSettingsPage.getPackageName();
    phoneSettingsPage.closeApp(packageName);
    assertNotEquals(phoneSettingsPage.getPackageName(), packageName);
  }

  @When("disabling the internet connection")
  public void disableInternet() {
    if (base.isAndroid() && ((AndroidDriver) base.getDriver()).getConnection().isWiFiEnabled()) {
      ((AndroidDriver) base.getDriver()).toggleWifi();
    }
    assertFalse(((AndroidDriver) base.getDriver()).getConnection().isWiFiEnabled());
  }

  @When("enabling the internet connection")
  public void enablingInternetConnection() {
    if (base.isAndroid() && !((AndroidDriver) base.getDriver()).getConnection().isWiFiEnabled()) {
      ((AndroidDriver) base.getDriver()).toggleWifi();
    }
    new BasePage(base.getDriver()).waitMilliseconds(3000);
    assertTrue(((AndroidDriver) base.getDriver()).getConnection().isWiFiEnabled());
  }

  @And("the {string} is deleted from the ForgeRock database based on phone number")
  public void theIsDeletedFromForgeRockDatabaseBasedOnPhoneNumber(String id) {
    findAndDeleteForgerockUser(id, "telephoneNumber");
  }

  @And("the {string} is deleted from the ForgeRock database based on email")
  public void theIsDeletedFromForgeRockDatabaseBasedOnEmail(String id) {
    findAndDeleteForgerockUser(id, "mail");
  }

  public void findAndDeleteForgerockUser(String id, String searchFilter) {
    User user = JsonManager.getUser(id, base.isAndroid());
    assertNotNull(user);
    base.setUser(user);
    String userInfo = null;
    if (searchFilter.equals("mail")) {
      userInfo = user.getEmail();
    } else if (searchFilter.equals("telephoneNumber")) {
      userInfo = user.getPhonePrefix() + user.getPhoneNumber();
    } else {
      fail("The " + searchFilter + " is not supported");
    }

    ForgeRock_API forgeRockApi = new ForgeRock_API();
    String accessTokenForgeRock = forgeRockApi.getToken();
    assertNotNull(accessTokenForgeRock);
    String forgeRockUserId = forgeRockApi.getForgeRockUserId(accessTokenForgeRock, searchFilter, userInfo);
    assertNotNull(forgeRockUserId);

    if (forgeRockUserId.length() > 1) {// Delete user if exists
      assertEquals(forgeRockApi.deleteUser(accessTokenForgeRock, forgeRockUserId), HttpURLConnection.HTTP_OK);
    }
    assertEquals(forgeRockApi.getForgeRockUserId(accessTokenForgeRock, searchFilter, userInfo).length(), 0);// Make sure it is deleted
  }

  @And("new {string} is generated using CIP API")
  public void newIsGeneratedUsingCIPAPI(String id) {
    User user = JsonManager.getUser(id, base.isAndroid());
    assertNotNull(user);
    base.setUser(user);

    CIP_API cipApi = new CIP_API();
    String accessToken = cipApi.getToken();
    assertNotNull(accessToken);
    String externalId = cipApi.createUser(accessToken, user.getEmail());

    ForgeRock_API forgeRockApi = new ForgeRock_API();
    String forgeRockUserId = forgeRockApi.getForgeRockUserId(forgeRockApi.getToken(), "frIndexedString1", externalId);
    assertFalse(forgeRockUserId.isEmpty()); //Check the user exists on Forgerock side
  }
}

