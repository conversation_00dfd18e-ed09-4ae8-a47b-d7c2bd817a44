package pulse.stepDefinitions.logins;

import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.registrationmfe.*;
import pulse.stepDefinitions.AppSteps;
import pulse.stepDefinitions.profile.ProfileSteps;
import pulse.utils.BaseUtils;
import utils.Gmail_API;
import utils.JsonManager;
import utils.Pojo.User;
import utils.StringUtilities;
import utils.Twillio_API;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.testng.Assert.*;

public class LoginSteps extends BaseUtils {
  private final LoginPage loginPage;
  private final CIP_LoginPage cipLoginPage;
  private final SignInPage signInPage;
  private final GetStartedPage getStartedPage;
  private final EnterYourCodePage enterYourCodePage;
  private final VerifyYourNumberPage verifyYourNumberPage;
  private final VerificationPage verificationPage;
  private final PrivacyStatementUpdatePage privacyStatementUpdatePage;
  private final CheckYourEmailPage checkYourEmailPage;
  private final SignInGooglePage signInGooglePage;
  private final JustOneMoreStepPage justOneMoreStepPage;
  BaseUtils base;
  private String verificationCode;
  private String emailVerificationCode;

  public LoginSteps(BaseUtils base) {
    super();
    this.base = base;
    this.cipLoginPage = new CIP_LoginPage(base.getDriver());
    this.loginPage = new LoginPage(base.getDriver());
    this.signInPage = new SignInPage(base.getDriver());
    this.getStartedPage = new GetStartedPage(base.getDriver());
    this.enterYourCodePage = new EnterYourCodePage(base.getDriver());
    this.verifyYourNumberPage = new VerifyYourNumberPage(base.getDriver());
    this.verificationPage = new VerificationPage(base.getDriver());
    this.privacyStatementUpdatePage = new PrivacyStatementUpdatePage(base.getDriver());
    this.checkYourEmailPage = new CheckYourEmailPage(base.getDriver());
    this.signInGooglePage = new SignInGooglePage(base.getDriver());
    this.justOneMoreStepPage = new JustOneMoreStepPage(base.getDriver());
  }


  @When("clicking the email button")
  public void clickingTheEmailButton() {
    loginPage.clickTheEmailButton();
  }

  @And("the email button is displayed")
  public void theEmailButtonIsDisplayed() {
    loginPage.emailButtonIsDisplayed();
  }

  @And("select the next button")
  public void selectTheNextButton() {
    loginPage.selectTheNextButton();
  }

  @And("select the Log in button")
  public void selectTheLoginButton() {
    loginPage.clickTheLoginButton();
  }

  @When("entering the {string} phone number and confirm")
  public void phoneNumberAndConfirm(String id) {
    User user = JsonManager.getUser(id, base.isAndroid());
    base.setUser(user);
    assertNotNull(user);

    loginPage.selectPhonePrefix(user.getPhonePrefix());
    assertEquals(loginPage.getPhonePrefix(), user.getPhonePrefix());

    loginPage.enterPhoneNumber(user.getPhoneNumber());
    assertEquals(loginPage.getPhoneNumber(), user.getPhoneNumber());

    loginPage.selectTheNextButton();
  }

  @When("I enter verification code and confirm")
  public void iEnterVerificationCodeAndConfirm() {

    loginPage.enterVerificationCode(verificationCode);
    assertEquals(loginPage.getVerificationCode(), verificationCode);

    loginPage.clickTheLoginButton();
  }

  @When("I enter a valid email {string}")
  public void iEnterAValidEmail(String arg0) {
    loginPage.enterValidEmail(arg0);
  }

  @When("I enter a valid password {string}")
  public void iEnterAValidPassword(String arg0) {
    loginPage.enterValidPassword(arg0);
  }

  @And("I tap the next button")
  public void clickTheNextButton() {
    verificationPage.clickNextButton();
  }

  @Then("the enter email text field is displayed")
  public void theEmailTextFieldIsDisplayed() {
    loginPage.emailTextFieldIsDisplayed();
  }

  @Then("the password text field is displayed")
  public void thePasswordFieldIsDisplayed() {
    loginPage.passwordFieldIsDisplayed();
  }

  @Then("the verify your identity screen is displayed")
  public void verifyYourIdentityScreenIsDisplayed() {
    verificationPage.verifyYourIdentityTextIsDisplayed();
  }

  @Then("the CIP Login page is displayed")
  public void iAmOnCIPLoginPage() {
    cipLoginPage.waitForPageLoaded(60);
    if (base.isAndroid()) {
      String defaultBrowser = cipLoginPage.getDefaultBrowser();
      if (!defaultBrowser.equals("chrome")) {
        String app = "com.android.settings/.Settings";
        assertEquals(cipLoginPage.startApplication(app), app);
        new AppSteps(base).setChromeAsDefaultBrowser();
        cipLoginPage.closeBrowser();
        ProfileSteps profileSteps = new ProfileSteps(base);
        profileSteps.iAmOnTheProfilePage();
        profileSteps.clickingTheLoginButton();
      }
      assertTrue(cipLoginPage.isChromeOpened());
    } else {
      assertTrue(cipLoginPage.isSafariOpened());
    }
    assertTrue(cipLoginPage.switchContext("WEBVIEW"));
  }

  @When("entering the {string} mobile number")
  public void iEnterMobileNumberById(String id) {
    User user = JsonManager.getUser(id, base.isAndroid());
    base.setUser(user);
    assertNotNull(user, "User " + id + " cannot be found");
    cipLoginPage.enterMobileOrEmail(user.getPhonePrefix() + user.getPhoneNumber());
    cipLoginPage.hideKeyboard();
  }

  public void iEnterMobileNumber(String phoneNumber) {
    cipLoginPage.enterMobileOrEmail(phoneNumber);
    cipLoginPage.hideKeyboard();
  }

  @When("entering the {string} email address on Login page")
  public void iEnterEmailById(String id) {
    User user = JsonManager.getUser(id, base.isAndroid());
    base.setUser(user);
    assertNotNull(user);
    cipLoginPage.enterMobileOrEmail(user.getEmail());
  }

  public void enterEmail(String email) {
    cipLoginPage.enterMobileOrEmail(email);
  }

  @Then("clicking Continue on the Log in page")
  public void iCanClickContinueOnLogInPage() {
    cipLoginPage.clickContinue();
  }

  @When("verification code can be entered on Verify your number page")
  public void iEnterVerificationCodeOnVerifyYourNumberPage() {
    verifyYourNumberPage.enterVerificationCode(verificationCode);
    assertEquals(verifyYourNumberPage.getVerificationCode(), verificationCode);
  }

  @Then("clicking Continue on the Verify your number page")
  public void iCanClickContinueOnVerifyYourNumberPage() {
    verifyYourNumberPage.clickContinue();
  }

  @Then("the SMS verification code is received")
  public void iReceiveCIPVerificationCode() {
    User user = base.getUser();
    String phoneNumber = user.getPhoneNumber();
    String phonePrefix = user.getPhonePrefix();

    String message = Twillio_API.read_SMS_Message(phonePrefix + "-" + phoneNumber);
    assertFalse(message.isEmpty(), "SMS verification code not received");

    verificationCode = StringUtilities.getStringAfter(message, "Your bp verification code is: ");
    assertFalse(verificationCode.isEmpty());
  }

  @When("User clicks Create an account")
  public void iClickCreateAnAccount() {
    cipLoginPage.clickCreateAnAccount();
  }

  @Then("Privacy policy update page may open")
  public void privacyPolicyUpdatePageOpens() {
    if (privacyStatementUpdatePage.isPageDisplayed()) {
      assertEquals(privacyStatementUpdatePage.getTextPageTitle(), "Privacy policy update");
    }
  }

  @When("Selecting Agree privacy policy and Continue might be selected")
  public void selectingAgreePrivacyPolicyAndContinue() {
    if (privacyStatementUpdatePage.isPageDisplayed()) {
//      privacyStatementUpdatePage.clickAgree();
      privacyStatementUpdatePage.clickContinue();
    }
  }

  @Then("Sign in page is displayed")
  public void signInPageIsDisplayed() {
    assertEquals(signInPage.getTextPageTitle(), "Sign in");
  }

  @When("user clicks Register now")
  public void userClicksRegisterNow() {
    signInPage.clickRegisterNow();
  }

  @Then("Get started page is displayed")
  public void getStartedPageIsDisplayed() {
    assertTrue(cipLoginPage.switchContext("WEBVIEW"));
    assertEquals(getStartedPage.getTextPageTitle(), "Get started");
  }

  @When("entering the phone number on Get started page")
  public void entersHisPhoneNumber() {
    User user = base.getUser();
    String phoneNumber = user.getPhoneNumber();
    if (!getStartedPage.getTextPhonePrefix().equals(user.getPhonePrefix())) {
      getStartedPage.clickPhonePrefix();
      assertTrue(cipLoginPage.switchContext("NATIVE_APP"));
      getStartedPage.selectPicklistValue(user.getPhonePrefix());
      assertTrue(cipLoginPage.switchContext("WEBVIEW"));
    }
    assertEquals(getStartedPage.getTextPhonePrefix(), user.getPhonePrefix());
    getStartedPage.enterPhoneNumber(phoneNumber);
    assertEquals(getStartedPage.getPhoneNumber(), phoneNumber);
  }

  @And("confirming the phone number on Get started page")
  public void confirmingThePhoneNumberOnGetStartedPage() {
    getStartedPage.confirmPhoneNumber();
  }

  @Then("Enter your code page is displayed")
  public void enterYourCodeIsDisplayed() {
    assertEquals(enterYourCodePage.getTextPageTitle(), "Enter your code");
  }

  @When("user enters SMS code")
  public void userEntersSMSCode() {
    enterYourCodePage.enterVerificationCode(verificationCode);
    assertEquals(enterYourCodePage.getVerificationCode(), verificationCode);
    enterYourCodePage.clickContinue();
    assertTrue(enterYourCodePage.switchContext("NATIVE_APP"));
  }

  @Then("Check your email page opens")
  public void checkYourEmailPageOpens() {
    assertEquals(checkYourEmailPage.getTextPageTitle(), "Check your email");
    assertTrue(checkYourEmailPage.getTextEmailAt().contains(base.getUser().getEmail()));
  }

  @And("the verification code is sent to the users email")
  public void theVerificationCodeIsSentToTheUsersEmail() {
    String email = StringUtilities.removeEmailPlusAddressing(base.getUser().getEmail(), "+", "@");
    Gmail_API gmailAPI = new Gmail_API(email);
    String query = "from:BP Accounts Team <<EMAIL>> to:" + base.getUser().getEmail();
    String emailBody = gmailAPI.readMail(query);
    assertNotNull(emailBody, "The email cannot be found: " + query);

    String regex = "(?<!#)\\b\\d{6}\\b"; // find 6 digits number where the # is not in front of it
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(emailBody);
    if (matcher.find()) {
      emailVerificationCode = matcher.group();
    }
    assertEquals(emailVerificationCode.length(), 6, "Code cannot be found in " + emailBody);
  }

  @When("user enters the verification code")
  public void userEntersTheVerificationCode() {
    checkYourEmailPage.enterVerificationCode(emailVerificationCode);
    assertEquals(checkYourEmailPage.getVerificationCode(), emailVerificationCode);
  }

  @And("user clicks Continue on Check your email page")
  public void userClicksContinueOnCheckYourEmailPage() {
    checkYourEmailPage.clickContinue();
    assertTrue(checkYourEmailPage.switchContext("NATIVE_APP"));
  }

  @When("clicking the Google icon")
  public void clickingTheGoogleIcon() {
    getStartedPage.clickGoogleIcon();
    assertTrue(checkYourEmailPage.switchContext("NATIVE_APP"));
    getStartedPage.clickSkipCurrentUser();
    assertTrue(checkYourEmailPage.switchContext("WEBVIEW"));
  }

  @Then("Sign in with Google page opens")
  public void signInWithGooglePageOpens() {
    assertEquals(signInGooglePage.getTextPageTitle(), "Sign in");
  }

  @When("entering and confirming an email address on Sign in with Google page")
  public void enteringEmailAddressOnSignInWithGooglePage() {
    String email = base.getUser().getEmail();
    signInGooglePage.enterEmail(email);
    assertEquals(signInGooglePage.getTextEmail(), email);
    signInGooglePage.clickNextEmail();
  }

  @And("entering and confirming the password on Sign in with Google page")
  public void enteringPasswordOnSignInWithGooglePage() {
    String brand = getBrand();
    String password;
    if (brand.equals("bpUS")) {
      password = System.getenv("GMAIL_PASSWORD_US_1");
    } else {
      password = System.getenv("GMAIL_PASSWORD1");
    }
    signInGooglePage.enterPassword(password);
    signInGooglePage.clickNextPassword();
  }

  @And("clicking Continue on Just one more step page")
  public void clickingContinueOnJustOneMoreStepPage() {
    if (justOneMoreStepPage.isPageDisplayed()) {
      justOneMoreStepPage.clickContinue();
    }
  }
}
