package pulse.stepDefinitions;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import pages.profile.DeleteAccountPage;
import pages.profile.PersonalInformationPage;
import pulse.utils.BaseUtils;
import static org.testng.Assert.*;

public class PersonalInformationSteps extends BaseUtils {
    BaseUtils base;

  PersonalInformationPage personalInformationPage;
  DeleteAccountPage deleteAccountPage;

  public PersonalInformationSteps(BaseUtils base) {
    super();
    this.base = base;
    this.personalInformationPage = new PersonalInformationPage(base.getDriver());
    this.deleteAccountPage = new DeleteAccountPage(base.getDriver());
  }

  @Then("Personal information page is displayed")
  public void aGuestIsOnTheProfilePage() {
    assertEquals(personalInformationPage.getPageTitle(), "Personal information");
  }

  @When("user clicks Delete account")
  public void userClicksDeleteAccount() {
    personalInformationPage.clickDeleteAccount();
  }

  @When("clicking the Edit button")
  public void clickingEditButton() {
    personalInformationPage.clickEditButton();
  }

  @Then("the Edit mode is active")
  public void theEditModeIsActive() {
    assertTrue(personalInformationPage.isEditModeActive());
  }
}

