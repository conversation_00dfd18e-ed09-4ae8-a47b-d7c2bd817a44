Feature: Logged in user Profile Screen Offer Tests

  @wip
  Scenario: The New pill is displayed for a UK user

    Given a UK wallet user "<User>" logs in
    And the Profile page is displayed
    When clicking on the offers and promotions button
    Then the offers and promotions screen is displayed
    When going back to the profile screen
    Then the offers and promotions new widget is not displayed

  @wip
  Scenario: The offers and promotions button is not displayed for a NL user

    Given a NL wallet user "<User>" logs in
    When the Profile page is displayed
    Then the offers and promotions button is not displayed

  @wip
  Scenario: The offers and promotions button is not displayed for a ES user

    Given a NL wallet user "<User>" logs in
    When the Profile page is displayed
    Then the offers and promotions button is not displayed
