Feature: Logged in user Profile Screen Offer Tests

  @wip
  Scenario Outline: The coming soon screen is displayed for "<User>"

    Given a user "<User>" logs in using mobile number
    And user taps on the Profile icon

    When clicking on the offers and promotions button
    Then Offers and promotions screen is displayed

    When screen contains Active Offer list
    Then offer with "<Active Code>" contains "<Credit>" left and expiry date with "<Expire date>"

    When the user clicks on Used and expired offers
    Then Used and expired offers screen is displayed
    Then there is expired offer containing offer with code "<Expired Code>"

    When going back to the Offers and promotions screen
    Then Offers and promotions screen is displayed

    Examples:
      | User                   | Active Code  | Credit | Expire date | Expired Code |
      | user_Expired_Wallet_UK | CCC10240E8D8 | £10.00 | 21/6/2025   | CCC1024D6148 |

