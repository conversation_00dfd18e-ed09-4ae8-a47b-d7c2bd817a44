Feature: Facebook Login

  #test will be disabled due to new screen captcha
  @wip
  Scenario Outline: A user can login via Facebook

    Given the application starts up

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Log in button
    Then the CIP Login page is displayed

    When tapping the Facebook icon
    Then the log in with Facebook page is displayed

    When the Facebook log in to your account page is displayed
    And user "<User>" enters Facebook credentials
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

  @bpGlobal
    Examples:
      | User       |
      | user_05_NL |

  @bpUS
    Examples:
      | User       |
      | user_18_US |


