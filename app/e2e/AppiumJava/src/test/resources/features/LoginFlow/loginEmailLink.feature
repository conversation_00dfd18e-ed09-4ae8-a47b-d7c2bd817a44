Feature: CIP Gmail login

  @wip
  Scenario Outline: A user can login by entering email address

    Given the application starts up

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Log in button
    Then the CIP Login page is displayed

    When entering the "<User>" email address on Login page
    And clicking Continue on the Log in page
    Then Check your email page opens
    And the verification link is sent to the users email

    When user enters the verification link to the url input
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    @bpGlobal
    Examples:
      | User       |
      | user_05_UK |

    @bpUS
    Examples:
      | User       |
      | user_20_US |


















