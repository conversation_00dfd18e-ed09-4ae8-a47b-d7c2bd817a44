Feature: Login Screen is displayed for a Guest from the map details

  @Android
  Scenario Outline: Selecting Login From The Map Details As A Guest Opens The Login Screen

    Given the application starts up
    When entering the search "<Location>"
    Then the list of results is returned in the dropdown menu

    When "<Location>" from the area "<Area>" is selected

    When the nearby charging locations menu is displayed
    Then the user can open the nearby charging locations menu

    When selecting the first option
    Then the charge point details screen is displayed

    When selecting the Log in or Create an account button
    Then the CIP Login page is displayed

    @bpGlobal
    Examples:
      | Location | Area |
      | London   | UK   |

    @bpUS
    Examples:
      | Location  | Area    |
      | San Lucas | CA, USA |
