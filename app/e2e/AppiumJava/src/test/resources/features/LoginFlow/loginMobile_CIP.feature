Feature: CIP mobile login

  @wip
  Scenario Outline: A User Can Login Via a Twilio Phone Number
    Given the application starts up

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Log in button
    Then the CIP Login page is displayed

    When entering the "<User>" mobile number
    And clicking Continue on the Log in page
    Then Enter your code page is displayed
    Then the SMS verification code is received

    When user enters SMS code

    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    @bpGlobal
    Examples:
      | User       |
      | user_12_NL |

    @bpUS
    Examples:
      | User       |
      | user_21_US |





















