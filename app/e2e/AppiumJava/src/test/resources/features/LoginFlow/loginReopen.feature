Feature: Restart app

  @wip
  Scenario Outline: A User Stays Logged In Once The App Is Restarted

    When a user "<User>" logs in using mobile number
    And user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    When user restarts the app
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    @bpGlobal
    Examples:
      | User       |
      | user_11_ES |

    @bpUS
    Examples:
      | User       |
      | user_21_US |



















