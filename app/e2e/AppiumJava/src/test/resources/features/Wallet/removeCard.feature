Feature: Remove the default card and the final card

  @wip
  Scenario Outline: A user can remove all the cards from the wallet

    Given a user "<User>" logs in using email address
    When user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    Given there are at least <NumCards> credit cards in a wallet
    When user removes non default card
    Then the card can be removed from the wallet

    When user removes the default card
    Then the second card will automatically become the default

    When user removes all the cards
    Then the linked cards list is an empty

    @bpGlobal
    Examples:
      | User       | NumCards |
      | user_08_NL | 3        |

    @bpUS
    Examples:
      | User       | NumCards |
      | user_04_US | 3        |
