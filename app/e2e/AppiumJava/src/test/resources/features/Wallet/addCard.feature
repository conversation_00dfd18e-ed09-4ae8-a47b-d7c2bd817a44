Feature: Add credit card

  @wip
  Scenario Outline: A user can add the card to the wallet

    When a user "<User>" logs in using email address
    When user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    When user removes the card from the wallet
    And user taps Add a new credit or debit card

    Then Add your payment method page is displayed
    When entering valid credit card number
    And entering the Expiry month
    And entering the Expiry year
    And entering the CVV
    And entering the Card holders name
    And entering the Street and house name
    And entering the Town or city name
    And entering the Postal or zip code
    And selecting the Counry
    And selecting the phone prefix
    And entering the Mobile number
    And entering the Email address
    And clicking Continue to card authorisation
    Then the Secure Checkout page is displayed

    When user enters the SMS code for 3DS card and submits
    Then the Payment methods page is displayed
    And the added card is in the Linked cards list

    @bpGlobal
    Examples:
      | User       |
      | user_02_NL |

    @bpUS
    Examples:
      | User       |
      | user_02_US |
















