Feature: Change default card

  @wip
  Scenario Outline: A user can change the default card in a wallet

    When a user "<User>" logs in using email address
    When user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    Given there are at least <NumCards> credit cards in a wallet
    When clicking Default card button
    Then Default card page is displayed
    When user select none default card
    And user clicks Save on Default card page

    Then the Payment methods page is displayed
    And the new default card is set and listed as first

    @bpGlobal
    Examples:
      | User       | NumCards |
      | user_02_ES | 2        |

    @bpUS
    Examples:
      | User       | NumCards |
      | user_03_US | 2        |


















