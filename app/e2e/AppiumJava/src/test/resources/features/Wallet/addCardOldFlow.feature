Feature: Add credit card

  @wip
  Scenario: A user can add the card to the wallet

    When a user "user_02_NL" logs in using email address
    When user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    When user removes the card from the wallet
    And user taps Add a new credit or debit card
    Then the Card information page is displayed

    When user fills in valid card information add save
    Then the Billing Address page is displayed

    When user enters billing address information and save
    Then the Contact information page is displayed

    When user enters contact information and save
    Then the Processing card detail modal is displayed

    When user clicks Continue on Processing modal
    Then the Secure Checkout page is displayed

    When user enters the SMS code for 3DS card and submits
    Then the Payment methods page is displayed
    And the added card is in the Linked cards list


















