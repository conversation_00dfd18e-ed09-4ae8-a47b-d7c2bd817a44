Feature: Charge from the map

  @wip
  Scenario Outline: Search for location on the map, select and open CP details and start the charging

    Given a user "<User>" logs in using email address

    When there is a charge in progress stop it
    When entering the search "<Location>"
    Then the list of results is returned in the dropdown menu

    When "<Location>" from the area "<Country>" is selected
    Then clusters on the map are displayed

    When selecting the cluster
    Then charge points "<Type>" are displayed on the map

    When selecting charge point on the map
    Then the charge point details screen is displayed

    When clicking Charge now button
    Then Select a connector page is displayed

    When selecting available connector and starting the charge
    Then Connect your vehicle page is displayed

    When confirming the vehicle is connected
    Then Charging page is displayed with the actual status

    When button Stop charging is clicked
    And Stop charging is confirmed in the modal
    Then Charge summary page is displayed

    When user clicks Done on Charge summary page
    Then the map screen is displayed

    @bpGlobal
    Examples:
      | User       | Country     | Location  | Type |
      | user_06_NL | Netherlands | Amsterdam | blue |

    @bpUS
    Examples:
      | User       | Country | Location | Type |
      | user_13_US | US      | Houston  | blue |






