Feature: Adding the card to start the charging

  @wip
  Scenario Outline: A user without any card in the wallet should be able to add the card and successfully charge

    When a user "<User>" logs in using email address

    When there is a charge in progress stop it
    When user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    When user removes all the cards
    And user goes back from Payment methods page
    And user clicks on Charge icon
    Then the Charge page is displayed

    When entering the charger ID
    And selecting Find charger
    Then Select a connector page is displayed

    When there is no payment card add some

    When selecting available connector
    And clicking the button Charge using Connector...
    Then Connect your vehicle page is displayed

    When confirming the vehicle is connected
    Then Charging page is displayed with the actual status

    When button Stop charging is clicked
    And Stop charging is confirmed in the modal
    Then Charge summary page is displayed

    When user clicks Done on Charge summary page
    Then the map screen is displayed

    @bpGlobal
    Examples:
      | User       |
      | user_09_NL |

    @bpUS @Android
    Examples:
      | User       |
      | user_10_US |

    @bpUS @iOS
    Examples:
      | User       |
      | user_23_US |





















