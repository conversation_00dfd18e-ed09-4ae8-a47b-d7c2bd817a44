Feature: Charging

  @wip
  Scenario Outline: Losing and regaining connection before starting a charge and during the charge

    When a user "<User>" logs in using mobile number

    When there is a charge in progress stop it
    When user clicks on Charge icon
    Then the Charge page is displayed

    When entering the charger ID
    And selecting Find charger
    Then Select a connector page is displayed

    When selecting available connector
    And disabling the internet connection
    Then the Charge using Connector... button will be disabled

    When enabling the internet connection
    Then the Charge using Connector... button will be enabled

    When clicking the button Charge using Connector...
    Then Connect your vehicle page is displayed

    When confirming the vehicle is connected
    Then Charging page is displayed with the actual status

    When disabling the internet connection
    Then No charging information is displayed

    When enabling the internet connection
    Then charging status is displayed

    When button Stop charging is clicked
    And Stop charging is confirmed in the modal
    Then Charge summary page is displayed

    @bpGlobal
    Examples:
      | User       |
      | user_11_NL |

    @bpUS
    Examples:
      | User       |
      | user_14_US |























