Feature: Charging

  @wip
  Scenario Outline: Shutting the app and re-opening the app during a charge session

    When a user "<User>" logs in using email address

    When there is a charge in progress stop it
    When user clicks on Charge icon
    Then the Charge page is displayed

    When entering the charger ID
    And selecting Find charger
    Then Select a connector page is displayed

    When selecting available connector
    And clicking the button Charge using Connector...
    Then Connect your vehicle page is displayed

    When confirming the vehicle is connected
    Then Charging page is displayed with the actual status

    When user restarts the app
    And user clicks on Charge icon
    Then Charging page is displayed with the actual status

    When button Stop charging is clicked
    And Stop charging is confirmed in the modal
    Then Charge summary page is displayed

    When user clicks Done on Charge summary page
    And user taps on the Profile icon
    Then the Profile page is displayed

    When Recent transactions are selected
    Then the Recent transactions page is displayed

    When the latest transaction is selected
    Then Transaction details are displayed

    When Download VAT invoice button is clicked
    Then the invoice can be downloaded and verified

    @bpGlobal
    Examples:
      | User       |
      | user_10_NL |

    @bpUS
    Examples:
      | User       |
      | user_12_US |




















