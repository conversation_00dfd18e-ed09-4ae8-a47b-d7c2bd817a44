Feature: Adding the card to the wallet by link

  @wip
  Scenario Outline: A user should be able to add the card by link

    When a user "<User>" logs in using email address

    When user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    When user removes the card from the wallet

    And user goes back from Payment methods page
    And user clicks on Charge icon
    Then the Charge page is displayed

    When entering the charger ID
    And selecting Find charger
    Then Select a connector page is displayed
    When clicking Add payment method link
    Then Add your payment card page is displayed
    And user clicks Add card detail
    And user can add a new card to the wallet
    Then Select a connector page is displayed whith added card

    @bpGlobal
    Examples:
      | User       |
      | user_09_NL |




















