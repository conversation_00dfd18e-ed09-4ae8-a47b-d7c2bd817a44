Feature: Invalid Charge Search

  @Android
  Scenario Outline: An error appears when entering an invalid charge point value

    Given the application starts up
    When user clicks on Charge icon
    Then the Serial Search page is displayed

    When entering an invalid CP number "<CPNum>" into the input field
    And selecting the Find Charger button
    Then a no matching charge error is displayed

    @bpGlobal
    Examples:
      | CPNum     |
      | 742383473 |

    @bpUS
    Examples:
      | CPNum     |
      | 742383473 |


