Feature: Charging

  @wip
  Scenario Outline: A user can start charge by entering charger ID

    Given a user "<User>" logs in using email address

    When there is a charge in progress stop it
    When user clicks on Charge icon
    Then the Charge page is displayed

    When entering the charger ID
    And selecting Find charger
    Then Select a connector page is displayed

    When selecting available connector
    And clicking the button Charge using Connector...
    Then Connect your vehicle page is displayed

    When confirming the vehicle is connected
    Then Charging page is displayed with the actual status

    When button Stop charging is clicked
    And Stop charging is confirmed in the modal
    Then Charge summary page is displayed

    When user clicks Done on Charge summary page
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed

    When Recent transactions are selected
    Then the Recent transactions page is displayed

    When the latest transaction is selected
    Then Transaction details are displayed

    When Download VAT invoice button is clicked
    Then the invoice can be downloaded and verified

    @bpGlobal
    Examples:
      | User       |
      | user_07_NL |

    @bpUS @Android
    Examples:
      | User       |
      | user_11_US |

    @bpUS @iOS
    Examples:
      | User       |
      | user_15_US |






















