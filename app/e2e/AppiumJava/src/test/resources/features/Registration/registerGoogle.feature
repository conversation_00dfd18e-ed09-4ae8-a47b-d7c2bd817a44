Feature: Register with Google account

  @wip
  Scenario Outline: A user is able to register to bp pulse app using Google account

    Given the application starts up
    And the "<User>" is deleted from the ForgeRock database based on email

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Create an account button
    Then Get started page is displayed

    When clicking the Google icon
    Then Sign in with Google page opens

    When entering and confirming an email address on Sign in with Google page
    And entering and confirming the password on Sign in with Google page
    Then customise the bp pulse app page opens

    When user selects the country on Customise page
    And user checks I have read terms and conditions
    And user clicks Jump right in
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    @bpGlobal
    Examples:
      | User       |
      | user_07_UK |

    @bpUS @Android
    Examples:
      | User       |
      | user_17_US |

























