Feature: Register future

  @wip
  Scenario Outline: A user is able to register to bp pulse app

    Given the application starts up
    And the "<User>" is deleted from the ForgeRock database based on phone number

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Create an account button
    Then Get started page is displayed

    When entering the phone number on Get started page
    And confirming the phone number on Get started page
    Then the SMS verification code is received
    And Enter your code page is displayed

    When user enters SMS code
    Then Add email address page opens

    When user enters email on Add email address page
    Then verify your email page opens
    And the verification link is sent to the users email

    When user enters the verification link to the url input
    Then customise the bp pulse app page opens

    When user selects the country on Customise page
    And user enters the first name on Customise page
    And user enters the last name on Customise page
    And user checks I have read terms and conditions
    And user clicks Jump right in
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    @bpGlobal
    Examples:
      | User        |
      | user_12_UK  |

    @bpUS @Android
    Examples:
      | User       |
      | user_22_US |

























