Feature: Register future

  @wip
  Scenario Outline: This is help feature to simplyfy user creation for automation tests without phone number

    Given the application starts up
    And the "<User>" is deleted from the ForgeRock database based on email
    And new "<User>" is generated using CIP API

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Log in button
    Then the CIP Login page is displayed

    When entering the "<User>" email address on Login page
    And clicking Continue on the Log in page
    Then Check your email page opens
    And the verification code is sent to the users email

    When user enters the verification code
    And user clicks Continue on Check your email page
    And Selecting Agree privacy policy and Continue might be selected
    Then customise the bp pulse app page opens

    When user selects the country on Customise page
    And user enters the first name on Customise page
    And user enters the last name on Customise page
    And user checks I have read terms and conditions
    And user clicks Jump right in
    Then the map screen is displayed

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page


    Examples:
      | User       |
      | user_??_UK |

























