Feature: Guest Profile Screen Tests

  @Android @iOS @bpGlobal @bpUS
  Scenario: The Log In To Create An Account button is displayed

    Given the application starts up
    When a Guest taps on the Profile icon
    Then a Guest user is on the Profile page
    And the log in to create an account button is displayed

  @Android @bpGlobal @bpUS
  Scenario: The Settings Screen is displayed for a Guest user

    Given the application starts up
    When a Guest taps on the Profile icon
    Then a Guest user is on the Profile page
    When the Settings button is clicked
    Then the default english language settings screen is displayed

  @Android @bpGlobal
  Scenario Outline: A guest user is able to change the app language to <Country>

    Given the application starts up
    When a Guest taps on the Profile icon
    Then a Guest user is on the Profile page

    When the Settings button is clicked
    Then the default english language settings screen is displayed

    When clicking the selected default English language option
    Then the language screen is displayed

    When selecting the chosen language
      | <Country> |
    Then the update language button is displayed

    When clicking the update language button
    Then the language is updated on the settings screen to:
      | <Country> |

    Examples:
      | Country |
      | NL      |
      | ES      |
#      | US      |
