Feature: Guest Help Screen Tests

  @Android @iOS
  Scenario Outline: A <Country> guest user is able to select their region help screen

    Given the application starts up
    When a Guest taps the Help icon
    And they select I'm charging in
      | <Country> |
    And clicking the show help page button
    Then the countries help screen is displayed
      | <Country> |

    @bpGlobal
    Examples:
      | Country |
      | UK      |
      | NL      |
      | ES      |
      | US      |

    @bpUS
    Examples:
      | Country |
      | US      |

