Feature: Using the filter page as a guest

  @Android
  Scenario Outline: Guest user should be able to filter CPs on the map

    Given the application starts up

    When entering the search "<Location>"
    Then the list of results is returned in the dropdown menu

    When "<Location>" from the area "<Country>" is selected
    Then clusters on the map are displayed

    When clicking the quick filter button on the Map page
    Then the Filter page opens

    When clearing all the filters
    And selecting connector type "<Connector type>"
    And saving the connector type filter
    Then the All connector buttons starts with "<Connector type>"

    Then charge points "<CP Type>" are displayed on the map

    When selecting charge point on the map
    Then the charge point details screen is displayed
    And the charge point should have "<Connector type>" listed

    @bpGlobal
    Examples:
      | Country | Location | Connector type | CP Type |
      | UK      | Leeds    | CCS2           | blue    |

    @bpUS
    Examples:
      | Country | Location | Connector type | CP Type |
      | US      | Houston  | CCS2           | blue    |









