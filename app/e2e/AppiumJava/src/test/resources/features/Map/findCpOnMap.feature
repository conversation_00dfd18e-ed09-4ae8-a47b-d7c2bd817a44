Feature: Find a CP on the map in other country as a UK user

  @wip
  Scenario Outline: UK user should be able to find CP's in other countries
    Given a user "user_04_UK" logs in using email address

    When entering the search "<Location>"
    Then the list of results is returned in the dropdown menu

    When "<Location>" from the area "<Country>" is selected
    Then clusters on the map are displayed

    When selecting the cluster
    Then charge points "<Type>" are displayed on the map

    When selecting charge point on the map
    Then the charge point details screen is displayed

    When opening Details tab on charge point details screen
    Then the "<Customer care phone>" number should be from that country
    Then closing the Details tab on charge point details screen

    @bpGlobal
    Examples:
      | Country     | Location  | Type  | Customer care phone |
#      | UK          | Oxford    | blue  | +44                 |
      | Netherlands | Amsterdam | blue  | +31                 |
#      | Spain       | Madrid    | white | +34                 |









