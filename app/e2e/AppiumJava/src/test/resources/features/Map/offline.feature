Feature: Check your internet connection

  @wip
  Scenario Outline: Check your internet connection is displayed when offline

    Given a user "<User>" logs in using mobile number

    When user taps on the Profile icon
    Then the Profile page is displayed

    When Recent transactions are selected
    Then the Recent transactions page is displayed

    When disabling the internet connection
    Then Check your internet connection is displayed on Recent transactions page

    When enabling the internet connection
    Then Check your internet connection is not displayed on Recent transactions page

    When user goes back from the Recent transaction details page
    And user taps on the Profile icon
    Then the Profile page is displayed

    When user taps the Payment methods
    Then the Payment methods page is displayed

    When disabling the internet connection
    Then Check your internet connection is displayed on the Payment methods page

    When enabling the internet connection
    Then Check your internet connection is not displayed on the Payment methods page

    Given there are at least <NumCards> credit cards in a wallet
    When clicking Default card button
    Then Default card page is displayed

    When disabling the internet connection
    Then the Save button on Default card page is disabled

    When enabling the internet connection
    Then the Save button on De<PERSON>ult card page is enabled

    When user clicks Cancel on Default card page
    Then the Payment methods page is displayed

    When user goes back from Payment methods page
    Then the Profile page is displayed

    When clicking the Personal information
    Then Personal information page is displayed

    When user clicks Delete account
#    Then Before you go page is displayed
#
#    When clicking Delete my account and date
#    Then  Select optionPage page is displayed
#
#    When selecting option "Do both - delete bp pulse data and close all accounts" on Select option page
#    When clicking Select on Select optionPage page
    Then  Delete account page is displayed

    When user selects Delete my account
    Then Confirm deletion page is displayed

    When selecting the reason of deletion "<Reason>"
    And selecting I understand the consequences
    Then the Confirm account deletion button will be enabled

    When disabling the internet connection
    Then the Confirm account deletion button will be disabled

    When enabling the internet connection
    Then the Confirm account deletion button will be enabled

    @bpGlobal
    Examples:
      | User       | NumCards | Reason |
      | user_03_NL | 1        | Other  |

    @bpUS
    Examples:
      | User       | NumCards | Reason |
      | user_09_US | 1        | Other  |






















