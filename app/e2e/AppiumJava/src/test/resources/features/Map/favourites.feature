Feature: Mark charge point as a favourite

  @wip
  Scenario Outline: Open the charge point detail and mark it as a favourite

    Given a user "<User>" logs in using email address

    When clicking the quick filter button on the Map page
    Then the Filter page opens

    When clearing all the filters
    And saving the connector type filter
    Then the map screen is displayed

    When entering the search "<Location>"
    Then the list of results is returned in the dropdown menu

    When "<Location>" from the area "<Country>" is selected
    Then charge points "<CP Type>" are displayed on the map

    When selecting charge point on the map
    Then the charge point details screen is displayed

    When marking the charge point "<CP Type>" as a favourite
    Then the favourite symbol is assigned to the charge point "<CP Type>"

    When closing the charge point details page
    Then the map screen is displayed

    When selecting the Favourites button
    Then only "<CP Type>" favourite charge points are displayed on the map

    @wip
    Examples:
      | User       | Country | Location | CP Type |
      | user_01_UK | UK      | Leeds    | blue    |

    @bpUS
    Examples:
      | User       | Country | Location  | CP Type |
      | user_08_US | CA, USA | San Lucas | blue    |







