Feature: Delete account

  @wip
  Scenario Outline: A user should be able to delete an account

    Given the application starts up
    And the "<User>" is deleted and re-registered using their email

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Personal information
    Then Personal information page is displayed

    When user clicks Delete account
#    Then Before you go page is displayed

#    When clicking Delete my account and date
#    Then  Select optionPage page is displayed
#
#    When selecting option "Do both - delete bp pulse data and close all accounts" on Select option page
#    When clicking Select on Select optionPage page
    Then  Delete account page is displayed

    When user selects Delete my account
    Then Confirm deletion page is displayed

    When selecting the reason of deletion "Other"
    And selecting I understand the consequences
    Then the Confirm account deletion button will be enabled

    When the Confirm account deletion button is clicked
    Then Request received page is displayed
    And the Account Deletion Requested email is received

#    When clicking Done on Request received page
#    Then Personal information page is displayed

    @wip
    Examples:
      | User       |
      | user_01_NL |
      | user_08_UK |


























