Feature: Edit personal info

  @wip
  Scenario Outline: A user can personal data

    Given a user "<User>" logs in using email address

    When user taps on the Profile icon
    Then the Profile page is displayed

    When clicking the Personal information
    Then Personal information page is displayed

    When clicking the Edit button
    Then the Edit mode is active

    Examples:
      | User       |
      | user_13_NL |
