Feature: Subs status set in profile MF<PERSON>

  @wip
  Scenario Outline: Activate Membership

#    Given the application starts up
    And the "<User>" is deleted and re-registered using their email

    When user taps on the Profile icon
    And the user name is displayed on the Profile page

    #Create subscription
    When the subscribe and save option is displayed
    Then the user clicks on subscribe and save

    When Subscribe and save page is displayed
    Then the button Set up my subscription now is displayed

    When the user clicks on button Set up my subscription now
    Then confirm page is displayed

    When Confirm and authorise button is displayed
    Then the user click on button Confirm and authorise

    #Verify subscription
    When My subscription option is displayed

    Examples:
      | User       |
      | user_14_UK |




