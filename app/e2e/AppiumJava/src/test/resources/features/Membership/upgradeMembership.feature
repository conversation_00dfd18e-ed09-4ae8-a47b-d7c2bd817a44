Feature: Upgrade Membership

  @wip
  Scenario: The Upgrade Membership Option is Available For A UK User
    Given a user "user_02_UK" logs in using email address
    And user taps on the Profile icon
    When the Profile page is displayed
    And is shown as logged in
    Then the subscribe and save option is displayed

  @wip
  Scenario: The Upgrade Membership Option is not Available For A NL User
    Given a user "user_03_UK" logs in using email address
    And user taps on the Profile icon
    When the Profile page is displayed
    And is shown as logged in
    Then the subscribe and save option is not displayed
