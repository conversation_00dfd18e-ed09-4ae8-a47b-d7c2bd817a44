Feature: Check Marketing preferences

  @wip
  Scenario: Updating marketing preference as a registered user
    Given a user "user_06_UK" logs in using email address

    When user taps on the Profile icon
    Then the Profile page is displayed

    When the Settings button is clicked
    Then the Settings page is displayed

    When the Marketing preferences are clicked
    Then the Marketing preferences page is displayed

    When preference "Email" is selected
    And Save preference is clicked
    Then Marketing preferences "Email" gets saved



