Feature: Subs user - Upgrade membership should be excluded from the Profile tab options

@wip
Scenario Outline: Membership exclusion

    Given the application starts up
    And the "<User>" is deleted and re-registered using their email

    When user taps on the Profile icon
    Then the Profile page is displayed
    And the user name is displayed on the Profile page

    #verify that subscription is not active
    When user taps on the Profile icon
    Then the Profile page is displayed
    Then My subscription option is not displayed

    Examples:
      | User       |
      | user_13_UK |





