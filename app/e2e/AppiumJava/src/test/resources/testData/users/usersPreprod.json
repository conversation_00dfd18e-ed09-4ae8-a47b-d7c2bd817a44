[{"id": "user_01_NL_Android", "firstName": "Preprod Reg", "lastName": "Nltesttwo", "country": "Netherlands", "countryCode": "NL", "street": "abc", "houseNumber": "abc", "postalCode": "abc", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+31", "phoneNumber": "97077777777", "email": "<EMAIL>", "cards": []}, {"id": "user_01_NL_iOS", "firstName": "Preprod", "lastName": "Nether", "country": "Netherlands", "countryCode": "NL", "street": "abc", "houseNumber": "abc", "postalCode": "abc", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+31", "phoneNumber": "97077777777", "email": "<EMAIL>", "cards": []}, {"id": "user_01_UK_Android", "firstName": "king", "lastName": "UkTwo", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7893933270", "email": "<EMAIL>", "cards": []}, {"id": "user_02_ES_Android", "firstName": "ervgwdacrgw", "lastName": "cgdaa", "country": "Spain", "countryCode": "ES", "street": "Str", "houseNumber": "123", "postalCode": "2345", "town": "Madrid", "timeZone": "Europe/Madrid", "phonePrefix": "+44", "phoneNumber": "7822026170", "email": "<EMAIL>", "cards": []}, {"id": "user_02_NL_Android", "firstName": "saecfasfz", "lastName": "sxaxcsxs`x", "country": "Netherlands", "countryCode": "NL", "street": "Some street", "houseNumber": "abc", "postalCode": "1234", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+31", "phoneNumber": "97077777777", "email": "<EMAIL>", "cards": []}, {"id": "user_02_UK_Android", "firstName": "kinger", "lastName": "finger", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7822027923", "email": "<EMAIL>", "cards": []}, {"id": "user_03_NL_Android", "firstName": "Offline Preprod", "lastName": "Testnlthree", "country": "Netherlands", "countryCode": "NL", "street": "Some street", "houseNumber": "abc", "postalCode": "1234", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7723181240", "email": "<EMAIL>", "chargerId": "NL*BPE*E0CI2A*01", "cards": []}, {"id": "user_03_UK_Android", "firstName": "<PERSON><PERSON>", "lastName": "Dingy", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7782352337", "email": "<EMAIL>", "cards": []}, {"id": "user_04_UK_iOS", "firstName": "English", "lastName": "King", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7883316465", "email": "<EMAIL>", "cards": []}, {"id": "user_05_NL_Android", "firstName": "dssfwccss", "lastName": "sdfadsfa", "country": "Netherlands", "countryCode": "NL", "street": "Sint Jansstraat", "houseNumber": "11", "postalCode": "1012 HG", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7441360050", "email": "<EMAIL>", "cards": []}, {"id": "user_06_NL_Android", "firstName": "Charger", "lastName": "<PERSON><PERSON><PERSON>", "country": "Netherlands", "countryCode": "NL", "street": "Sint Jansstraat", "houseNumber": "11", "postalCode": "1012 HG", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7360545360", "latitude": 52.415, "longitude": 4.911588946161477, "email": "<EMAIL>", "cards": []}, {"id": "user_07_NL_Android", "firstName": "agregdawcrf", "lastName": "Test", "country": "Netherlands", "countryCode": "NL", "street": "Sint Jansstraat", "houseNumber": "11", "postalCode": "1012 HG", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7701412377", "email": "<EMAIL>", "chargerId": "NL*BPE*EINF1501*01", "cards": []}, {"id": "user_07_NL_iOS", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "country": "Netherlands", "countryCode": "NL", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "xxxxx", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7380327236", "email": "<EMAIL>", "chargerId": "NL*BPE*E0CI2Y*07", "cards": []}, {"id": "user_07_UK_Android", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7782475315", "email": "<EMAIL>", "chargerId": "BPPULSEUK01", "cards": []}, {"id": "user_08_NL_Android", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Tester", "country": "Netherlands", "countryCode": "NL", "street": "Sint Jansstraat", "houseNumber": "11", "postalCode": "1012 HG", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+31", "phoneNumber": "97077777777", "email": "<EMAIL>", "cards": []}, {"id": "user_09_NL_Android", "firstName": "gwfavfs", "lastName": "cdgcscgvcagz", "country": "Netherlands", "countryCode": "NL", "street": "My st", "houseNumber": "123", "postalCode": "3456", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+31", "phoneNumber": "97077777777", "email": "<EMAIL>", "chargerId": "NL*BPE*EINF1501*01", "cards": []}, {"id": "user_10_NL_Android", "firstName": "<PERSON>", "lastName": " <PERSON>", "country": "Netherlands", "countryCode": "NL", "street": "abc", "houseNumber": "abc", "postalCode": "abc", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+31", "phoneNumber": "97077777777", "email": "<EMAIL>", "chargerId": "NL*BPE*EINF1501*01", "cards": []}, {"id": "user_11_ES_Android", "firstName": "spanich pp", "lastName": "seder", "country": "Spain", "countryCode": "ES", "street": "wdfds", "houseNumber": "dssd", "postalCode": "sdcsd", "town": "Madrid", "timeZone": "Europe/Madrid", "phonePrefix": "+44", "phoneNumber": "7700148973", "email": "<EMAIL>", "chargerId": "NL*BPE*EINF1501*01", "cards": []}, {"id": "user_11_NL_Android", "firstName": "Map Preprod", "lastName": "<PERSON><PERSON><PERSON>", "country": "Netherlands", "countryCode": "NL", "street": "Sint Jansstraat", "houseNumber": "11", "postalCode": "1012 HG", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7402983351", "email": "<EMAIL>", "chargerId": "NL*BPE*EINF1501*01", "cards": []}, {"id": "user_12_NL_Android", "firstName": "Amster", "lastName": "Nether", "country": "Netherlands", "countryCode": "NL", "street": "Sint Jansstraat", "houseNumber": "11", "postalCode": "1012 HG", "town": "Amsterdam", "timeZone": "Europe/Amsterdam", "phonePrefix": "+44", "phoneNumber": "7397394190", "email": "<EMAIL>", "cards": []}, {"id": "user_12_UK_Android", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7480549153", "email": "<EMAIL>", "cards": []}, {"id": "user_12_UK_iOS", "firstName": "Jabo", "lastName": "Babo", "country": "United Kingdom", "countryCode": "GB", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7822028517", "email": "<EMAIL>", "cards": []}, {"id": "user_15_UK_Android", "firstName": "<PERSON><PERSON>", "lastName": "Land", "country": "United Kingdom", "street": "<PERSON>", "houseNumber": "20", "postalCode": "WC1N 2DL", "town": "London", "timeZone": "Europe/London", "phonePrefix": "+44", "phoneNumber": "7777777777", "email": "<EMAIL>", "cards": []}]