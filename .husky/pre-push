# Branch prefix validation, to prevent branching mess in ADO
branch_name=$(git rev-parse --abbrev-ref HEAD)
pattern="^(feat|chore|ci|fix|release|docs|refactor|test)/"

if [[ ! $branch_name =~ $pattern ]]; then
  echo "Error: Branch name must start with one of 'feat/', 'chore/', 'ci/', 'fix/', 'release/', 'docs/', 'refactor/', 'test/'"
  echo "Your branch name is: $branch_name"
  exit 1
fi

yarn pre-push