diff --git a/lib/load-changelog-config.js b/lib/load-changelog-config.js
index d078a3330b1ce7159aff02fec0aaf7e3790f0620..d7f9d25a40825e727f53fdfb6293223562057551 100644
--- a/lib/load-changelog-config.js
+++ b/lib/load-changelog-config.js
@@ -27,7 +27,7 @@ export default async ({ preset, config, parserOpts, writerOpts, presetConfig },
       (await importFrom.silent(__dirname, presetPackage)) || (await importFrom(cwd, presetPackage))
     )(presetConfig);
   } else if (config) {
-    loadedConfig = await ((await importFrom.silent(__dirname, config)) || (await importFrom(cwd, config)))();
+    loadedConfig = await ((await importFrom.silent(__dirname, config)) || (await importFrom(cwd, config)));
   } else {
     loadedConfig = await conventionalChangelogAngular();
   }
