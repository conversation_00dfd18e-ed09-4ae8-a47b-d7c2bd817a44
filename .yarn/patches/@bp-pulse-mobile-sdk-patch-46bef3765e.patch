diff --git a/cli/installPods.js b/cli/installPods.js
index d25568e487549fa9885d7ca61f1d031726b8576f..5f1f247227ad0aedccb20708c861fcd5968aa48c 100644
--- a/cli/installPods.js
+++ b/cli/installPods.js
@@ -1,11 +1,11 @@
 const path = require('path');
 const { execSync } = require('child_process');
-
+const podInstall = require.resolve('pod-install');
 const ROOT = path.resolve(__dirname, '../');
 
 const installPods = () => {
   console.log('Installing pods');
-  execSync('node ../../../node_modules/pod-install/build/index.js --quiet', {
+  execSync(`node ${podInstall} --quiet`, {
     stdio: 'inherit',
     cwd: ROOT,
   });
diff --git a/metro.config.js b/metro.config.js
index bb16237070ea0773d7d709f8f66b1da178a4d289..d4a69f919ddf327ba0eb4dfcc65cc203793994f3 100644
--- a/metro.config.js
+++ b/metro.config.js
@@ -5,6 +5,7 @@ const exclusionList = require('metro-config/src/defaults/exclusionList');
 const pak = require('../../../package.json');
 
 const root = path.resolve(__dirname, '../../../');
+const monorepoRoot = path.resolve(root, '../../');
 
 const modules = Object.keys({
   ...pak.dependencies,
@@ -16,7 +17,7 @@ module.exports = (async () => {
   } = await getDefaultConfig();
   return {
     projectRoot: __dirname,
-    watchFolders: [root],
+    watchFolders: [root, monorepoRoot],
 
     // We need to make sure that only one version is loaded for peerDependencies
     // So we block them at the root, and alias them to the versions in example's node_modules
